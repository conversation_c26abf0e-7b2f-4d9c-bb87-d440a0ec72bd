#!/usr/bin/env python3
"""
Obsidian Vault to Qdrant Embedding Script
Embeds all markdown files from an Obsidian vault into a local Qdrant instance
using Ollama's Qwen3 8B Q4 embedding model.
"""

import os
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional
import frontmatter
import requests
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
import argparse
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ObsidianVaultEmbedder:
    def __init__(self, 
                 vault_path: str,
                 qdrant_host: str = "localhost",
                 qdrant_port: int = 6333,
                 ollama_host: str = "localhost",
                 ollama_port: int = 11434,
                 collection_name: str = "obsidian_vault",
                 embedding_model: str = "qwen2.5:7b"):
        
        self.vault_path = Path(vault_path)
        self.qdrant_client = QdrantClient(host=qdrant_host, port=qdrant_port)
        self.ollama_base_url = f"http://{ollama_host}:{ollama_port}"
        self.collection_name = collection_name
        self.embedding_model = embedding_model
        
        # Verify connections
        self._verify_connections()
        
    def _verify_connections(self):
        """Verify connections to Qdrant and Ollama"""
        try:
            # Test Qdrant connection
            collections = self.qdrant_client.get_collections()
            logger.info(f"✓ Connected to Qdrant at {self.qdrant_client._client.host}:{self.qdrant_client._client.port}")
            
            # Test Ollama connection
            response = requests.get(f"{self.ollama_base_url}/api/tags")
            if response.status_code == 200:
                logger.info(f"✓ Connected to Ollama at {self.ollama_base_url}")
            else:
                raise Exception(f"Ollama connection failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Connection verification failed: {e}")
            raise
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama"""
        try:
            response = requests.post(
                f"{self.ollama_base_url}/api/embeddings",
                json={
                    "model": self.embedding_model,
                    "prompt": text
                }
            )
            response.raise_for_status()
            return response.json()["embedding"]
        except Exception as e:
            logger.error(f"Failed to get embedding: {e}")
            raise
    
    def extract_markdown_content(self, file_path: Path) -> Dict[str, Any]:
        """Extract content and metadata from markdown file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                post = frontmatter.load(f)
            
            # Extract metadata
            metadata = dict(post.metadata)
            content = post.content
            
            # Add file information
            metadata.update({
                'file_path': str(file_path.relative_to(self.vault_path)),
                'file_name': file_path.name,
                'file_size': file_path.stat().st_size,
                'modified_time': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                'content_length': len(content),
                'has_frontmatter': bool(post.metadata)
            })
            
            return {
                'content': content,
                'metadata': metadata,
                'title': metadata.get('title', file_path.stem),
                'full_text': f"{metadata.get('title', file_path.stem)}\n\n{content}"
            }
            
        except Exception as e:
            logger.error(f"Failed to process {file_path}: {e}")
            return None
    
    def create_collection(self, vector_size: int = 1024):
        """Create or recreate the Qdrant collection"""
        try:
            # Delete existing collection if it exists
            try:
                self.qdrant_client.delete_collection(self.collection_name)
                logger.info(f"Deleted existing collection: {self.collection_name}")
            except:
                pass
            
            # Create new collection
            self.qdrant_client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
            )
            logger.info(f"Created collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"Failed to create collection: {e}")
            raise
    
    def generate_point_id(self, file_path: str) -> str:
        """Generate a consistent point ID from file path"""
        return hashlib.md5(file_path.encode()).hexdigest()
    
    def embed_vault(self, chunk_size: int = 1000, overlap: int = 200):
        """Embed all markdown files in the vault"""
        markdown_files = list(self.vault_path.rglob("*.md"))
        logger.info(f"Found {len(markdown_files)} markdown files")
        
        if not markdown_files:
            logger.warning("No markdown files found in vault")
            return
        
        # Get embedding dimension from first file
        test_embedding = self.get_embedding("test")
        vector_size = len(test_embedding)
        logger.info(f"Embedding dimension: {vector_size}")
        
        # Create collection
        self.create_collection(vector_size)
        
        points = []
        processed_count = 0
        
        for file_path in markdown_files:
            try:
                # Skip hidden files and directories
                if any(part.startswith('.') for part in file_path.parts):
                    continue
                
                logger.info(f"Processing: {file_path.relative_to(self.vault_path)}")
                
                # Extract content and metadata
                doc_data = self.extract_markdown_content(file_path)
                if not doc_data:
                    continue
                
                # Create chunks if content is large
                content = doc_data['full_text']
                if len(content) <= chunk_size:
                    chunks = [content]
                else:
                    chunks = self._create_chunks(content, chunk_size, overlap)
                
                # Process each chunk
                for i, chunk in enumerate(chunks):
                    if not chunk.strip():
                        continue
                    
                    # Generate embedding
                    embedding = self.get_embedding(chunk)
                    
                    # Create point ID
                    point_id = self.generate_point_id(f"{file_path}_{i}")
                    
                    # Prepare metadata
                    chunk_metadata = doc_data['metadata'].copy()
                    chunk_metadata.update({
                        'chunk_index': i,
                        'total_chunks': len(chunks),
                        'chunk_content': chunk[:500] + "..." if len(chunk) > 500 else chunk,
                        'embedding_model': self.embedding_model,
                        'embedded_at': datetime.now().isoformat()
                    })
                    
                    # Create point
                    point = PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=chunk_metadata
                    )
                    points.append(point)
                
                processed_count += 1
                
                # Batch upload every 50 files
                if len(points) >= 50:
                    self._upload_points(points)
                    points = []
                
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {e}")
                continue
        
        # Upload remaining points
        if points:
            self._upload_points(points)
        
        logger.info(f"Successfully processed {processed_count} files")
        
        # Print collection info
        collection_info = self.qdrant_client.get_collection(self.collection_name)
        logger.info(f"Collection '{self.collection_name}' now contains {collection_info.points_count} points")
    
    def _create_chunks(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Split text into overlapping chunks"""
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            
            # Try to break at sentence or paragraph boundaries
            if end < len(text):
                last_period = chunk.rfind('.')
                last_newline = chunk.rfind('\n\n')
                break_point = max(last_period, last_newline)
                
                if break_point > start + chunk_size // 2:
                    chunk = text[start:break_point + 1]
                    end = break_point + 1
            
            chunks.append(chunk.strip())
            start = end - overlap
            
            if start >= len(text):
                break
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    def _upload_points(self, points: List[PointStruct]):
        """Upload points to Qdrant"""
        try:
            self.qdrant_client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            logger.info(f"Uploaded {len(points)} points to Qdrant")
        except Exception as e:
            logger.error(f"Failed to upload points: {e}")
            raise
    
    def search(self, query: str, limit: int = 5) -> List[Dict]:
        """Search the embedded vault"""
        try:
            query_embedding = self.get_embedding(query)
            
            results = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit,
                with_payload=True
            )
            
            return [
                {
                    'score': result.score,
                    'file_path': result.payload.get('file_path'),
                    'title': result.payload.get('title'),
                    'content': result.payload.get('chunk_content'),
                    'metadata': result.payload
                }
                for result in results
            ]
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='Embed Obsidian vault into Qdrant')
    parser.add_argument('vault_path', help='Path to Obsidian vault')
    parser.add_argument('--qdrant-host', default='localhost', help='Qdrant host')
    parser.add_argument('--qdrant-port', type=int, default=6333, help='Qdrant port')
    parser.add_argument('--ollama-host', default='localhost', help='Ollama host')
    parser.add_argument('--ollama-port', type=int, default=11434, help='Ollama port')
    parser.add_argument('--collection', default='obsidian_vault', help='Qdrant collection name')
    parser.add_argument('--model', default='qwen2.5:7b', help='Ollama embedding model')
    parser.add_argument('--search', help='Search query to test after embedding')
    
    args = parser.parse_args()
    
    # Create embedder
    embedder = ObsidianVaultEmbedder(
        vault_path=args.vault_path,
        qdrant_host=args.qdrant_host,
        qdrant_port=args.qdrant_port,
        ollama_host=args.ollama_host,
        ollama_port=args.ollama_port,
        collection_name=args.collection,
        embedding_model=args.model
    )
    
    # Embed vault
    embedder.embed_vault()
    
    # Test search if query provided
    if args.search:
        logger.info(f"Testing search with query: '{args.search}'")
        results = embedder.search(args.search)
        
        print(f"\nSearch Results for '{args.search}':")
        print("=" * 50)
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['title']} (Score: {result['score']:.3f})")
            print(f"   File: {result['file_path']}")
            print(f"   Content: {result['content'][:200]}...")
            print()

if __name__ == "__main__":
    main()
