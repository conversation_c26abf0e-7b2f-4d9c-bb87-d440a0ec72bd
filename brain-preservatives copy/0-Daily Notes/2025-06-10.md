---
creation date: 2025-04-21 23:12
modification date: Monday 21st April 2025 23:12:09
type: daily
date: 2025-04-21
day_of_week: Monday
week: 2025-W17
month: 2025-04
tags:
  - daily
  - 2025-04
mood: 
energy_level: ""
weather: ""
location: ""
---

# 2025-04-21 - Monday

<< [[2025-04-20]] | [[2025-04-22]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- Calling Qcat Process:
	- Ring 1300 753 432
	- to  get through the menu to review of gov decision services:
		-  4 -> 3 -> 2-> | Wait for prompt to press a button: **press 0** -> 
		  -> after 1 minute, the telephone prompter will eventually ask you if you want a call back. press 1 to Take this opportunity !

## Tasks
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- I quickly skimmed over my #todoist inbox last night, cleaning up a small number of tasks and assigning them to the correct projects. These tasks seem to be caught up here and will go unseen on the church office computer's todoist account. I need to make sure the correct project is assigned using the # character (#CRUCA). 
	- This way, all tasks should be presentable and ready for printing as a report.
	- They will be seen to when i have time. It cannot be put off though, as i identified the previous point, it is a crucial task which will see an improvement to the state of the #todoist data, potentially improving actionability with better quality data for easier recall.
	- I have recently identified how crucial memory is as a resource. Lack of memory grinds everything to an absolute halt (such as cognition). Education depends on it.
		- Comparing memory to other faculties of intellect, It seems to stand out as the most important to preserve up to a certain point where diminishing returns applies at a much more significant rate than the rest too. the mind, like RAM or a hard disk, must exist and perform at a reasonable rate in order to be functional, albeit useful.
- By not forming a structure in my habits, things have been out of shape (of-course). Things are overwhelming, unmanageable.
	- It is far too difficult to plan things. For instance, Last night when i was going through my #todoist, and scheduling new tasks, I often feel like the time i'm scheduling is a complete and utter guess... which does not feel right at all. Todoist has a great calendar function that should be used, and people seem to get that right, however i just put it on at a time that i'm usually awake in the morning, and in early afternoon and nothing more.
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 
		-  %%^ Apathy at best, but at the same time, perfectionate, which is why certain tasks get too much attention at the expense of others. #ADHD definition? What is attention? what's its substance. How does one control it? no really, at the electrochemical level. A choice being made to focus inwards rather than outwards?  A strong impulse to withdraw, focusing on the inner world rather than that of the barren, harsh external world. That's all our ancestors once knew, but now, that's completly flipped on its head. Withdrawing is not all we 444know in this 'connected' world. %%
			- #improvements #organisation  This is probably the most important improvement to make, which will solve the planning problems and the long pauses in thinking when #tasks should be scheduled, saving lots of time and an enourmou potential in mitigating periods of extreme stress. 
			  ***solutions?***
				-    `Pint out a calendar for quick referal so task times can be more effectively considered`

## Notes
<!-- Any other notes or information -->
## #QCAT Directions June - #July
- Should receive email soon with further #correspondance
	- to be scheduled for #2025-07-22 #2025-04 a of now

---
---

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-04-21)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on 2025-04-21
```

## Upcoming Tasks
```tasks
not done
due after 2025-04-21
due before 2025-04-28
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-04-21 Meeting|Create New Meeting]]
- [[2025-04-21 Task|Create New Task]]
- [[2025-04-21 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-04|Monthly Overview]]
- [[2025-W17|Weekly Overview]]
- [[Tasks]]
- [[Home]]