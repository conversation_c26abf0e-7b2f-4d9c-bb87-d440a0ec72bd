---
creation date: 2025-04-21 23:12
modification date: Monday 21st April 2025 23:12:09
type: daily
date: 2025-04-21
day_of_week: Monday
week: 2025-W17
month: 2025-04
tags:
  - daily
  - 2025-04
mood: ""
energy_level: ""
weather: ""
location: ""
TQ_explain: false
TQ_extra_instructions: 
TQ_short_mode: 
TQ_show_backlink: 
TQ_show_cancelled_date: 
TQ_show_created_date: 
TQ_show_depends_on: 
TQ_show_done_date: 
TQ_show_due_date: 
TQ_show_edit_button: 
TQ_show_id: 
TQ_show_on_completion: 
TQ_show_postpone_button: 
TQ_show_priority: 
TQ_show_recurrence_rule: 
TQ_show_scheduled_date: 
TQ_show_start_date: true
TQ_show_tags: 
TQ_show_task_count: 
TQ_show_tree: 
TQ_show_urgency:
---

# 2025-04-21 - Monday

<< [[2025-04-20]] | [[2025-04-22]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- $500 transferred from #kirsty to #card to top up the card account today 12/6

## Tasks
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
	$0/ month
  **Raycast**Free, forever¸
	
	Core features, including:Clipboard History, Quicklinks, Calculator, Snippets, Emoji Picker, Window Management + many more...
	
	Raycast AI50 Free Messages. Try any Pro model. Or use your own API key (BYOK).
	
	Raycast Notes5 Free Notes
	Raycast for iOS
	
	Thousands of extensions
	
	Custom Extensions
	
	Developer Tooling
	
	Download
	
	**Raycast Pro**AI at your fingertips
	
	$10/ month
	
	Everything in Free
	
	Raycast AI, including:
	
	- Raycast Ray-1, Ray-1 mini
	- OpenAI GPT-4.1 mini, GPT-4.1 nano, GPT-4o mini, o4-mini, o3-mini
	- Anthropic Claude 3.5 Haiku
	- Perplexity Sonar, Sonar Reasoning, Sonar Reasoning Pro
	- Mistral Nemo, Mistral Small 3, Codestral
	- Google Gemini 2.5 Flash, Gemini 2.0 Flash, Gemini 2.0 Flash Thinking
	- xAI Grok-3 Mini Beta
	
	Compare all models
	
	Cloud SyncSync Snippets, Notes, Chats with other Macs, and iOS
	
	Translator
	
	Unlimited Raycast Notes
	
	Unlimited Clipboard History
	
	Custom Window Management
	
	Custom Themes
	
	Start Free Trial
	
	**Pro + Advanced AI**The most intelligent models
	
	$20
	
	/ month
	
	Everything in Pro
	
	Advanced AI Models, including:
	- OpenAI GPT-4.1, GPT-4, GPT-4 Turbo, GPT-4o, o3, o1
	- Anthropic Claude 3.5 Sonnet, Claude 3.7 Sonnet, Claude 3.7 Sonnet (Reasoning), Claude 3 Opus, Claude 4 Sonnet, Claude 4 Opus, Claude 4 Sonnet (Reasoning), Claude 4 Opus (Reasoning)
	- Perplexity Sonar Pro
	- Mistral Large, Mistral Medium
	- Google Gemini 2.5 Pro
	- xAI Grok-3 Beta, Grok-2
	
	Compare all models
	
	Subscribe to Pro + Advanced AI
- [ ] 
## Journal
<!-- How was your day? What happened? What did you learn? -->
- Used the raycast pro free trial

## Notes
<!-- Any other notes or information -->
### Finance Meeting 
- Finance Meeting was held today following poor May documentation filing performance. I took some notes on the meeting, which listed some things I need to do to improve, aswell as understanding the requirements of the auditor, accountant and treasurer:
	- **Data required for Finance Documentation Gathering:** Susan (or the current accountant) requires data fields to enter into xero for compliance and record keeping of EVERY transaction:
		- Date, Name, price with the cents exactly
		- as soon as comes in, print out invoice, and put into red (outgoing) or black (incoming) folder, ready to pay/invoice on payment day (TT: Transactions Thursday)
		- will then get entererd into xero from date name(item/category) and 
	- **Receipt preferences:** A ppoint raised from feedback from the Chaplaincy payments:  need to have official receipts, meaning receipts given from an organisation after payments should be used over the st George payment submission receipt. Do not just use the st George payment reciepts every time, ait until an official receipt from the payee comes.. if they provide one. St. George payment receipts are to be used only as a last resort.
	- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-04-21)
SORT time ASC
```

## Today's Tasks
```tasks
not done
due on 2025-04-21
```

## Upcoming Tasks
```tasks
not done
due after 2025-04-21
due before 2025-04-28
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-04-21 Meeting|Create New Meeting]]
- [[2025-04-21 Task|Create New Task]]
- [[2025-04-21 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-04|Monthly Overview]]
- [[2025-W17|Weekly Overview]]
- [[Tasks]]
- [[Home]]
