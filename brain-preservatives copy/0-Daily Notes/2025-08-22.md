---
creation_date: 2025-08-22 02:14
modification_date: Friday 22nd August 2025 02:14:04
type: daily
status: active
priority: medium
area_category: Personal
owner: Jordan
tags:
  - daily
  - 2025-08
date: 2025-08-22
day_of_week: Friday
week: 2025-W34
month: 2025-08
mood: ""
energy_level: 
weather: ""
location: ""
related_projects: 
related_areas: 
related_resources: 
related_people:
  - "[[Jordan]]"
task_priority: medium
task_context: admin
---

# 2025-08-22 - Friday

<< [[2025-08-21]] | [[2025-08-23]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=brain-preservatives&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-
**Mrs Princess Pacey Login Details and Passwords** 

**RHIANNON’S student ID TORRENS:**
_A00153248_
  
**RHIANNON’S TFN:** 
*********
**JORDA<PERSON>’S TFN:**
********* 
  
**RHIANNON’S USI**: 
BZXK9T22BC 

  
**MICROSOFT 365:**
**Username:** <EMAIL>
**Password:** MrsPacey2024 
  
**PHOEBE’S SAMSUNG:**
**Samsung / google account:** 

<EMAIL> 
Stitch1602
**Phone pin:** 060396
  
**PHOEBE’S LIFE360:**
**Email:** <EMAIL>
**Password:** BrownEyedGirl16!
  
**RHIANNON’S LIFE360:** 
**Email:** <EMAIL>
**Password:** PrincessPaacey2024! 
  
**DEPARTMENT OF MAIN ROADS:** 
**REGO REF NUMBER:** **************
**BILLER CODE:** 48173 
  
**OPTUS:**
**Email:** <EMAIL>
**Password:** Anakyn2k22 
  
**AGL:**
**Email:** <EMAIL>
**Password:** GreenEyedGirl16!
[https://www.agl.com.au/help-support/billing-payments/pay-your-bill](https://www.agl.com.au/help-support/billing-payments/pay-your-bill)
  

**MYOB LOGIN & RECOVERY CODE:** 
**Username:** Rhiannon Pacey
**Password:** GreenEyedGirl16! 
**RECOVERY CODE:** 5MZT1AEJK65EKS6TK7Z3SBLY

**REALESTATE.COM**
**Username:** <EMAIL>
**Password: *****no password - a unique identification code is sent to email*** 

---

Fortnightly Income: 1,143.94 + 1,098.5 ‎ = 2,242.44 


## Tasks for Today
#todo #tasks #outstanding
### 📋 Carried Forward Tasks
> **50 tasks from 19 days** - Click to expand/collapse

> [!info]- 📋 Carried Forward Tasks (50 total)
> 
> #### 🔥 Recent Tasks (Last 7 Days)
> 
> **From 2025-08-19** (3 days ago) - 2 tasks:
> 
> - [ ] On thursday, pay for daisydisk to manage disk space [[DaisyDisk, the most popular disk space analyzer]]
> - [ ] Watch "Foundation" a new scifi tv series (2021-?)
> 
> #### 📚 Older Tasks (41 tasks from 14 days)
> 
> **From 2025-07-22** (31 days ago) - 2 tasks:
> 
> - [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
> - [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17
> 
> **From 2025-07-02** (51 days ago) - 1 tasks:
> 
> - [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder
> 
> **From 2025-06-25** (58 days ago) - 1 tasks:
> 
> - [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
> 
> **From 2025-06-24** (59 days ago) - 1 tasks:
> 
> - [ ] add the webssh key to dropbear
> 
> **From 2025-06-17** (66 days ago) - 5 tasks:
> 
> - [x] should i use setapp or buyout software licenses for mac?
> - [x] buy #software and add receipts to #tax:
> - [x] swish
> - [x] contexts
> - [ ] augment
> 
> **From 2025-06-13** (70 days ago) - 2 tasks:
> 
> - [ ] add all clippings to relevant sections in vault
> - [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
> 
> **From 2025-06-12** (71 days ago) - 2 tasks:
> 
> - [x] Consider if #raycast pro is worth purchasing
> - [x] Add the #raycast pro features to a reference in 3-resources
> 
> **From 2025-06-11** (72 days ago) - 1 tasks:
> 
> - [x] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
> 
> **From 2025-06-10** (73 days ago) - 1 tasks:
> 
> - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.
> 
> **From 2025-06-08** (75 days ago) - 10 tasks:
> 
> - [ ] (90/212) electricity ;
> - [ ] $212.00 due on the 06•06.25 - missed payment
> - [ ] optus;
> - [ ] • $220.40 due on the 12•06-25
> - [ ] fuel;
> - [ ] have already put in over $50.00 so far
> - [x] smokes;  [completion:: 2025-08-22]
> - [ ] license renewal7[[]()]()
> - [ ] groceries + %:
> - [ ] $ currently after :
> 
> **From 2025-06-03** (80 days ago) - 3 tasks:
> 
> - [x] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
> - [x] sort all daily notes data into vault properly
> - [ ] use augment ai trial 13 <NAME_EMAIL>
> 
> **From 2025-04-16** (128 days ago) - 5 tasks:
> 
> - [x] Migrate Obsidian vault to PARA structure
> - [x] Add proper frontmatter to existing notes
> - [x] Tag notes appropriately for better organization
> - [x] Start linking related notes together
> - [x] Create tables of contents for each section
> 
> **From 2025-04-15** (129 days ago) - 4 tasks:
> 
> - [x] Research PARA methodology for Obsidian vault organization
> - [x] Look into dataview plugin capabilities
> - [x] Explore templates for consistent note structure
> - [x] Plan vault reorganization
> 
> **From 2025-04-14** (130 days ago) - 3 tasks:
> 
> - [x] Review current Obsidian vault organization  [completion:: 2025-08-22]
> - [x] Research best practices for note organization
> - [ ] Look into productivity plugins for Obsidian
> 

### ✨ New Tasks for Today

- [ ] New Princess Email - <EMAIL>
- [ ] 
- [ ]

## CI/CD
<!-- Administrative tasks, correspondence, documentation -->
-

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Maintainence
<!-- Compliance, training, documentation -->
-

## Development/Updates
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-

## 🎯 Today's Focus
<!-- Show only the most important tasks -->

### 🔥 Must Do Today
```dataview

TASK
FROM "1-Projects" OR "2-Areas" OR "0-Daily Notes"
WHERE !completed
  AND task_priority = "urgent"
  AND (due <= date("2025-08-22") OR contains(tags, "#today"))
SORT file.path ASC
LIMIT 3

```



### ⚡ High Impact Work
```dataview

TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "high"
  AND (due <= date("2025-08-22") + dur(2 days) OR !due)
SORT due ASC
LIMIT 5

```



### 📋 If Time Permits
```dataview

TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "medium"
  AND estimated_time <= "30min"
SORT estimated_time ASC
LIMIT 4

```



## 🚀 Active Project Status
```dataview

TABLE WITHOUT ID
  file.link as "🚀 Project",
  choice(priority = "critical", "🔥", choice(priority = "high", "⚡", "📋")) as "P",
  completion_percentage + "%" as "Done",
  choice(deadline < date("2025-08-22"), "⚠️ OVERDUE", choice(deadline <= date("2025-08-22") + dur(7 days), "🔜 Soon", "📅 " + string(deadline))) as "Deadline"
FROM "1-Projects"
WHERE status = "active"
  AND (file.mtime >= date("2025-08-22") - dur(3 days)
   OR deadline <= date("2025-08-22") + dur(14 days)
   OR priority = "critical"
   OR priority = "high")
SORT choice(deadline < date("2025-08-22"), 1, choice(priority = "critical", 2, 3)) ASC
LIMIT 5

```



## 📅 Today's Meetings
```dataview

TABLE WITHOUT ID
  file.link as "📅 Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date("2025-08-22")
SORT time ASC

```



## ⚠️ Overdue & Urgent
```dataview

TABLE WITHOUT ID
  "⚠️" as "",
  file.link as "OVERDUE TASKS",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  due as "Was Due"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND due < date("2025-08-22")
SORT task_priority ASC, due ASC
LIMIT 8

```



## 🔄 Context-Based Quick Tasks
```dataview

TABLE WITHOUT ID
  choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄")) as "P",
  file.link as "QUICK WINS (15min)",
  choice(energy_required, energy_required, "medium") as "Energy"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND estimated_time = "15min"
SORT task_priority ASC, energy_required DESC
LIMIT 6

```



## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-08-22 Meeting|Create New Meeting]]
- [[2025-08-22 Task|Create New Task]]
- [[2025-08-22 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-08|Monthly Overview]]
- [[2025-W34|Weekly Overview]]
- [[Tasks]]
- [[Home]]
