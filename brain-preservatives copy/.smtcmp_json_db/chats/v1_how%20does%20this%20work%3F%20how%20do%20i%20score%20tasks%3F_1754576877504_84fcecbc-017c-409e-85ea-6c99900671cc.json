{"id": "84fcecbc-017c-409e-85ea-6c99900671cc", "title": "how does this work? how do i score tasks?", "messages": [{"role": "user", "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "how does this work? how do i score tasks?", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "\n\nhow does this work? how do i score tasks?\n\n"}], "id": "c1b23216-29df-4493-b351-1cacfd78df8c", "mentionables": [{"type": "current-file", "file": "0.2-Tasks/Smart Task Dashboard V2.md"}]}, {"role": "user", "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "how does this work? how do i score tasks?", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "\n\nhow does this work? how do i score tasks?\n\n"}], "id": "fca82769-135c-49b9-ae27-917cc0668a84", "mentionables": [{"type": "current-file", "file": "0.2-Tasks/Smart Task Dashboard V2.md"}]}, {"role": "assistant", "content": "Based on the provided file structure, you can use any of the following tags in your markdown files to set priority:\n\n- `priority_score` (number)\n- `tags` (string array) - e.g., `[\"website\", \"infrastructure\"]`\n- `completion_percentage` (number)\n- `estimated_hours` (number)\n- `deadline` (date)\n\nEach of these values contributes to the overall priority calculation:\n\n1. **Higher `priority_score`** indicates greater urgency and importance.\n2. **Nearer `deadline`** increases task urgency.\n3. **Relevant `tags`** (like \"website\", \"infrastructure\", etc.) can impact context-based prioritization.\n4. **Lower `estimated_hours` for high-scoring tasks** helps identify quick wins.\n\nFor example, in a markdown file:\n<smtcmp_block language=\"markdown\">\n```dataview\n---\npriority_score: 85\ntags: [\"website\", \"infrastructure\"]\ncompletion_percentage: 0\nestimated_hours: 3\ndeadline: 2024-12-31\n---\n```\n</smtcmp_block>\n\nThe system calculates and ranks tasks automatically. If you want to modify the scoring logic, update the dataview queries in `0.2-Tasks/Smart Task Dashboard V2.md`.", "id": "2d219866-0bcb-42eb-b893-ba990bdb4f01", "metadata": {"model": {"providerType": "ollama", "providerId": "ollama", "id": "devstral:apply", "model": "devstral:24b", "promptLevel": 1}, "usage": {"prompt_tokens": 3777, "completion_tokens": 284, "total_tokens": 4061}}}], "createdAt": 1754576691659, "updatedAt": 1754576877504, "schemaVersion": 1}