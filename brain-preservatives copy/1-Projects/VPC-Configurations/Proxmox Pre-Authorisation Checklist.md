
Pre-Installation Checklist:

1. Download Proxmox VE 8.2 ISO from another computer:
•  Go to: https://www.proxmox.com/en/downloads
•  Download: proxmox-ve_8.2-2.iso (or latest version)
•  Size: ~1.2GB
2. Create Bootable USB (from another computer):
•  Use <PERSON><PERSON> (recommended) or <PERSON> (Windows)
•  Select the Proxmox ISO
•  Select your USB drive (minimum 2GB)
•  Flash the image

Installation Process:

#### Phase 1: Boot and Install
1. Insert USB and reboot this server
2. Enter BIOS (usually F2, F12, DEL, or ESC during boot)
3. Set USB as first boot device
4. Boot from USB and you'll see Proxmox installer

#### Phase 2: Installation Settings
When the installer starts, configure:

```
1. Agreement → Accept EULA

2. Target Disk:
   - Select: /dev/sda (466GB) ← Install here
   - Filesystem: ZFS (RAID0) or ext4
   - Leave sdb & sdc for VM storage later

3. Location:
   - Country: Your country
   - Timezone: Your timezone
   - Keyboard: Your layout

4. Network Configuration:
   - Hostname: proxmox.local (or your preference)
   - IP Address: ***********/24 (keep current)
   - Gateway: ***********
   - DNS: *******

5. Admin Password:
   - Set a strong root password
   - Email: <EMAIL>
 ```
#### Phase 2.2 Installation Settings: Network Host Configuration
1. Use a Fully Qualified Domain Name (FQDN) hostname, such as the one I am using: `ursae.paceyspace.com`
	1.  (replace `ursae` with the actual server host name for the cluster naming configuration you want, it can be whatever you want to name it on the network)
2. create an A record from your DNS to the public IP of the server
3. **forward port 8006** on the router gateway to the server's internal network IP
#### Phase 3: Post-Installation

Once Proxmox boots:

1. Access Web Interface:
•  https://***********:8006
•  Username: root
•  Password: (what you set)
2. Initial Setup via Web UI:
```
   a. Update repositories (remove enterprise repo)
   b. Add storage for VMs (sdb & sdc)
   c. Configure network bridge
   d. Upload ISOs for VM
```
Storage Configuration Plan:
```
/dev/sda (466GB) → Proxmox OS + Local storage
/dev/sdb (932GB) → VM Storage (add as LVM or ZFS)
/dev/sdc (932GB) → Backup/Additional Storage
```
After Proxmox is Installed:

We'll create:
1. Ubuntu Server VM - For general services
2. LXC Container - For Portainer
3. Optional: Another VM for Enhance if needed

⚠️ IMPORTANT NOTES:

•  This WILL completely wipe your current system
•  All data will be lost (you're okay with this)
•  Network will be reconfigured (we'll keep same IP)
•  Cannot be undone without reinstalling

Ready to Proceed?

Do you have:
USB drive ready (2GB+)?
Access to another computer to create bootable USB?
Physical access to the server for installation?
About 30-45 minutes for the process?

Once you've created the bootable USB and are ready to proceed with the installation, boot from it and follow the steps above. After Proxmox is installed and you can access the web interface, let me know and I'll help you:

1. Configure storage pools
2. Set up your first VM
3. Install Portainer
4. Get everything running smoothly