---

# Vault Security Configuration for Production

## Overview

This guide covers securing your HashiCorp Vault installation for production use with multiple client deployments. Focus on budget-friendly security practices that don't compromise protection.

## Tags
#vault #security #production #authentication #policies #tls #encryption #yendorcats

---

## Authentication Methods

### 1. Token Authentication (Current Setup)

Your current implementation uses token-based authentication, which is simple but requires careful token management.

#### Creating Service Tokens

```bash
# Create a policy for the YendorCats application
vault policy write yendorcats-policy - << EOF
# Allow reading secrets for the application
path "secret/data/yendorcats/*" {
  capabilities = ["read"]
}

# Allow reading specific client secrets
path "secret/data/client*/yendorcats/*" {
  capabilities = ["read"]
}
EOF

# Create a token with the policy
vault token create -policy=yendorcats-policy -ttl=8760h -renewable=true
```

#### Token Renewal Script

```bash
#!/bin/bash
# vault-token-renewal.sh

VAULT_ADDR="http://localhost:8200"
VAULT_TOKEN_FILE="/secure/vault-token"

# Read current token
CURRENT_TOKEN=$(cat $VAULT_TOKEN_FILE)

# Renew token
NEW_TOKEN=$(vault token renew -format=json | jq -r '.auth.client_token')

if [ $? -eq 0 ]; then
    echo $NEW_TOKEN > $VAULT_TOKEN_FILE
    echo "Token renewed successfully"
    
    # Restart your application to pick up new token
    systemctl restart yendorcats-api
else
    echo "Token renewal failed"
    exit 1
fi
```

### 2. AppRole Authentication (Recommended for Production)

More secure than tokens for automated systems:

```bash
# Enable AppRole auth method
vault auth enable approle

# Create role for YendorCats
vault write auth/approle/role/yendorcats \
    token_policies="yendorcats-policy" \
    token_ttl=1h \
    token_max_ttl=4h \
    bind_secret_id=true

# Get role ID (store securely)
vault read auth/approle/role/yendorcats/role-id

# Generate secret ID (regenerate periodically)
vault write -f auth/approle/role/yendorcats/secret-id
```

#### Update Your Application for AppRole

<augment_code_snippet path="backend/YendorCats.API/Services/ISecretsManagerService.cs" mode="EXCERPT">
```csharp
// In SecretsManagerService constructor
string vaultAddress = _configuration["Vault:Address"] ?? "http://localhost:8200";
string roleId = _configuration["Vault:RoleId"] ?? Environment.GetEnvironmentVariable("VAULT_ROLE_ID");
string secretId = _configuration["Vault:SecretId"] ?? Environment.GetEnvironmentVariable("VAULT_SECRET_ID");

var authMethod = new AppRoleAuthMethodInfo(roleId, secretId);
var vaultClientSettings = new VaultClientSettings(vaultAddress, authMethod);
_vaultClient = new VaultClient(vaultClientSettings);
```
</augment_code_snippet>

---

## TLS/SSL Configuration

### 1. Generate Self-Signed Certificates (Free Option)

```bash
# Create certificate directory
mkdir -p /etc/vault/tls

# Generate private key
openssl genrsa -out /etc/vault/tls/vault-key.pem 2048

# Generate certificate
openssl req -new -x509 -key /etc/vault/tls/vault-key.pem \
    -out /etc/vault/tls/vault-cert.pem -days 365 \
    -subj "/C=AU/ST=State/L=City/O=YendorCats/CN=vault.yourdomain.com"
```

### 2. Update Vault Configuration

```hcl
# vault.hcl
ui = true

storage "file" {
  path = "/vault/data"
}

listener "tcp" {
  address = "0.0.0.0:8200"
  tls_cert_file = "/etc/vault/tls/vault-cert.pem"
  tls_key_file = "/etc/vault/tls/vault-key.pem"
  tls_min_version = "tls12"
}

# Disable HTTP listener
# listener "tcp" {
#   address = "0.0.0.0:8201"
#   tls_disable = true
# }
```

### 3. Update Application Configuration

```json
{
  "Vault": {
    "Address": "https://localhost:8200",
    "Token": "your-vault-token-here",
    "SecretPath": "secret/yendorcats/app-secrets",
    "SkipTlsVerify": true
  }
}
```

---

## Access Policies

### Client-Specific Policies

```bash
# Policy for Client 1
vault policy write client1-policy - << EOF
# Allow access only to client1 secrets
path "secret/data/client1/yendorcats/*" {
  capabilities = ["read"]
}

# Deny access to other clients
path "secret/data/client2/*" {
  capabilities = ["deny"]
}
path "secret/data/client3/*" {
  capabilities = ["deny"]
}
EOF

# Policy for Client 2
vault policy write client2-policy - << EOF
path "secret/data/client2/yendorcats/*" {
  capabilities = ["read"]
}
path "secret/data/client1/*" {
  capabilities = ["deny"]
}
path "secret/data/client3/*" {
  capabilities = ["deny"]
}
EOF
```

### Admin Policy for Management

```bash
vault policy write admin-policy - << EOF
# Full access to secrets management
path "secret/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

# Manage auth methods
path "auth/*" {
  capabilities = ["create", "read", "update", "delete", "list", "sudo"]
}

# Manage policies
path "sys/policies/acl/*" {
  capabilities = ["create", "read", "update", "delete", "list", "sudo"]
}
EOF
```

---

## Audit Logging

### Enable File Audit

```bash
# Enable audit logging
vault audit enable file file_path=/vault/logs/audit.log

# Verify audit is enabled
vault audit list
```

### Log Rotation Setup

```bash
# Create logrotate configuration
cat > /etc/logrotate.d/vault << EOF
/vault/logs/audit.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 0640 vault vault
    postrotate
        /bin/kill -HUP \$(cat /var/run/vault.pid) 2>/dev/null || true
    endrotate
}
EOF
```

---

## Network Security

### 1. Firewall Configuration

```bash
# Allow only necessary ports
ufw allow 8200/tcp  # Vault HTTPS
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP (if needed)
ufw allow 443/tcp   # HTTPS
ufw enable
```

### 2. Reverse Proxy with Nginx (Optional)

```nginx
# /etc/nginx/sites-available/vault
server {
    listen 443 ssl;
    server_name vault.yourdomain.com;
    
    ssl_certificate /etc/ssl/certs/vault.crt;
    ssl_certificate_key /etc/ssl/private/vault.key;
    
    location / {
        proxy_pass http://127.0.0.1:8200;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## Backup Security

### 1. Encrypted Backups

```bash
#!/bin/bash
# secure-backup.sh

BACKUP_DIR="/secure/vault-backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="vault-backup-$DATE.tar.gz"

# Create backup
tar -czf $BACKUP_DIR/$BACKUP_FILE /vault/data

# Encrypt backup
gpg --cipher-algo AES256 --compress-algo 1 --s2k-mode 3 \
    --s2k-digest-algo SHA512 --s2k-count 65536 --symmetric \
    --output $BACKUP_DIR/$BACKUP_FILE.gpg $BACKUP_DIR/$BACKUP_FILE

# Remove unencrypted backup
rm $BACKUP_DIR/$BACKUP_FILE

# Keep only last 7 days of backups
find $BACKUP_DIR -name "vault-backup-*.gpg" -mtime +7 -delete
```

### 2. Automated Backup Script

```bash
# Add to crontab
crontab -e

# Backup every 6 hours
0 */6 * * * /secure/scripts/secure-backup.sh
```

---

## Monitoring and Alerting

### 1. Health Check Script

```bash
#!/bin/bash
# vault-health-check.sh

VAULT_ADDR="https://localhost:8200"

# Check if Vault is sealed
STATUS=$(vault status -format=json 2>/dev/null | jq -r '.sealed')

if [ "$STATUS" = "true" ]; then
    echo "CRITICAL: Vault is sealed!"
    # Send alert (email, webhook, etc.)
    exit 2
elif [ "$STATUS" = "false" ]; then
    echo "OK: Vault is unsealed and healthy"
    exit 0
else
    echo "WARNING: Cannot determine Vault status"
    exit 1
fi
```

### 2. Token Expiry Monitoring

```bash
#!/bin/bash
# token-expiry-check.sh

TOKEN_INFO=$(vault token lookup -format=json)
TTL=$(echo $TOKEN_INFO | jq -r '.data.ttl')

# Alert if token expires in less than 24 hours
if [ $TTL -lt 86400 ]; then
    echo "WARNING: Vault token expires in $TTL seconds"
    # Trigger renewal or alert
fi
```

---

## Security Checklist

- [ ] **Authentication**: Use AppRole instead of long-lived tokens
- [ ] **TLS**: Enable HTTPS with valid certificates
- [ ] **Policies**: Implement least-privilege access policies
- [ ] **Audit**: Enable and monitor audit logs
- [ ] **Firewall**: Restrict network access to Vault
- [ ] **Backups**: Encrypt and secure backup files
- [ ] **Monitoring**: Set up health checks and alerts
- [ ] **Updates**: Keep Vault updated to latest stable version
- [ ] **Secrets**: Rotate secrets regularly
- [ ] **Access**: Review and audit access permissions monthly

---
