### **Architecture Overview**

#architecture #single-vault #multi-tenant

Copy

Single Vault Instance
```
|── secret/client1/website1/
│   ├── app-secrets
│   ├── db-credentials
│   └── api-keys
|── secret/client2/ecommerce/
│   ├── app-secrets
│   ├── payment-keys
│   └── db-credentials
|── secret/client3/portfolio/
│   ├── app-secrets
│   └── cms-keys
└── secret/shared/
    ├── monitoring-keys
    └── backup-credentials
```

### **Implementation Example**

#implementation #policies #access-control
```
# Create client-specific policies
vault policy write client1-policy - <<EOF
path "secret/data/client1/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}
path "secret/metadata/client1/*" {
  capabilities = ["list"]
}
# Deny access to other clients
path "secret/data/client2/*" {
  capabilities = ["deny"]
}
path "secret/data/client3/*" {
  capabilities = ["deny"]
}
EOF

# Create client-specific tokens
vault token create -policy=client1-policy -ttl=8760h -renewable=true
```

### **Client Isolation Configuration**

#isolation #security #multi-tenant
```json
{
  "clients": {
    "client1": {
      "vault_token": "hvs.client1-specific-token",
      "secret_path": "secret/client1/website1",
      "policies": ["client1-policy"],
      "allowed_ips": ["client1-server-ip"]
    },
    "client2": {
      "vault_token": "hvs.client2-specific-token", 
      "secret_path": "secret/client2/ecommerce",
      "policies": ["client2-policy"],
      "allowed_ips": ["client2-server-ip"]
    }
  }
}
```

