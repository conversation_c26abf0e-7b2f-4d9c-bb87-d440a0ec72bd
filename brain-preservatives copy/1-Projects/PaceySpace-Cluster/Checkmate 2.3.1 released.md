---
title: "Checkmate 2.3.1 released"
source: "https://www.reddit.com/r/selfhosted/comments/1m3uj6p/checkmate_231_released/?share_id=wd7139nHXZaaG_tJLcm3H&utm_content=2&utm_medium=ios_app&utm_name=iossmf&utm_source=share&utm_term=22"
author:
  - "[[gorkemcetin]]"
published: 2025-07-19
created: 2025-07-21
description:
tags:
  - "clippings"
---
**Checkmate is an open-source, self-hosted tool designed to track and monitor server hardware, uptime, response times, and incidents in real-time with beautiful visualizations.**

This release introduces several features and fixes a few bugs. Also there are several UI tweaks, UX improvements and small changes for stability of the whole system. Also we're so proud to have passed 90+ contributors and 6.9K stars mark!

**In this release (2.2 + 2.3 combined):**

- BullMQ and Redis have been removed from the project and replaced with Pulse. People had a lot of issues with those two services and we've seen a great deal of simplicity with Pulse.
- Notification channels have been added. This means you don't have to define a notification for each monitor, but add it under the global Notification section, which can be accessed from the sidebar. Then, each notification channel can be added to monitors.
- Incidents section now includes a summary of all incidents.
- You can optionally add/remove the administrator login link in the status page
- You can optionally display IP/URL on a status page
- A new sidebar for "Logs" have been added. It includes two tabs:
	- Job queue: All the jobs (e.g active pings) can be viewed here
	- Server logs: All the logs in the Docker container, which makes the debugging of issues easier.
- Added PagerDuty integration to notifications
- Added a search button for Infrastructure monitors
- Status page servers can now be bulk selected

Web page: [https://checkmate.so/](https://checkmate.so/)  
Discord channel: [https://discord.com/invite/NAb6H3UTjK](https://discord.com/invite/NAb6H3UTjK)  
Reddit channel: [https://www.reddit.com/r/CheckmateMonitoring](https://www.reddit.com/r/CheckmateMonitoring)  
GitHub: [https://github.com/bluewave-labs/checkmate](https://github.com/bluewave-labs/checkmate)  
Download: [https://github.com/bluewave-labs/Checkmate/releases](https://github.com/bluewave-labs/Checkmate/releases)  
Documentation: [https://docs.checkmate.so/](https://docs.checkmate.so/)

---

## Comments

> **veraokulo** • [9 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zip0f/) • 2025-07-19
> 
> How does it compare to gatus?
> 
> > **gorkemcetin** • [7 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zmdh2/) • 2025-07-19
> > 
> > I "think" Gatus is an uptime monitor + status page, whereas Checkmate is uptime monitor/status page + infra monitor + webspeed monitor and (soon) network monitor.
> > 
> > I might be mistaken about Gatus tho, that is what I see on their web page.

> **dgibbs128** • [16 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zetbp/) • 2025-07-19
> 
> Looks good. I have been after a simple all in one tool for ping/website/server monitoring. Currently, use uptime kuma and beszel so this might actually meet my needs better
> 
> > **gorkemcetin** • [3 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zff4k/) • 2025-07-19
> > 
> > Going to add "Network" section as well to get more "network" related info from servers as well, just fyi.
> > 
> > If you would like to add a wishlist, we have an issue for that: [https://github.com/bluewave-labs/Checkmate/issues/2389](https://github.com/bluewave-labs/Checkmate/issues/2389)
> > 
> > > **dgibbs128** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zfmyt/) • 2025-07-19
> > > 
> > > Thanks. I will spin up a demo container and play with it. If im happy I will get a proper install going. Also I am unable to login to your demo. Says the password is wrong
> > > 
> > > > **gorkemcetin** • [0 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zggby/) • 2025-07-19
> > > > 
> > > > Ops, will fix this. Thanks for the heads up!

> **kY2iB3yH0mN8wI2h** • [5 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3znkq5/) • 2025-07-19
> 
> 2 bad the demo does not work
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zny8d/) • 2025-07-19
> > 
> > Yup the db is removed after the new deployment :-D Waiting for the dev to fix it.

> **dude\_Im\_hilarious** • [4 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zj8ij/) • 2025-07-19
> 
> The resource monitoring on the client machines - does that easily work on windows? Mac?
> 
> > **gorkemcetin** • [5 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zlw5j/) • 2025-07-19
> > 
> > Currently Linux but the maintainer is working on Win and Mac, and those 2 platforms will be available soon.

> **rubn-g** • [4 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n409fcp/) • 2025-07-19
> 
> Pulse is now an archived project, unmantained. What do you plan to do?
> 
> > **gorkemcetin** • [5 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40l0ry/) • 2025-07-19
> > 
> > We already wrote a replacement for it and it’ll be ready in the next release.
> > 
> > **redundant78** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41zhsb/) • 2025-07-19
> > 
> > This is a legit concern - running your monitoring system on an abandoned dependency seems like a reciepe for trouble down the road.

> **ZeshinFox** • [4 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40o9a0/) • 2025-07-19
> 
> This looks really interesting. I was looking for some Uptime Kuma alternatives so I’d love to try this out. I was wondering if it’s possible to front it with Traefik and not run the nginx front end? Assuming you just use nginx as the ssl / proxy layer - I haven’t looked into the config or code yet.
> 
> I was also curious if you’re thinking of adding OIDC?
> 
> > **gorkemcetin** • [5 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40qip6/) • 2025-07-19
> > 
> > Traefik should be possible. There are people using Checkmate with Traefik already.
> > 
> > OIDC: In the plans but not very imminent to be honest. There is an open issue for this.

> **mcking230** • [3 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zpdu5/) • 2025-07-19
> 
> Nice thing. Upkuma+librenms+beszel+dashdot+anyothersoftware)))) Will try it today. I like that u do it more stable than more full of bugy features.
> 
> > **gorkemcetin** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zv48x/) • 2025-07-19
> > 
> > We are testing before each release as much as we can really. Not to turn down anyone.
> > 
> > > **mcking230** • [0 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zvezd/) • 2025-07-19
> > > 
> > > Will try to be a part of your project.
> > > 
> > > > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zwet2/) • 2025-07-19
> > > > 
> > > > Thank you. Appreciate your support.

> **ilikeror2** • [3 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40xf7i/) • 2025-07-19
> 
> Wow this looks amazing
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40yckk/) • 2025-07-19
> > 
> > Thank you! :)
> > 
> > > **ilikeror2** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41dfah/) • 2025-07-19
> > > 
> > > Any plans to publish to unraid App Store?
> > > 
> > > > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41haf3/) • 2025-07-19
> > > > 
> > > > Sorry, not sure what it is.
> > > > 
> > > > > **ilikeror2** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41hj65/) • 2025-07-19
> > > > > 
> > > > > One of the most popular community NAS OS’s with built in docker support: [https://unraid.net/](https://unraid.net/)
> > > > > 
> > > > > > **gorkemcetin** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41tsuv/) • 2025-07-19
> > > > > > 
> > > > > > Got it. We can do if we have enough demand, for sure!
> > > > > > 
> > > > > > > **kataflokc** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41z17w/) • 2025-07-19
> > > > > > > 
> > > > > > > Yes, I second that!
> > > > > > > 
> > > > > > > [Making an unRAID template](https://selfhosters.net/docker/templating/templating/) is really simple btw
> > > > > > > 
> > > > > > > **ilikeror2** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41uy8a/) • 2025-07-19
> > > > > > > 
> > > > > > > I just posted this in the unraid subreddit, let’s see if it picks up any traction :)
> > > > > > > 
> > > > > > > > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41v799/) • 2025-07-19
> > > > > > > > 
> > > > > > > > Sure, if it’s an easy process (that needs our attention) we can also do. Or is it something Unraid devs need to get done?
> > > > > > > > 
> > > > > > > > > **ilikeror2** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41vh65/) • 2025-07-19
> > > > > > > > > 
> > > > > > > > > I believe it’s all on your side, but I’m not well versed in it 100%. You just need to have a docker template and then submit it to the community applications maintainer.

> **CMageti** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zn46z/) • 2025-07-19
> 
> I'm discovering your tool, and I have one question : can it have custom checks ? like the custom scripts we can run in nagios ? or the local checks in checkmk ?
> 
> I checked your website and doc (really quick, I must say) and couldn't find a mention of such a feature.
> 
> > **gorkemcetin** • [5 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zo4rb/) • 2025-07-19
> > 
> > Zabbix plugin support ([https://www.zabbix.com/documentation/current/en/devel/plugins](https://www.zabbix.com/documentation/current/en/devel/plugins)) is in the works. This way you'll be able to do simple, custom checks.
> > 
> > > **CMageti** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zr2vy/) • 2025-07-19
> > > 
> > > Great news, thanks
> > > 
> > > > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zv8lo/) • 2025-07-19
> > > > 
> > > > Oh thanks for your interest [u/CMageti](https://www.reddit.com/user/CMageti/) !
> > 
> > **tfks** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40shuh/) • 2025-07-19
> > 
> > I don't know anything about Zabbix or what it can do; would this mean that people can write custon notification plugins?
> > 
> > > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40v4sg/) • 2025-07-19
> > > 
> > > Yes. Either you would be able to use one of the Zabbix plugins to monitor anything on your Linux server or you'd be able to write your own as well.

> **Liberam-Societatem** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zsa33/) • 2025-07-19
> 
> I deployed it on my server, tried to register, but it gives an error. I didn’t understand what was going on
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zupnr/) • 2025-07-19
> > 
> > Not sure what the issue is - any logs or errors?

> **Chinoman10** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40c9n8/) • 2025-07-19
> 
> OP, please allow the backend to have a different domain than the front-end... I get errors/rejections from the backend since the front-end uses a different domain...

> **Boomam** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40tdsz/) • 2025-07-19
> 
> Looks like it has potential. Its a bit brittle though...  
>    
> Fresh install on a fresh test system, page dies and then logs you out if you try adding a infrastructure monitor that isnt in the format it expects.  
>    
> Docs & workflow needs some work too, to add in notes on first login creation.  
>    
> Will be keeping and eye on it though, being able to consolidate several apps into one is great, and most importantly, its not too chatty on the logs either. :-)
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40uwys/) • 2025-07-19
> > 
> > Thanks - what was the format you inputted? I haven't seen this issue before so wondering what happened.
> > 
> > > **Boomam** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40vfbf/) • 2025-07-19
> > > 
> > > I started typing a FQDN for a server, accidently added in https at the beginning and it killed the page (as in locked up). Nothing responded.  
> > > Going back to the root IP of the test system just took me back to a login page.  
> > > I was able to duplicate it between Windows & MacOS too.  
> > >  
> > > 
> > > Was just playing around with notifications too - "Sending notification failed" when testing both webhooks and SMTP.  
> > > Testing both in the shell of the container works however (curl, etc.)  
> > >  
> > > 
> > > > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40wc56/) • 2025-07-19
> > > > 
> > > > Probably there is something wrong with the installation. Here is what I get: [https://ibb.co/N69yfVMD](https://ibb.co/N69yfVMD)
> > > > 
> > > > Also the same with "sending notifications failed" - if curl works, it should work on the server.
> > > > 
> > > > What is the version of your Checkmate?
> > > > 
> > > > > **Boomam** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40wtc3/) • 2025-07-19
> > > > > 
> > > > > I'm just using the docker-compose from the docs. Only changes are updating the UPTIME\_APP\_API\_BASE\_URL to the IP of my test system.
> > > > > 
> > > > > > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41tyh1/) • 2025-07-19
> > > > > > 
> > > > > > Did you change the other two vars as per the note in the installation doc?
> > > > > > 
> > > > > > > **Boomam** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41u6ss/) • 2025-07-19
> > > > > > > 
> > > > > > > Yes. I'm not worried about it, I'll likely bookmark the app and keep an eye on it over the next few months - start fresh then.

> **ztjuh** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41tca7/) • 2025-07-19
> 
> God works in mysterious ways ♟️

> **evrial** • [\-13 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3ze6ip/) • 2025-07-19
> 
> Do we really need 5 clones of uptime kuma?
> 
> > **xAragon\_** • [15 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zejfo/) • 2025-07-19
> > 
> > The more free open source options, the better.  
> > You don't have to use any of them.
> > 
> > > **evrial** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zez0b/) • 2025-07-19
> > > 
> > > Fair, I only wish these people had the mental capacity to unite and replace nextcloud from existence
> > > 
> > > > **Arphenyte** • [4 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zfkh2/) • 2025-07-19
> > > > 
> > > > OpenCloud is trying to do that, they are not quite there yet, but keeping my eye on it.
> > > > 
> > > > > **evrial** • [0 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zh02y/) • 2025-07-19
> > > > > 
> > > > > Their codebase is largely renamed ocis, no way to takeoff with that
> > > 
> > > **cataklix** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n401tv9/) • 2025-07-19
> > > 
> > > I’m working on a cloud native replacement of NextCloud / Google Suite, fully encrypted and self-hostable, even for email.
> > > 
> > > It’s pretty new : I started in march 25, and I’ve released Task & Notes as of now, lmk if you have any feedbacks :)
> > > 
> > > [https://github.com/atomic-blend](https://github.com/atomic-blend)
> > > 
> > > **xAragon\_** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zf4mt/) • 2025-07-19
> > > 
> > > Have you tried OwnCloud Infinite Scale?
> > > 
> > > > **evrial** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zgcbz/) • 2025-07-19
> > > > 
> > > > Their docker compose files are nasty
> 
> **gorkemcetin** • [5 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zgmf4/) • 2025-07-19
> 
> This is not only an uptime app and not a clone.
> 
> [https://checkmate.so/](https://checkmate.so/)
> 
> **NatoBoram** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n412wxh/) • 2025-07-19
> 
> Yes.
> 
> We also need *more* than 40 different kanban boards.

> **SirSoggybotom** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n3zoq3m/) • 2025-07-19
> 
> Congrats on the release! For projects not needing Redis/BullMQ, Pulse is a cool switch. Question: with the new "Logs" sidebar, are there plans to integrate with external logging tools, or will it stay Docker-centric? Open-source versatility keeps things fresh, but centralized logging could be a boost. Thoughts?
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n45uhp1/) • 2025-07-20
> > 
> > Logs sidebar is all about the Checkmate logs, meaning it gives you more info when there is something wrong.
> > 
> > Collecting logs and acting on them is a different beast. We first want to do best about what we are doing :)

> **RVP97** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40w5ad/) • 2025-07-19
> 
> Would be very useful to be able to ping Postgres like in uptime kuma
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n40ykqy/) • 2025-07-19
> > 
> > Checkmate has port monitoring as well - that should help?
> > 
> > > **NatoBoram** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n4144za/) • 2025-07-19
> > > 
> > > I think it's more about having helpful presets to make it easier to configure
> > > 
> > > > **gorkemcetin** • [3 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n419s86/) • 2025-07-19
> > > > 
> > > > That is interesting. Not everyone should remember port numbers :) Thank you!
> > > > 
> > > > > **adamshand** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n42jzwy/) • 2025-07-19
> > > > > 
> > > > > If you wanted to do something more useful than checking a port, being able to issue a sql command and check the result would be quite useful to a lot of people.
> > 
> > **RVP97** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n42b3o6/) • 2025-07-19
> > 
> > For example with uptime kuma I select Postgres, then put the db url, and put a query like select 1. Is that possible with port monitoring?
> > 
> > > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n42cgic/) • 2025-07-19
> > > 
> > > Ah currently no, but that would be a great addition. If you could create an issue for this, we can implement.
> > > 
> > > > **RVP97** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n42d1y0/) • 2025-07-19
> > > > 
> > > > Great!!

> **zandadoum** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n4117xw/) • 2025-07-19
> 
> Interesting.
> 
> I have several backup systems that only send notifications via mail.
> 
> Can your system check emails and extract information from them (body has info like host name, time start, time end, GB transferred and ofc if it is OK or KO)?
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n412ru1/) • 2025-07-19
> > 
> > Unfortunately no, Checkmate only checks servers and not emails.
> > 
> > > **zandadoum** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n41316q/) • 2025-07-19
> > > 
> > > Shame. I use way too many tools and I am looking into unifying stuff. checkmk looked promising and I got the email part running, but it’s so over complicated that I shiver just thinking of adding 1 feature to it.

> **stackfullofdreams** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n43o0af/) • 2025-07-20
> 
> I tried to deploy this a few months ago and can't remember what went wrong . I really wanted the global map with servers on it . I should try again or let Claude code figure it out for me
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n44oodd/) • 2025-07-20
> > 
> > Most probably it was because the installation was a bit hard. We quite simplified it so you shouldnt have issues.
> > 
> > As per the map, definitely doable. How many servers do you have?
> > 
> > > **stackfullofdreams** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n493d2j/) • 2025-07-20
> > > 
> > > I thought there was a map you guys had but needed to get it working... we have < 20 now, install wa sa lot smoother but I did let claude code do the work this time.

> **bendem** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n44vk3p/) • 2025-07-20
> 
> What does the incident management feature look like? The demo is down and there is no screenshots of it.
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n45uqdx/) • 2025-07-20
> > 
> > You can check [https://docs.checkmate.so](https://docs.checkmate.so/) for additional screenshots

> **UltraCheckmate** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n45oscv/) • 2025-07-20
> 
> Nice name.

> **Gohanbe** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n45u25z/) • 2025-07-20
> 
> +1 for pulse, absolutely phenomenal monitoring solution for proxmox, OP please add a link also.
> 
> > **gorkemcetin** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n45vkqz/) • 2025-07-20
> > 
> > Thanks. I am not sure - which link?

> **AfterShock** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n4634u3/) • 2025-07-20
> 
> Is it just monitoring or can it also take basic actions like ssh and restart docker containers if found unhealthy?
> 
> > **gorkemcetin** • [2 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n465kl3/) • 2025-07-20
> > 
> > Currently it's just about monitoring. The features you mention is on top of our minds but we'll need to get monitoring done right first :)

> **Far-File-1477** • [1 points](https://reddit.com/r/selfhosted/comments/1m3uj6p/comment/n46s6ub/) • 2025-07-20
> 
> So, trying to install with docker compose on a Kubuntu machine with this yaml
> 
> [https://raw.githubusercontent.com/bluewave-labs/Checkmate/develop/docker/dist-mono/docker-compose.yaml](https://raw.githubusercontent.com/bluewave-labs/Checkmate/develop/docker/dist-mono/docker-compose.yaml)
> 
> All god with the install, when trying to register i get the following error.
> 
> We're unable to connect to the server. Please check your internet connection or verify your deployment configuration if the problem.
> 
> Any fix for this? I also tried replacing localhost with my server ip in the yaml file, no good.