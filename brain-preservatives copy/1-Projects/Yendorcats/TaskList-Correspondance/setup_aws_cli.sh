#!/bin/bash

# AWS CLI Setup for Backblaze B2
echo "Setting up AWS CLI for Backblaze B2..."

# Configure AWS CLI with Backblaze B2 credentials
aws configure set aws_access_key_id 004d0cd685eb5360000000008 --profile backblaze
aws configure set aws_secret_access_key K0049vAJ9EscCkyDMkP978wwES+Z2NI --profile backblaze
aws configure set region us-west-004 --profile backblaze

echo "AWS CLI configured for Backblaze B2"
echo ""
echo "Example commands to view metadata:"
echo "-----------------------------------"
echo ""
echo "# List objects in bucket:"
echo "aws s3 ls s3://yendor/YendorCats-General-SiteAccess/ --endpoint-url=https://s3.us-west-004.backblazeb2.com --profile backblaze"
echo ""
echo "# View metadata for a specific object:"
echo "aws s3api head-object --bucket yendor --key Yendor<PERSON>ats-General-SiteAccess/queens/IMG_8520.jpg --endpoint-url=https://s3.us-west-004.backblazeb2.com --profile backblaze"
echo ""
echo "# View metadata (formatted with jq):"
echo "aws s3api head-object --bucket yendor --key YendorCats-General-SiteAccess/queens/IMG_8520.jpg --endpoint-url=https://s3.us-west-004.backblazeb2.com --profile backblaze | jq '.Metadata'"
