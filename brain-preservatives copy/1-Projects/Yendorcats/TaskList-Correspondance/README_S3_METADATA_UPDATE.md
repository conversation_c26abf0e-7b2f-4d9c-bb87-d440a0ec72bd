# YendorCats S3 Metadata Update Tool

This Python script batch updates S3 metadata for cat images stored in Backblaze B2, ensuring the website displays correct information for each cat.

## Features

- **Batch metadata updates** for all cat images
- **Dry run mode** to preview changes before applying
- **Category-specific updates** (queens or studs only)
- **Automatic cat identification** based on filename patterns
- **Progress tracking** and error reporting
- **Safe operation** with confirmation prompts

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

Or manually install:
```bash
pip install boto3 botocore
```

### 2. Configure Secret Key

You have three options for providing the Backblaze secret key:

#### Option A: Environment Variable (Recommended)
```bash
export BACKBLAZE_SECRET_KEY='your-secret-key-here'
python update_s3_metadata.py
```

#### Option B: Edit Script Directly
Open `update_s3_metadata.py` and add your secret key:
```python
S3_CONFIG = {
    # ... other config ...
    'secret_access_key': 'your-secret-key-here'
}
```

#### Option C: Enter When Prompted
Just run the script and it will ask for the key:
```bash
python update_s3_metadata.py
```

## Usage

### Run the Script

```bash
python update_s3_metadata.py
```

### Menu Options

1. **Dry Run** - Shows what would be updated without making changes
2. **Update All Images** - Updates metadata for all cat images
3. **Update Queens Only** - Updates only images in the queens category
4. **Update Studs Only** - Updates only images in the studs category
5. **Exit** - Quit the program

### Recommended Workflow

1. **Start with a dry run** (Option 1) to see what changes will be made
2. Review the output to ensure correct cat matching
3. Run the actual update (Options 2-4) when satisfied

## Metadata Fields Updated

Based on client correspondence, the script updates these fields:

### Required Fields
- `name` - Cat's name
- `gender` - M or F
- `breed` - Maine Coon
- `color` - Color description
- `hair_color` - Fur color details
- `date_of_birth` - Birth date
- `category` - queens/studs/kittens
- `type` - queen/stud/kitten

### Optional Fields
- `description` - Detailed description
- `personality` - Personality traits
- `mother` - Mother's name (for offspring)
- `father` - Father's name (for offspring)

## Cat Data Included

### Queens (7 cats)
- Indy (black smoke tortie)
- Rosie (black silver tortie tabby)
- Athena (black smoke and white)
- Jeannie (black silver and white tortie tabby)
- Sera (black tabby bi colour)
- Anji (black tortie tabby)
- Loretta (black silver tabby)

### Studs (3 cats)
- Louie (blue silver tabby) - Son of Soren & Athena
- Soren (blue/silver tabby and white)
- Dennis (black silver mackerel tabby)

## How It Works

1. **Connects to Backblaze B2** using S3-compatible API
2. **Lists all images** in the bucket (or specified prefix)
3. **Matches images to cats** using filename patterns:
   - Photo numbers (e.g., "4400" for Indy)
   - Cat names in filenames
4. **Compares metadata** to check if update needed
5. **Updates metadata** by copying object with new metadata
6. **Reports results** with summary statistics

## Troubleshooting

### Connection Issues
- Verify your secret key is correct
- Check internet connection
- Ensure bucket name is correct: `yendor`

### No Matches Found
- Check that image filenames contain:
  - Photo numbers from correspondence (e.g., 4400, 8520)
  - Cat names (e.g., indy.jpg, rosie.jpg)

### Metadata Not Showing on Website
- Ensure website code reads S3 metadata correctly
- Check that metadata keys match what website expects
- Verify images have public-read ACL

## Adding New Cats

To add more cats, edit the `CATS_METADATA` dictionary in the script:

```python
{
    'name': 'NewCat',
    'filename_patterns': ['unique-id', 'newcat'],
    'metadata': {
        'name': 'NewCat',
        'gender': 'M',
        'breed': 'Maine Coon',
        # ... other fields
    }
}
```

## Safety Features

- **Dry run mode** - Preview changes without modifying anything
- **Confirmation prompts** - Must confirm before live updates
- **Preserves ACL** - Maintains public-read access for website
- **Error handling** - Continues on errors, reports at end
- **Metadata validation** - Only updates when changes detected

## Important Notes

1. **Backup recommended** - Consider backing up important metadata before bulk updates
2. **S3 copy operation** - Updates require copying objects (normal S3 behavior)
3. **Public access maintained** - Script preserves public-read ACL for website display
4. **Case insensitive matching** - Filename patterns match regardless of case

## Support

For issues or questions about:
- **Script usage** - Check this README
- **Cat information** - Refer to client correspondence
- **Website display** - Check website's S3 metadata implementation
- **Backblaze B2** - Consult Backblaze documentation
