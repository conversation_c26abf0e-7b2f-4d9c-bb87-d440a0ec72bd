# Backblaze B2 Metadata Configuration for YendorCats Images

## Configuration Details
- **Storage Provider**: Backblaze B2 (S3-compatible)
- **Bucket**: `yendor`
- **Path**: `YendorCats-General-SiteAccess/`
- **Galleries**: `queens/`, `studs/`, `kittens/`
- **Endpoint**: `https://s3.us-west-004.backblazeb2.com`

## Metadata Updates Applied

### ✅ All Images Updated Successfully

All images in your three galleries have been updated with the following metadata:

1. **Content-Type Headers**
   - Set correctly based on file extension (e.g., `image/jpeg` for .jpg files)

2. **Cache-Control Headers**
   - Set to: `public, max-age=31536000, immutable`
   - Enables aggressive caching (1 year) for better performance

3. **Content-Disposition Headers**
   - Set to: `inline; filename="[filename]"`
   - Ensures images display inline in browsers rather than downloading

4. **Custom Metadata**
   - `original-name`: Stores the original filename
   - `image-type`: Stores the file extension type (jpg, png, etc.)
   - `gallery`: Indicates which gallery the image belongs to (queens, studs, or kittens)

## Images Updated

- **Queens Gallery**: 7 images updated
- **Studs Gallery**: 
  - Dennis subfolder: 5 images
  - Soren subfolder: 5 images
  - louie subfolder: 7 images
- **Kittens Gallery**: 16 images updated

**Total**: 40 images successfully updated

## CORS Configuration

Your Backblaze bucket already has CORS properly configured:
```json
{
    "AllowedHeaders": ["authorization", "range"],
    "AllowedMethods": ["HEAD", "GET"],
    "AllowedOrigins": ["*"],
    "MaxAgeSeconds": 3600
}
```

## Frontend Implementation

### Image URLs for Your Frontend

Use the following URL format in your frontend application:

```javascript
// Backblaze B2 public URL format
const imageUrl = `https://f004.backblazeb2.com/file/yendor/YendorCats-General-SiteAccess/${gallery}/${imageName}`;

// Example URLs:
// Queens: https://f004.backblazeb2.com/file/yendor/YendorCats-General-SiteAccess/queens/IMG_6277.jpg
// Studs: https://f004.backblazeb2.com/file/yendor/YendorCats-General-SiteAccess/studs/Dennis/[imagename].jpg
// Kittens: https://f004.backblazeb2.com/file/yendor/YendorCats-General-SiteAccess/kittens/charlie.jpg
```

### HTML Example

```html
<img 
  src="https://f004.backblazeb2.com/file/yendor/YendorCats-General-SiteAccess/kittens/charlie.jpg"
  alt="Charlie the kitten"
  loading="lazy"
  decoding="async"
/>
```

### React/JavaScript Example

```javascript
const GalleryImage = ({ gallery, imageName, altText }) => {
  const baseUrl = 'https://f004.backblazeb2.com/file/yendor/YendorCats-General-SiteAccess';
  const imageUrl = `${baseUrl}/${gallery}/${imageName}`;
  
  return (
    <img 
      src={imageUrl}
      alt={altText}
      loading="lazy"
      decoding="async"
      onError={(e) => {
        console.error(`Failed to load image: ${imageUrl}`);
      }}
    />
  );
};
```

## Verification Commands

To verify any image's metadata:

```bash
aws s3api head-object \
  --bucket yendor \
  --key "YendorCats-General-SiteAccess/[gallery]/[image-name]" \
  --profile backblaze \
  --endpoint-url https://s3.us-west-004.backblazeb2.com | jq '.'
```

## Script Location

The update script has been saved for future use:
`/Users/<USER>/Documents/notes/Obsidian/Brain/brain-preservatives/1-Projects/Yendorcats/TaskList-Correspondance/update_backblaze_metadata.sh`

You can run this script again whenever you add new images to your galleries.

## Troubleshooting

If images still don't display properly on your frontend:

1. **Verify the Backblaze public URL format** - The f004 subdomain might vary based on your region
2. **Check browser console** for specific error messages
3. **Clear browser cache** to ensure new headers are loaded
4. **Test image URL directly** in browser to ensure it's accessible
5. **Check if Backblaze bucket is set to public** for file access

## Benefits of These Updates

1. **Improved Performance**: Images will be cached by browsers for 1 year
2. **Better SEO**: Proper content-type headers help search engines
3. **Reduced Bandwidth**: Caching reduces repeated downloads
4. **Professional Display**: Images display inline instead of prompting downloads
5. **Metadata Tracking**: Gallery information stored with each image
