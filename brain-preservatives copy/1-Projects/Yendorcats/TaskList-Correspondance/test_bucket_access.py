#!/usr/bin/env python3
"""
Test which buckets are accessible with current credentials
"""

import boto3
from botocore.exceptions import ClientError

# S3 Configuration
S3_CONFIG = {
    'endpoint_url': 'https://s3.us-west-004.backblazeb2.com',
    'access_key_id': '004d0cd685eb5360000000008',
    'secret_access_key': 'K0049vAJ9EscCkyDMkP978wwES+Z2NI'
}

def test_bucket_access():
    """Test access to each bucket"""
    
    print("Testing Bucket Access Permissions")
    print("=" * 60)
    
    # Create S3 client
    s3_client = boto3.client(
        's3',
        endpoint_url=S3_CONFIG['endpoint_url'],
        aws_access_key_id=S3_CONFIG['access_key_id'],
        aws_secret_access_key=S3_CONFIG['secret_access_key']
    )
    
    # Get list of buckets
    try:
        response = s3_client.list_buckets()
        buckets = [b['Name'] for b in response.get('Buckets', [])]
    except:
        print("Failed to list buckets")
        return
    
    print(f"Found {len(buckets)} buckets. Testing access to each:\n")
    
    for bucket_name in buckets:
        print(f"Testing: {bucket_name}")
        
        try:
            # Try to list objects
            response = s3_client.list_objects_v2(
                Bucket=bucket_name,
                MaxKeys=3
            )
            
            if 'Contents' in response:
                count = response.get('KeyCount', 0)
                print(f"  ✓ READ access - Found {count} objects")
                
                # Show a few sample files
                for obj in response['Contents'][:3]:
                    print(f"    - {obj['Key']}")
                    
                # Try to get metadata for first object
                if response['Contents']:
                    first_key = response['Contents'][0]['Key']
                    try:
                        head_response = s3_client.head_object(
                            Bucket=bucket_name,
                            Key=first_key
                        )
                        metadata = head_response.get('Metadata', {})
                        if metadata:
                            print(f"    Metadata for {first_key}:")
                            for k, v in list(metadata.items())[:3]:
                                print(f"      {k}: {v}")
                    except:
                        pass
            else:
                print(f"  ✓ READ access - Bucket is empty")
                
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            if error_code == 'AccessDenied':
                print(f"  ✗ NO ACCESS - {e.response.get('Error', {}).get('Message', 'Access Denied')}")
            else:
                print(f"  ✗ Error: {error_code}")
        
        print()
    
    print("=" * 60)
    print("\nRECOMMENDATION:")
    print("The application key needs to be updated with permissions for the 'yendor' bucket.")
    print("\nIn Backblaze B2:")
    print("1. Go to App Keys section")
    print("2. Create a new key or edit the existing one")
    print("3. Set 'Bucket Restrictions' to 'yendor' (or 'All')")
    print("4. Set capabilities to include: listBuckets, listFiles, readFiles, writeFiles")
    print("5. Use the new key in this script")

if __name__ == "__main__":
    test_bucket_access()
