#!/usr/bin/env python3
"""
Verify that metadata was successfully applied to S3 objects
"""

import boto3
from botocore.exceptions import ClientError
import json

# S3 Configuration
S3_CONFIG = {
    'endpoint_url': 'https://s3.us-west-004.backblazeb2.com',
    'bucket_name': 'yendor',
    'access_key_id': '004d0cd685eb5360000000008',
    'secret_access_key': 'K0049vAJ9EscCkyDMkP978wwES+Z2NI'
}

def verify_metadata():
    """Verify metadata on a sample of images"""
    
    print("Verifying S3 Metadata Updates")
    print("=" * 60)
    
    # Create S3 client
    s3_client = boto3.client(
        's3',
        endpoint_url=S3_CONFIG['endpoint_url'],
        aws_access_key_id=S3_CONFIG['access_key_id'],
        aws_secret_access_key=S3_CONFIG['secret_access_key']
    )
    
    # Sample images to check
    test_images = [
        'YendorCats-General-SiteAccess/queens/IMG_4400.jpg',  # Indy
        'YendorCats-General-SiteAccess/queens/Cat1/TESTING-IMG_4400.jpg',  # Indy
        'YendorCats-General-SiteAccess/queens/IMG_8520.jpg',  # Jeannie
        'YendorCats-General-SiteAccess/studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg',  # Dennis
        'YendorCats-General-SiteAccess/studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg',  # Soren
        'YendorCats-General-SiteAccess/studs/louie/510586750_24370060715931660_903145896275112471_n.jpg',  # Louie
    ]
    
    for image_key in test_images:
        print(f"\nChecking: {image_key}")
        print("-" * 40)
        
        try:
            response = s3_client.head_object(
                Bucket=S3_CONFIG['bucket_name'],
                Key=image_key
            )
            
            metadata = response.get('Metadata', {})
            
            if metadata:
                print("✓ Metadata found:")
                # Print key metadata fields
                important_fields = ['name', 'gender', 'breed', 'color', 'date-of-birth', 'category', 'type', 'mother', 'father']
                
                for field in important_fields:
                    if field in metadata:
                        print(f"  - {field}: {metadata[field]}")
                
                # Show total metadata count
                print(f"  Total metadata fields: {len(metadata)}")
            else:
                print("✗ No metadata found")
                
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', '')
            if error_code == 'NotFound':
                print(f"✗ Image not found")
            else:
                print(f"✗ Error: {error_code}")
    
    print("\n" + "=" * 60)
    print("Verification Complete")
    print("=" * 60)
    
    # Generate public URLs for testing
    print("\nPublic URLs for website testing:")
    print("-" * 40)
    base_url = "https://s3.us-west-004.backblazeb2.com/yendor/"
    
    for image_key in test_images[:3]:  # Show first 3 URLs
        if 'Cat1/TESTING' not in image_key:  # Skip test images
            public_url = base_url + image_key
            print(f"{public_url}")

if __name__ == "__main__":
    verify_metadata()
