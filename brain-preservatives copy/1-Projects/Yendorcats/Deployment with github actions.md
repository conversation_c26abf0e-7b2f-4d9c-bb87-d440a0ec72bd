---
creation_date: 2025-08-12
modification_date: 2025-08-12
type: project
status: active
priority: high
deadline: 2025-09-11
tags:
  - para/projects
  - yendor
  - yendorcats
  - deployment
  - github-actions
---
---
# #github-actions #docker #compose #ghcr #ci-cd #cache-busting #git-sha #deployment #automation

# Complete GitHub Container Registry CI/CD Setup with Git Commit SHA Cache-Busting

This comprehensive guide sets up automated Docker image building and deployment using GitHub Actions, GitHub Container Registry (GHCR), and git commit SHAs for cache-busting. Every commit to main automatically builds and tags images with the commit SHA, ensuring fresh deployments and proper browser cache invalidation.

---

## Overview of the Complete System

### What happens on each commit:
1. **GitHub Actions triggers** when you push to main
2. **Builds three Docker images** (API, Frontend, Uploader) tagged with the git commit SHA
3. **Pushes to GHCR** at `ghcr.io/your-username/yendorcats-*:abc1234`
4. **Frontend assets** get cache-busted with the same SHA in CSS/JS URLs
5. **Deploy script** pulls the specific SHA-tagged images and restarts services

### Benefits:
- **Immutable deployments**: Each commit creates a unique, traceable image
- **Cache-busting**: Browser caches refresh only when assets actually change
- **Rollback capability**: Easy to redeploy any previous commit's images
- **Zero manual versioning**: Git SHA automatically provides the version

---

## Files Created/Modified

### 1. GitHub Actions Workflow
**File:** `.github/workflows/frontend-ci.yml`

This workflow builds all three services (API, Frontend, Uploader) and tags them with the git commit SHA.

Key features:
- Triggers on pushes to main affecting relevant directories
- Uses GitHub Container Registry (GHCR) with automatic authentication
- Tags images with both the commit SHA and `latest`
- Includes optional auto-deploy step (commented out)

````yaml path=.github/workflows/frontend-ci.yml mode=EXCERPT
name: Build and Push All Services (API, Frontend, Uploader)

on:
  push:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - 'backend/**'
      - 'tools/file-uploader/**'
      - 'Dockerfile.frontend.ci'

jobs:
  build-and-push-all:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    steps:
      - name: Compute short SHA tag
        id: vars
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
      
      - name: Build and push API image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: backend/YendorCats.API/Dockerfile
          push: true
          tags: |
            ghcr.io/${{ github.repository_owner }}/yendorcats-api:${{ steps.vars.outputs.sha_short }}
            ghcr.io/${{ github.repository_owner }}/yendorcats-api:latest
````

### 2. Production Docker Compose
**File:** `docker-compose.production.yml`

Updated to use environment variables for registry, owner, and image tags. All services now pull from GHCR with SHA-based tags.

````yaml path=docker-compose.production.yml mode=EXCERPT
services:
  api:
    image: ${REGISTRY}/${OWNER}/yendorcats-api:${IMAGE_TAG:-latest}
    container_name: yendorcats-api-production
    # ... rest of config

  frontend:
    image: ${REGISTRY}/${OWNER}/yendorcats-frontend:${IMAGE_TAG:-latest}
    container_name: yendorcats-frontend-production
    # Using image-based frontend with cache-busted assets; volumes removed

  uploader:
    image: ${REGISTRY}/${OWNER}/yendorcats-uploader:${IMAGE_TAG:-latest}
    container_name: yendorcats-uploader-production
````

### 3. Environment Configuration
**File:** `.env.example`

Template for production environment variables including registry configuration.

````bash path=.env.example mode=EXCERPT
# GitHub Container Registry Configuration
REGISTRY=ghcr.io
OWNER=your-github-username-or-org

# Image tag - set this to the git commit SHA for cache-busted deployments
# Example: IMAGE_TAG=abc1234 (short SHA from git rev-parse --short HEAD)
IMAGE_TAG=latest

# Database Configuration
MYSQL_ROOT_PASSWORD=your-secure-root-password
MYSQL_USER=yendorcats_user
MYSQL_PASSWORD=your-secure-user-password

# JWT Secret for API authentication
YENDOR_JWT_SECRET=your-very-long-random-jwt-secret-key-here
````

### 4. Deployment Script
**File:** `deploy.sh`

Automated deployment script that pulls images and restarts services with proper error handling and status reporting.

````bash path=deploy.sh mode=EXCERPT
#!/bin/bash
# Deploy script for YendorCats production environment

set -e  # Exit on any error

# Source environment variables
source ".env"

# Validate required environment variables
if [ -z "$REGISTRY" ] || [ -z "$OWNER" ]; then
    echo "Error: REGISTRY and OWNER must be set in .env"
    exit 1
fi

# Pull latest images
echo "Pulling latest images..."
docker compose -f docker-compose.production.yml pull

# Start/restart services
echo "Starting services..."
docker compose -f docker-compose.production.yml up -d
````

---

## Setup Instructions

### Step 1: GitHub Container Registry Setup

1. **Enable GHCR for your repository:**
   - Go to your GitHub repository settings
   - Navigate to "Actions" → "General"
   - Ensure "Read and write permissions" are enabled for GITHUB_TOKEN

2. **Make packages public (optional but recommended for easier access):**
   - After first push, go to your GitHub profile → "Packages"
   - Find your yendorcats packages and change visibility to public

### Step 2: Server Setup

1. **Create environment file on your server:**
```bash
# Copy the example and fill in your values
cp .env.example .env
nano .env
```

2. **Set the required variables in `.env`:**
```bash
REGISTRY=ghcr.io
OWNER=your-github-username
IMAGE_TAG=latest  # Will be updated to specific SHAs for deployments

# Fill in your actual values for database, JWT, S3, etc.
MYSQL_ROOT_PASSWORD=your-secure-password
YENDOR_JWT_SECRET=your-jwt-secret
AWS_S3_BUCKET_NAME=your-bucket
# ... etc
```

3. **Ensure Docker and Docker Compose are installed:**
```bash
# Install Docker if not already installed
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo apt-get update
sudo apt-get install docker-compose-plugin
```

### Step 3: First Deployment

1. **Push to main branch** to trigger the CI/CD pipeline
2. **Check GitHub Actions** to see the build progress
3. **Note the commit SHA** from the Actions output
4. **Deploy on your server:**

```bash
# Set the specific commit SHA you want to deploy
export IMAGE_TAG=abc1234  # Replace with actual SHA from GitHub Actions

# Or update .env file
echo "IMAGE_TAG=abc1234" >> .env

# Run deployment
./deploy.sh
```

---

## How Cache-Busting Works

### Frontend Assets
The frontend Dockerfile automatically stamps HTML files with the git commit SHA:

**Before build (in HTML):**
```html
<link rel="stylesheet" href="css/main.css?v=__ASSET_VERSION__">
<script>window.__BUILD_VERSION__ = "__ASSET_VERSION__";</script>
```

**After build (stamped with SHA):**
```html
<link rel="stylesheet" href="css/main.css?v=abc1234">
<script>window.__BUILD_VERSION__ = "abc1234";</script>
```

### Browser Behavior
- **Same SHA**: Browser uses cached assets (fast)
- **New SHA**: Browser fetches fresh assets once, then caches them
- **Result**: Perfect cache efficiency with automatic invalidation on changes

---

## Daily Workflow

### For Developers
1. **Make changes** to frontend, backend, or uploader
2. **Commit and push** to main branch
3. **GitHub Actions automatically:**
   - Builds new images tagged with commit SHA
   - Pushes to GHCR
   - Reports the new tag in Actions output

### For Deployment
1. **Check GitHub Actions** for the latest successful build
2. **Note the commit SHA** from the build output
3. **Deploy specific version:**

```bash
# Method 1: Environment variable
export IMAGE_TAG=abc1234
./deploy.sh

# Method 2: Update .env file
sed -i 's/IMAGE_TAG=.*/IMAGE_TAG=abc1234/' .env
./deploy.sh

# Method 3: One-liner
IMAGE_TAG=abc1234 docker compose -f docker-compose.production.yml pull && \
IMAGE_TAG=abc1234 docker compose -f docker-compose.production.yml up -d
```

---

## Verification and Troubleshooting

### Verify Deployment
1. **Check running containers:**
```bash
docker compose -f docker-compose.production.yml ps
```

2. **Verify image tags:**
```bash
docker images | grep yendorcats
```

3. **Check frontend version in browser:**
   - Open browser console
   - Type: `window.__BUILD_VERSION__`
   - Should show your commit SHA

4. **Verify cache-busting:**
   - View page source
   - CSS/JS URLs should include `?v=abc1234`

### Common Issues

**Images not pulling:**
- Check GHCR package visibility (make public if needed)
- Verify REGISTRY and OWNER in .env
- Ensure Docker is logged into GHCR: `docker login ghcr.io`

**Old assets loading:**
- Hard refresh browser (Ctrl+F5)
- Check that IMAGE_TAG matches the deployed SHA
- Verify window.__BUILD_VERSION__ shows expected SHA

**Services not starting:**
- Check logs: `docker compose -f docker-compose.production.yml logs`
- Verify all environment variables in .env
- Ensure database is healthy: `docker compose -f docker-compose.production.yml ps`

---

## Advanced Usage

### Rollback to Previous Version
```bash
# Find previous commit SHA
git log --oneline -10

# Deploy previous version
export IMAGE_TAG=def5678  # Previous commit SHA
./deploy.sh
```

### Manual Build (if needed)
```bash
# Build locally with current commit SHA
export SHA=$(git rev-parse --short HEAD)

# Build all images
docker build -t ghcr.io/your-username/yendorcats-api:$SHA -f backend/YendorCats.API/Dockerfile .
docker build -t ghcr.io/your-username/yendorcats-frontend:$SHA -f Dockerfile.frontend.ci .
docker build -t ghcr.io/your-username/yendorcats-uploader:$SHA -f tools/file-uploader/Dockerfile tools/file-uploader

# Push to registry
docker push ghcr.io/your-username/yendorcats-api:$SHA
docker push ghcr.io/your-username/yendorcats-frontend:$SHA
docker push ghcr.io/your-username/yendorcats-uploader:$SHA
```

### Enable Auto-Deploy (Optional)
Uncomment the deploy job in `.github/workflows/frontend-ci.yml` and add these secrets to your GitHub repository:

- `DEPLOY_HOST`: Your server's IP or hostname
- `DEPLOY_USER`: SSH username
- `DEPLOY_SSH_KEY`: Private SSH key for authentication

---

## Cost and Performance Benefits

### Bandwidth Savings
- **Aggressive caching**: Static assets cached until SHA changes
- **Reduced origin requests**: Only new commits trigger asset downloads
- **CDN-friendly**: Query parameters work well with most CDNs

### Operational Benefits
- **Immutable deployments**: Each SHA represents a specific, reproducible state
- **Easy rollbacks**: Deploy any previous commit instantly
- **Clear traceability**: Know exactly which code is running
- **Zero manual versioning**: Git provides the version automatically

---

## Next Steps and Customization

### Add More Services
To add additional services to the CI/CD pipeline:

1. **Add build step** to `.github/workflows/frontend-ci.yml`
2. **Add service** to `docker-compose.production.yml` with `${REGISTRY}/${OWNER}/service-name:${IMAGE_TAG:-latest}`
3. **Update deploy script** if needed

### Switch to Different Registry
To use Docker Hub or AWS ECR instead of GHCR:

1. **Update REGISTRY** in .env: `REGISTRY=docker.io` or `REGISTRY=123456789.dkr.ecr.us-east-1.amazonaws.com`
2. **Update login step** in GitHub Actions workflow
3. **Add appropriate secrets** for authentication

### Add Staging Environment
Create `docker-compose.staging.yml` and separate `.env.staging` for testing deployments before production.

---

This setup provides a robust, automated CI/CD pipeline with proper cache-busting and version management. Every commit creates a deployable artifact, and deployments are fast, traceable, and easily reversible.

---
