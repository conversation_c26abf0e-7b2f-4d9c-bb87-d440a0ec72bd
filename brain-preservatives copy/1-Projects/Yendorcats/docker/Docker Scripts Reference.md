---
title: "Docker Scripts Reference - YendorCats"
tags: [docker, scripts, automation, deployment, cicd, bash, utilities]
aliases: [docker-scripts, automation-scripts, deployment-scripts]
created: 2025-07-27
updated: 2025-07-27
type: reference
status: complete
---

# Docker Scripts Reference

> **Navigation**: [[README]] | [[Docker Deployment Guide]] | [[Docker CI-CD Pipeline]]

---

## 📋 Script Overview

The YendorCats project includes several automation scripts for Docker deployment and management. These scripts provide consistent, reliable deployment processes and reduce manual errors.

| Script | Purpose | Environment | Status |
|--------|---------|-------------|--------|
| `deploy-staging.sh` | [[#Staging Deployment Script]] | Staging | ✅ Active |
| `deploy-ecr.sh` | [[#ECR Deployment Script]] | Production | ✅ Ready |
| `health-check.sh` | [[#Health Check Script]] | All | 🔄 Planned |
| `backup-data.sh` | [[#Backup Script]] | All | 🔄 Planned |

---

## 🚀 Staging Deployment Script

### `deploy-staging.sh`

**Purpose**: Automated staging environment deployment with validation and health checks.

**Location**: `./deploy-staging.sh`

**Usage**:
```bash
# Make executable (first time)
chmod +x deploy-staging.sh

# Run deployment
./deploy-staging.sh
```

### Script Features
- ✅ **Environment Validation**: Checks required variables and dependencies
- ✅ **Image Building**: Builds all container images with error handling
- ✅ **Service Deployment**: Starts services with proper dependency order
- ✅ **Health Verification**: Tests all endpoints and service connectivity
- ✅ **Status Reporting**: Provides clear success/failure feedback

### Script Flow
```mermaid
graph TD
    A[Start] --> B[Validate Environment]
    B --> C[Check Docker/Compose]
    C --> D[Stop Existing Containers]
    D --> E[Build Images]
    E --> F[Start Services]
    F --> G[Wait for Startup]
    G --> H[Health Checks]
    H --> I{All Healthy?}
    I -->|Yes| J[Success Report]
    I -->|No| K[Error Report]
    J --> L[End]
    K --> L
```

### Configuration
```bash
# Required environment file
.env.staging

# Required variables checked:
# - AWS_S3_BUCKET_NAME
# - AWS_S3_ACCESS_KEY  
# - AWS_S3_SECRET_KEY
# - YENDOR_JWT_SECRET
```

### Output Example
```bash
🚀 YendorCats Staging Deployment
=================================
[SUCCESS] Found .env.staging configuration file
[SUCCESS] All required environment variables are set
[SUCCESS] Docker and Docker Compose are available
[INFO] Building staging images...
[SUCCESS] Images built successfully
[INFO] Starting staging services...
[SUCCESS] Services started successfully
[SUCCESS] Frontend is responding
[SUCCESS] API is responding  
[SUCCESS] File uploader is responding
[SUCCESS] 🎉 Staging deployment completed successfully!
```

### Troubleshooting
```bash
# View script logs
./deploy-staging.sh 2>&1 | tee deployment.log

# Debug mode (if implemented)
DEBUG=1 ./deploy-staging.sh

# Manual verification after script
docker-compose -f docker-compose.staging.yml ps
curl http://localhost/health
```

---

## 🏭 ECR Deployment Script

### `deploy-ecr.sh`

**Purpose**: Tags and pushes staging images to Amazon ECR for production deployment.

**Location**: `./deploy-ecr.sh`

**Prerequisites**:
- AWS CLI configured
- ECR repositories created
- Staging images built

**Usage**:
```bash
# Set AWS account ID
export AWS_ACCOUNT_ID=************

# Run ECR deployment
./deploy-ecr.sh
```

### Script Features
- ✅ **AWS Authentication**: Handles ECR login automatically
- ✅ **Repository Management**: Creates ECR repositories if needed
- ✅ **Image Tagging**: Tags local images for ECR
- ✅ **Batch Upload**: Pushes all services to ECR
- ✅ **Verification**: Confirms successful uploads

### Required Environment Variables
```bash
# Required
AWS_ACCOUNT_ID=************

# Optional (with defaults)
AWS_REGION=us-west-2
```

### ECR Repository Structure
```
************.dkr.ecr.us-west-2.amazonaws.com/
├── yendorcats/frontend:staging
├── yendorcats/api:staging
└── yendorcats/uploader:staging
```

### Script Output
```bash
🚀 YendorCats ECR Deployment
============================
[SUCCESS] Using AWS Account ID: ************
[SUCCESS] Successfully logged in to ECR
[SUCCESS] Repository yendorcats/frontend already exists
[INFO] Tagging yendorcats/frontend:staging...
[INFO] Pushing ************.dkr.ecr.us-west-2.amazonaws.com/yendorcats/frontend:staging...
[SUCCESS] Successfully pushed frontend image
[SUCCESS] 🎉 ECR deployment completed successfully!
```

---

## 🏥 Health Check Script

### `health-check.sh` (Planned)

**Purpose**: Comprehensive health monitoring for all services.

**Planned Features**:
- Service endpoint testing
- Database connectivity checks
- Storage service validation
- Performance metrics collection
- Alert generation for failures

**Usage** (Planned):
```bash
# Quick health check
./health-check.sh

# Detailed health report
./health-check.sh --detailed

# Continuous monitoring
./health-check.sh --monitor --interval=30
```

**Implementation Status**: 🔄 Planned for next iteration

---

## 💾 Backup Script

### `backup-data.sh` (Planned)

**Purpose**: Automated backup of application data and configuration.

**Planned Features**:
- Database backup (SQLite)
- Configuration backup
- Log archival
- S3 backup upload
- Retention policy management

**Usage** (Planned):
```bash
# Full backup
./backup-data.sh

# Database only
./backup-data.sh --database-only

# Upload to S3
./backup-data.sh --upload-s3
```

**Implementation Status**: 🔄 Planned for production deployment

---

## 🛠 Script Development Guidelines

### Script Structure Template
```bash
#!/bin/bash

# Script Name and Purpose
# Description of what the script does

set -e  # Exit on any error

# Color definitions for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Output functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Main script logic here
```

### Best Practices
- **Error Handling**: Use `set -e` and check return codes
- **Logging**: Provide clear, colored output for different message types
- **Validation**: Check prerequisites before executing main logic
- **Idempotency**: Scripts should be safe to run multiple times
- **Documentation**: Include usage examples and troubleshooting

---

## 🔧 Utility Functions

### Common Script Functions
```bash
# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Wait for service to be ready
wait_for_service() {
    local url=$1
    local timeout=${2:-60}
    local count=0
    
    while [ $count -lt $timeout ]; do
        if curl -f -s "$url" >/dev/null; then
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    return 1
}

# Validate environment variable
validate_env_var() {
    local var_name=$1
    if [ -z "${!var_name}" ]; then
        print_error "Required environment variable $var_name is not set"
        return 1
    fi
}
```

### Docker Helper Functions
```bash
# Check if container is running
is_container_running() {
    docker ps --format '{{.Names}}' | grep -q "^$1$"
}

# Get container health status
get_container_health() {
    docker inspect --format='{{.State.Health.Status}}' "$1" 2>/dev/null || echo "no-health-check"
}

# Wait for container to be healthy
wait_for_healthy() {
    local container=$1
    local timeout=${2:-60}
    local count=0
    
    while [ $count -lt $timeout ]; do
        local health=$(get_container_health "$container")
        if [ "$health" = "healthy" ]; then
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    return 1
}
```

---

## 🔄 CI/CD Integration

### GitHub Actions Integration
```yaml
# .github/workflows/deploy-staging.yml
name: Deploy Staging
on:
  push:
    branches: [develop]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Staging
        run: ./deploy-staging.sh
        env:
          AWS_S3_BUCKET_NAME: ${{ secrets.STAGING_S3_BUCKET }}
          AWS_S3_ACCESS_KEY: ${{ secrets.STAGING_S3_ACCESS_KEY }}
          AWS_S3_SECRET_KEY: ${{ secrets.STAGING_S3_SECRET_KEY }}
          YENDOR_JWT_SECRET: ${{ secrets.STAGING_JWT_SECRET }}
```

### Jenkins Integration
```groovy
pipeline {
    agent any
    
    environment {
        AWS_S3_BUCKET_NAME = credentials('staging-s3-bucket')
        YENDOR_JWT_SECRET = credentials('staging-jwt-secret')
    }
    
    stages {
        stage('Deploy Staging') {
            steps {
                sh './deploy-staging.sh'
            }
        }
        
        stage('Health Check') {
            steps {
                sh './health-check.sh'
            }
        }
    }
}
```

---

## 📊 Script Monitoring

### Logging Integration
```bash
# Add to scripts for centralized logging
log_to_file() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> /var/log/yendorcats-deployment.log
}

# Usage in scripts
print_status "Starting deployment"
log_to_file "Deployment started by $(whoami)"
```

### Metrics Collection
```bash
# Track deployment metrics
DEPLOYMENT_START=$(date +%s)

# At end of script
DEPLOYMENT_END=$(date +%s)
DEPLOYMENT_DURATION=$((DEPLOYMENT_END - DEPLOYMENT_START))
echo "Deployment completed in ${DEPLOYMENT_DURATION} seconds"
```

---

## 🎯 Script Customization

### Environment-Specific Scripts
```bash
# Create environment-specific versions
cp deploy-staging.sh deploy-production.sh

# Modify for production
sed -i 's/staging/production/g' deploy-production.sh
sed -i 's/Staging/Production/g' deploy-production.sh
```

### Custom Hooks
```bash
# Add custom hooks to scripts
if [ -f "./hooks/pre-deploy.sh" ]; then
    print_status "Running pre-deployment hooks"
    ./hooks/pre-deploy.sh
fi

# Main deployment logic here

if [ -f "./hooks/post-deploy.sh" ]; then
    print_status "Running post-deployment hooks"
    ./hooks/post-deploy.sh
fi
```

---

## 🏷 Script Categories

**Deployment**: #deployment #staging #production #automation
**Maintenance**: #backup #health-check #monitoring #cleanup
**CI/CD**: #continuous-integration #continuous-deployment #pipeline
**Utilities**: #helper-functions #validation #logging #metrics

---

> **Related**: [[Docker Deployment Guide]] | [[Docker CI-CD Pipeline]] | [[Docker Quick Reference]]
