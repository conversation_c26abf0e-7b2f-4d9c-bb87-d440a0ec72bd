graph TD
    subgraph "Testing Environment"
        api[API Service<br>yendorcats-test-api]
        uploader[File Uploader<br>yendorcats-test-uploader]
        minio[MinIO S3<br>yendorcats-test-minio]
        nginx[Nginx Frontend<br>yendorcats-test-nginx]
        
        nginx --> |Static Files| Client
        api --> |REST API| Client
        uploader --> |File Upload| Client
        
        api --> |Store/Retrieve Data| SQLite[(SQLite<br>Test Database)]
        api --> |Object Storage| minio
        uploader --> |Store Files| minio
    end
    
    subgraph "Volumes"
        SQLite --- db_vol[api-test-data]
        api --- logs_vol[api-test-logs]
        minio --- minio_vol[minio-test-data]
    end
    
    subgraph "Networks"
        api --- uploader
        api --- nginx
        uploader --- minio
    end

