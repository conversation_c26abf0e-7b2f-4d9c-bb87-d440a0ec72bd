---
creation_date: 2025-08-12
modification_date: 2025-08-12
type: project
status: active
priority: medium
deadline: 2025-09-11
tags:
  - para/projects
  - aws
  - ecr
  - docker
  - compose
  - ci-cd
  - manual
  - iam
  - step-by-step
  - actions
  - github
  - deployment
  - deploy
  - ci
  - automate
  - automatic
---

# Manual Setup Guide: Understanding ECR CI/CD Without Scripts

I'll walk you through each step manually so you understand exactly how the ECR CI/CD pipeline works. Since you have an IAM role on your instance, we can skip access keys for the server side.

---
## Part 1: Manual ECR Repository Creation

### Step 1: Create ECR Repositories
Instead of using the script, create each repository manually ==**ATTN**== Shouldn't region be ap-southeast-2:

```bash
# Create API repository
aws ecr create-repository \
    --repository-name yendorcats-api \
    --region us-west-004 \
    --image-scanning-configuration scanOnPush=true \
    --encryption-configuration encryptionType=AES256

# Create Frontend repository  
aws ecr create-repository \
    --repository-name yendorcats-frontend \
    --region us-west-004 \
    --image-scanning-configuration scanOnPush=true \
    --encryption-configuration encryptionType=AES256

# Create Uploader repository
aws ecr create-repository \
    --repository-name yendorcats-uploader \
    --region us-west-004 \
    --image-scanning-configuration scanOnPush=true \
    --encryption-configuration encryptionType=AES256
```

### Step 2: Set Lifecycle Policies (Optional but Recommended)
This prevents your ECR from filling up with old images:

```bash
# Create lifecycle policy for API
aws ecr put-lifecycle-policy \
    --repository-name yendorcats-api \
    --region us-west-004 \
    --lifecycle-policy-text '{
        "rules": [
            {
                "rulePriority": 1,
                "description": "Keep last 10 images",
                "selection": {
                    "tagStatus": "any",
                    "countType": "imageCountMoreThan",
                    "countNumber": 10
                },
                "action": {
                    "type": "expire"
                }
            }
        ]
    }'

# Repeat for frontend and uploader (same policy)
aws ecr put-lifecycle-policy --repository-name yendorcats-frontend --region us-west-004 --lifecycle-policy-text '{"rules":[{"rulePriority":1,"description":"Keep last 10 images","selection":{"tagStatus":"any","countType":"imageCountMoreThan","countNumber":10},"action":{"type":"expire"}}]}'

aws ecr put-lifecycle-policy --repository-name yendorcats-uploader --region us-west-004 --lifecycle-policy-text '{"rules":[{"rulePriority":1,"description":"Keep last 10 images","selection":{"tagStatus":"any","countType":"imageCountMoreThan","countNumber":10},"action":{"type":"expire"}}]}'
```

### Step 3: Get Your ECR Registry URL
```bash
# Get your AWS account ID
aws sts get-caller-identity --query Account --output text

# Your ECR registry will be:
# {ACCOUNT_ID}.dkr.ecr.us-west-004.amazonaws.com
```

---

## Part 2: GitHub Secrets Setup

### What the GitHub Actions Workflow Needs
The workflow needs these secrets to push to ECR:

1. **AWS_ACCOUNT_ID**: Your 12-digit AWS account ID
2. **AWS_ACCESS_KEY_ID**: Access key for a user with ECR permissions
3. **AWS_SECRET_ACCESS_KEY**: Secret key for that user

### Manual GitHub Secrets Setup
1. Go to your GitHub repository
2. Click **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret** for each:

```
Name: AWS_ACCOUNT_ID
Value: ************  (your actual account ID)

Name: AWS_ACCESS_KEY_ID  
Value: AKIA...  (your access key)

Name: AWS_SECRET_ACCESS_KEY
Value: abc123...  (your secret key)
```

### Understanding the Workflow Authentication
````yaml path=.github/workflows/frontend-ci.yml mode=EXCERPT
- name: Configure AWS credentials
  uses: aws-actions/configure-aws-credentials@v4
  with:
    aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
    aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    aws-region: us-west-004

- name: Login to Amazon ECR
  uses: aws-actions/amazon-ecr-login@v2
````

**What this does:**
1. Configures AWS CLI in the GitHub runner with your credentials
2. Gets a temporary ECR login token
3. Logs Docker into your ECR registry
4. Now `docker push` commands work to your ECR

---

## Part 3: Understanding the Build Process

### How Images Get Tagged
````yaml path=.github/workflows/frontend-ci.yml mode=EXCERPT
- name: Compute short SHA tag
  id: vars
  run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

- name: Build and push API image
  uses: docker/build-push-action@v5
  with:
    context: .
    file: backend/YendorCats.API/Dockerfile
    push: true
    tags: |
      ${{ env.ECR_REGISTRY }}/yendorcats-api:${{ steps.vars.outputs.sha_short }}
      ${{ env.ECR_REGISTRY }}/yendorcats-api:latest
````

**What happens:**
1. `git rev-parse --short HEAD` gets the current commit SHA (e.g., "abc1234")
2. Each image gets tagged with both the SHA and "latest"
3. Example tags: `************.dkr.ecr.us-west-004.amazonaws.com/yendorcats-api:abc1234`

### Manual Build (to understand the process)
You can build and push manually to see how it works:

```bash
# Get current commit SHA
SHA=$(git rev-parse --short HEAD)
echo "Current SHA: $SHA"

# Set your ECR registry (replace with your account ID)
ECR_REGISTRY="************.dkr.ecr.us-west-004.amazonaws.com"

# Login to ECR
aws ecr get-login-password --region us-west-004 | docker login --username AWS --password-stdin $ECR_REGISTRY

# Build and tag API image
docker build -t $ECR_REGISTRY/yendorcats-api:$SHA -f backend/YendorCats.API/Dockerfile .
docker build -t $ECR_REGISTRY/yendorcats-api:latest -f backend/YendorCats.API/Dockerfile .

# Build and tag frontend image (with cache-busting)
docker build -t $ECR_REGISTRY/yendorcats-frontend:$SHA -f Dockerfile.frontend.ci .
docker build -t $ECR_REGISTRY/yendorcats-frontend:latest -f Dockerfile.frontend.ci .

# Build and tag uploader image
docker build -t $ECR_REGISTRY/yendorcats-uploader:$SHA -f tools/file-uploader/Dockerfile tools/file-uploader
docker build -t $ECR_REGISTRY/yendorcats-uploader:latest -f tools/file-uploader/Dockerfile tools/file-uploader

# Push all images
docker push $ECR_REGISTRY/yendorcats-api:$SHA
docker push $ECR_REGISTRY/yendorcats-api:latest
docker push $ECR_REGISTRY/yendorcats-frontend:$SHA
docker push $ECR_REGISTRY/yendorcats-frontend:latest
docker push $ECR_REGISTRY/yendorcats-uploader:$SHA
docker push $ECR_REGISTRY/yendorcats-uploader:latest
```

---

## Part 4: Server-Side Deployment (Manual)

### Step 1: Create .env File
Since you have an IAM role, you don't need AWS credentials in the .env:

```bash
# Create .env file
cat > .env << 'EOF'
# AWS ECR Configuration
ECR_REGISTRY=************.dkr.ecr.us-west-004.amazonaws.com
IMAGE_TAG=latest

# Database Configuration
MYSQL_ROOT_PASSWORD=your-secure-root-password
MYSQL_USER=yendorcats_user
MYSQL_PASSWORD=your-secure-user-password

# JWT Secret
YENDOR_JWT_SECRET=your-very-long-random-jwt-secret-key-here

# S3/Backblaze B2 Configuration
AWS_S3_BUCKET_NAME=your-bucket-name
AWS_S3_ACCESS_KEY=your_b2_key_id_here
AWS_S3_SECRET_KEY=your_b2_application_key_here

# Backblaze B2 specific
B2_APPLICATION_KEY_ID=your-b2-key-id
B2_APPLICATION_KEY=your-b2-application-key
B2_BUCKET_ID=your-b2-bucket-id
EOF
```

### Step 2: Manual ECR Login (Understanding)
```bash
# Load environment variables
source .env

# Login to ECR using IAM role (no access keys needed)
aws ecr get-login-password --region us-west-004 | docker login --username AWS --password-stdin $ECR_REGISTRY

# Verify login worked
docker pull $ECR_REGISTRY/yendorcats-api:latest
```

**What happens:**
1. `aws ecr get-login-password` uses your IAM role to get a temporary token
2. `docker login` authenticates Docker with ECR using that token
3. Now Docker can pull/push to your ECR repositories

### Step 3: Manual Deployment Process
```bash
# Set specific commit SHA you want to deploy
export IMAGE_TAG=abc1234  # Replace with actual SHA from GitHub Actions

# Pull the specific images
docker pull $ECR_REGISTRY/yendorcats-api:$IMAGE_TAG
docker pull $ECR_REGISTRY/yendorcats-frontend:$IMAGE_TAG
docker pull $ECR_REGISTRY/yendorcats-uploader:$IMAGE_TAG

# Stop current containers
docker compose -f docker-compose.production.yml down

# Start with new images
docker compose -f docker-compose.production.yml up -d

# Check status
docker compose -f docker-compose.production.yml ps
```

---

## Part 5: Understanding Cache-Busting

### How Frontend Cache-Busting Works

**1. HTML Placeholders (before build):**
````html path=frontend/index.html mode=EXCERPT
<!-- Build Version: __ASSET_VERSION__ -->
<script>window.__BUILD_VERSION__ = "__ASSET_VERSION__";</script>
<link rel="stylesheet" href="css/main.css?v=__ASSET_VERSION__">
<script src="js/carousel.js?v=__ASSET_VERSION__"></script>
````

**2. Dockerfile Stamping Process:**
````dockerfile path=Dockerfile.frontend.ci mode=EXCERPT
# Stage 1: Get git SHA
FROM alpine:3.19 AS version
RUN apk add --no-cache git
COPY .git .git
RUN git rev-parse --short HEAD > /version.txt

# Stage 2: Stamp HTML files
FROM nginx:alpine AS production
COPY frontend/ /usr/share/nginx/html/
COPY --from=version /version.txt /version.txt
RUN ASSET_VERSION=$(cat /version.txt) && \
    find /usr/share/nginx/html -name "*.html" -print0 | \
    xargs -0 -I {} sh -c 'sed -i "s/__ASSET_VERSION__/'"${ASSET_VERSION}"'/g" "{}"'
````

**3. Result (after build with SHA abc1234):**
```html
<!-- Build Version: abc1234 -->
<script>window.__BUILD_VERSION__ = "abc1234";</script>
<link rel="stylesheet" href="css/main.css?v=abc1234">
<script src="js/carousel.js?v=abc1234">
```

**4. Browser Behavior:**
- Same SHA = browser uses cached files (fast)
- New SHA = browser downloads fresh files once, then caches them
- Perfect cache efficiency with automatic invalidation

---

## Part 6: Manual Verification

### Check ECR Repositories
```bash
# List your repositories
aws ecr describe-repositories --region us-west-004

# List images in a specific repository
aws ecr list-images --repository-name yendorcats-frontend --region us-west-004

# Get image details
aws ecr describe-images --repository-name yendorcats-frontend --region us-west-004
```

### Check Running Deployment
```bash
# Check container status
docker compose -f docker-compose.production.yml ps

# Check which image versions are running
docker images | grep yendorcats

# Check logs
docker compose -f docker-compose.production.yml logs frontend
```

### Verify Cache-Busting in Browser
1. Open your website
2. Open browser console (F12)
3. Type: `window.__BUILD_VERSION__`
4. Should show your commit SHA
5. View page source - CSS/JS URLs should have `?v=abc1234`

---

## Part 7: Complete Manual Workflow

### When You Make Changes:
1. **Commit and push to main**
2. **GitHub Actions runs automatically** (check Actions tab)
3. **Note the SHA** from the Actions output
4. **Deploy manually:**

```bash
# On your server
source .env

# Login to ECR
aws ecr get-login-password --region us-west-004 | docker login --username AWS --password-stdin $ECR_REGISTRY

# Set the new SHA (from GitHub Actions)
export IMAGE_TAG=def5678

# Pull new images
docker pull $ECR_REGISTRY/yendorcats-api:$IMAGE_TAG
docker pull $ECR_REGISTRY/yendorcats-frontend:$IMAGE_TAG
docker pull $ECR_REGISTRY/yendorcats-uploader:$IMAGE_TAG

# Update and restart
docker compose -f docker-compose.production.yml pull
docker compose -f docker-compose.production.yml up -d

# Verify
docker compose -f docker-compose.production.yml ps
```

### To Rollback:
```bash
# Use a previous SHA
export IMAGE_TAG=abc1234  # Previous working version
docker compose -f docker-compose.production.yml pull
docker compose -f docker-compose.production.yml up -d
```

---

## Understanding the Complete Flow

1. **Developer pushes code** → triggers GitHub Actions
2. **GitHub Actions:**
   - Checks out code with .git folder
   - Computes git SHA
   - Builds 3 Docker images tagged with SHA
   - Stamps frontend HTML with SHA for cache-busting
   - Pushes images to ECR
3. **Server deployment:**
   - Uses IAM role to authenticate with ECR
   - Pulls specific SHA-tagged images
   - Restarts containers with new images
4. **Browser:**
   - Downloads assets with new SHA query parameters
   - Caches them until next SHA change

This gives you immutable, traceable deployments with perfect cache-busting, all tied to your git commits.

---

## Related
- [[1-Projects]]
