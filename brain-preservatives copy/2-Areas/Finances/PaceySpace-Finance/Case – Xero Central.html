<!doctype html>
<html>
    <head>
        <title>Case – Xero Central</title>
        <meta charset='utf-8'/>
        <style>
 .ͼ1.cm-focused {outline: 1px dotted #212121;}
.ͼ1 {position: relative !important; box-sizing: border-box; display: flex !important; flex-direction: column;}
.ͼ1 .cm-scroller {display: flex !important; align-items: flex-start !important; font-family: monospace; line-height: 1.4; height: 100%; overflow-x: auto; position: relative; z-index: 0; overflow-anchor: none;}
.ͼ1 .cm-content[contenteditable=true] {-webkit-user-modify: read-write-plaintext-only;}
.ͼ1 .cm-content {margin: 0; flex-grow: 2; flex-shrink: 0; display: block; white-space: pre; word-wrap: normal; box-sizing: border-box; min-height: 100%; padding: 4px 0; outline: none;}
.ͼ1 .cm-lineWrapping {white-space: pre-wrap; white-space: break-spaces; word-break: break-word; overflow-wrap: anywhere; flex-shrink: 1;}
.ͼ2 .cm-content {caret-color: black;}
.ͼ3 .cm-content {caret-color: white;}
.ͼ1 .cm-line {display: block; padding: 0 2px 0 6px;}
.ͼ1 .cm-layer > * {position: absolute;}
.ͼ1 .cm-layer {position: absolute; left: 0; top: 0; contain: size style;}
.ͼ2 .cm-selectionBackground {background: #d9d9d9;}
.ͼ3 .cm-selectionBackground {background: #222;}
.ͼ2.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #d7d4f0;}
.ͼ3.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #233;}
.ͼ1 .cm-cursorLayer {pointer-events: none;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer {animation: steps(1) cm-blink 1.2s infinite;}
@keyframes cm-blink {50% {opacity: 0;}}
@keyframes cm-blink2 {50% {opacity: 0;}}
.ͼ1 .cm-cursor, .ͼ1 .cm-dropCursor {border-left: 1.2px solid black; margin-left: -0.6px; pointer-events: none;}
.ͼ1 .cm-cursor {display: none;}
.ͼ3 .cm-cursor {border-left-color: #ddd;}
.ͼ1 .cm-dropCursor {position: absolute;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor {display: block;}
.ͼ1 .cm-iso {unicode-bidi: isolate;}
.ͼ1 .cm-announced {position: fixed; top: -10000px;}
@media print {.ͼ1 .cm-announced {display: none;}}
.ͼ2 .cm-activeLine {background-color: #cceeff44;}
.ͼ3 .cm-activeLine {background-color: #99eeff33;}
.ͼ2 .cm-specialChar {color: red;}
.ͼ3 .cm-specialChar {color: #f78;}
.ͼ1 .cm-gutters {flex-shrink: 0; display: flex; height: 100%; box-sizing: border-box; z-index: 200;}
.ͼ1 .cm-gutters-before {inset-inline-start: 0;}
.ͼ1 .cm-gutters-after {inset-inline-end: 0;}
.ͼ2 .cm-gutters.cm-gutters-before {border-right-width: 1px;}
.ͼ2 .cm-gutters.cm-gutters-after {border-left-width: 1px;}
.ͼ2 .cm-gutters {background-color: #f5f5f5; color: #6c6c6c; border: 0px solid #ddd;}
.ͼ3 .cm-gutters {background-color: #333338; color: #ccc;}
.ͼ1 .cm-gutter {display: flex !important; flex-direction: column; flex-shrink: 0; box-sizing: border-box; min-height: 100%; overflow: hidden;}
.ͼ1 .cm-gutterElement {box-sizing: border-box;}
.ͼ1 .cm-lineNumbers .cm-gutterElement {padding: 0 3px 0 5px; min-width: 20px; text-align: right; white-space: nowrap;}
.ͼ2 .cm-activeLineGutter {background-color: #e2f2ff;}
.ͼ3 .cm-activeLineGutter {background-color: #222227;}
.ͼ1 .cm-panels {box-sizing: border-box; position: sticky; left: 0; right: 0; z-index: 300;}
.ͼ2 .cm-panels {background-color: #f5f5f5; color: black;}
.ͼ2 .cm-panels-top {border-bottom: 1px solid #ddd;}
.ͼ2 .cm-panels-bottom {border-top: 1px solid #ddd;}
.ͼ3 .cm-panels {background-color: #333338; color: white;}
.ͼ1 .cm-dialog label {font-size: 80%;}
.ͼ1 .cm-dialog {padding: 2px 19px 4px 6px; position: relative;}
.ͼ1 .cm-dialog-close {position: absolute; top: 3px; right: 4px; background-color: inherit; border: none; font: inherit; font-size: 14px; padding: 0;}
.ͼ1 .cm-tab {display: inline-block; overflow: hidden; vertical-align: bottom;}
.ͼ1 .cm-widgetBuffer {vertical-align: text-top; height: 1em; width: 0; display: inline;}
.ͼ1 .cm-placeholder {color: #888; display: inline-block; vertical-align: top; user-select: none;}
.ͼ1 .cm-highlightSpace {background-image: radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%); background-position: center;}
.ͼ1 .cm-highlightTab {background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>'); background-size: auto 100%; background-position: right 90%; background-repeat: no-repeat;}
.ͼ1 .cm-trailingSpace {background-color: #ff332255;}
.ͼ1 .cm-button {vertical-align: middle; color: inherit; font-size: 70%; padding: .2em 1em; border-radius: 1px;}
.ͼ2 .cm-button:active {background-image: linear-gradient(#b4b4b4, #d0d3d6);}
.ͼ2 .cm-button {background-image: linear-gradient(#eff1f5, #d9d9df); border: 1px solid #888;}
.ͼ3 .cm-button:active {background-image: linear-gradient(#111, #333);}
.ͼ3 .cm-button {background-image: linear-gradient(#393939, #111); border: 1px solid #888;}
.ͼ1 .cm-textfield {vertical-align: middle; color: inherit; font-size: 70%; border: 1px solid silver; padding: .2em .5em;}
.ͼ2 .cm-textfield {background-color: white;}
.ͼ3 .cm-textfield {border: 1px solid #555; background-color: inherit;}
.ͼ1 .cm-foldPlaceholder {background-color: #eee; border: 1px solid #ddd; color: #888; border-radius: .2em; margin: 0 1px; padding: 0 1px; cursor: pointer;}
.ͼ1 .cm-foldGutter span {padding: 0 1px; cursor: pointer;}
.ͼp .cm-vimMode .cm-cursorLayer:not(.cm-vimCursorLayer) {display: none;}
.ͼp .cm-vim-panel {padding: 0px 10px; font-family: monospace; min-height: 1.3em;}
.ͼp .cm-vim-panel input {background: transparent; border: none; outline: none;}
.ͼo .cm-vimMode .cm-line {caret-color: transparent !important;}
.ͼo .cm-fat-cursor {position: absolute; border: none; white-space: pre;}
.ͼo.cm-focused > .cm-scroller > .cm-cursorLayer > .cm-fat-cursor {background: var(--interactive-accent); color: var(--text-on-accent);}
.ͼo:not(.cm-focused) > .cm-scroller > .cm-cursorLayer > .cm-fat-cursor {color: transparent !important;}
 @keyframes loading {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.workspace-leaf-content[data-type="git-view"] .button-border {
    border: 2px solid var(--interactive-accent);
    border-radius: var(--radius-s);
}

.workspace-leaf-content[data-type="git-view"] .view-content {
    padding: 0;
}

.workspace-leaf-content[data-type="git-history-view"] .view-content {
    padding: 0;
}

.loading > svg {
    animation: 2s linear infinite loading;
    transform-origin: 50% 50%;
    display: inline-block;
}

.obsidian-git-center {
    margin: auto;
    text-align: center;
    width: 50%;
}

.obsidian-git-textarea {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.obsidian-git-disabled {
    opacity: 0.5;
}

.obsidian-git-center-button {
    display: block;
    margin: 20px auto;
}

.tooltip.mod-left {
    overflow-wrap: break-word;
}

.tooltip.mod-right {
    overflow-wrap: break-word;
}

/* Limits the scrollbar to the view body */
.git-view {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
}

.git-tools {
    display: flex;
    margin-left: auto;
}
.git-tools .type {
    padding-left: var(--size-2-1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
}

.git-tools .type[data-type="M"] {
    color: orange;
}
.git-tools .type[data-type="D"] {
    color: red;
}
.git-tools .buttons {
    display: flex;
}
.git-tools .buttons > * {
    padding: 0 0;
    height: auto;
}

.workspace-leaf-content[data-type="git-view"] .tree-item-self,
.workspace-leaf-content[data-type="git-history-view"] .tree-item-self {
    align-items: center;
}

.workspace-leaf-content[data-type="git-view"]
    .tree-item-self:hover
    .clickable-icon,
.workspace-leaf-content[data-type="git-history-view"]
    .tree-item-self:hover
    .clickable-icon {
    color: var(--icon-color-hover);
}

/* Highlight an item as active if it's diff is currently opened */
.is-active .git-tools .buttons > * {
    color: var(--nav-item-color-active);
}

.git-author {
    color: var(--text-accent);
}

.git-date {
    color: var(--text-accent);
}

.git-ref {
    color: var(--text-accent);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-d-none {
    display: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-wrapper {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header {
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--interactive-accent);
    font-family: var(--font-monospace);
    height: 35px;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header,
.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    font-size: 14px;
    margin-left: auto;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-added {
    border: 1px solid #b4e2b4;
    border-radius: 5px 0 0 5px;
    color: #399839;
    padding: 2px;
    text-align: right;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-deleted {
    border: 1px solid #e9aeae;
    border-radius: 0 5px 5px 0;
    color: #c33;
    margin-left: 1px;
    padding: 2px;
    text-align: left;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name-wrapper {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 15px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name {
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-wrapper {
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    margin-bottom: 1em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    cursor: pointer;
    display: none;
    font-size: 12px;
    justify-content: flex-end;
    padding: 4px 8px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse.d2h-selected {
    background-color: #c8e1ff;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse-input {
    margin: 0 4px 0 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-table {
    border-collapse: collapse;
    font-family: Menlo, Consolas, monospace;
    font-size: 13px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-files-diff {
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-diff {
    overflow-y: hidden;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-side-diff {
    display: inline-block;
    margin-bottom: -8px;
    margin-right: -4px;
    overflow-x: scroll;
    overflow-y: hidden;
    width: 50%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line {
    padding: 0 8em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    padding: 0 4.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-ctn {
    word-wrap: normal;
    background: none;
    display: inline-block;
    padding: 0;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    vertical-align: middle;
    white-space: pre;
    width: 100%;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #ffb6ba;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #8d232881;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line ins {
    border-radius: 0.2em;
    display: inline-block;
    margin-top: -1px;
    text-decoration: none;
    vertical-align: middle;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #97f295;
    text-align: left;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #1d921996;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix {
    word-wrap: normal;
    background: none;
    display: inline;
    padding: 0;
    white-space: pre;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1 {
    float: left;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1,
.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0 0.5em;
    text-overflow: ellipsis;
    width: 3.5em;
}

.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    float: right;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    position: absolute;
    text-align: right;
    width: 7.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    padding: 0 0.5em;
    position: absolute;
    text-align: right;
    text-overflow: ellipsis;
    width: 4em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-tbody tr {
    position: relative;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-emptyplaceholder,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    direction: rtl;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #fee8e9;
    border-color: #e9aeae;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: #dfd;
    border-color: #b4e2b4;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #521b1d83;
    border-color: #691d1d73;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: rgba(30, 71, 30, 0.5);
    border-color: #13501381;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-info {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
    color: var(--text-normal);
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #fdf2d0;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #55492480;
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: #ded;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: rgba(37, 78, 37, 0.418);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper {
    margin-bottom: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper a {
    color: #3572b0;
    text-decoration: none;
}

.workspace-leaf-content[data-type="diff-view"]
    .d2h-file-list-wrapper
    a:visited {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-header {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-title {
    font-weight: 700;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-line {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list {
    display: block;
    list-style: none;
    margin: 0;
    padding: 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li {
    border-bottom: 1px solid var(--background-modifier-border);
    margin: 0;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li:last-child {
    border-bottom: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-switch {
    cursor: pointer;
    display: none;
    font-size: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-icon {
    fill: currentColor;
    margin-right: 10px;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted {
    color: #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added {
    color: #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed {
    color: #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-tag {
    background-color: var(--background-primary);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 10px;
    margin-left: 5px;
    padding: 0 2px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted-tag {
    border: 2px solid #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added-tag {
    border: 1px solid #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed-tag {
    border: 1px solid #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved-tag {
    border: 1px solid #3572b0;
}

/* ====================== Line Authoring Information ====================== */

.cm-gutterElement.obs-git-blame-gutter {
    /* Add background color to spacing inbetween and around the gutter for better aesthetics */
    border-width: 0px 2px 0.2px 2px;
    border-style: solid;
    border-color: var(--background-secondary);
    background-color: var(--background-secondary);
}

.cm-gutterElement.obs-git-blame-gutter > div,
.line-author-settings-preview {
    /* delegate text color to settings */
    color: var(--obs-git-gutter-text);
    font-family: monospace;
    height: 100%; /* ensure, that age-based background color occupies entire parent */
    text-align: right;
    padding: 0px 6px 0px 6px;
    white-space: pre; /* Keep spaces and do not collapse them. */
}

@media (max-width: 800px) {
    /* hide git blame gutter not to superpose text */
    .cm-gutterElement.obs-git-blame-gutter {
        display: none;
    }
}

.git-unified-diff-view,
.git-split-diff-view .cm-deletedLine .cm-changedText {
    background-color: #ee443330;
}

.git-unified-diff-view,
.git-split-diff-view .cm-insertedLine .cm-changedText {
    background-color: #22bb2230;
}

.git-obscure-prompt[git-is-obscured="true"] #git-show-password:after {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg>');
}

.git-obscure-prompt[git-is-obscured="false"] #git-show-password:after {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-eye-off"><path d="M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49"></path><path d="M14.084 14.158a3 3 0 0 1-4.242-4.242"></path><path d="M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143"></path><path d="m2 2 20 20"></path></svg>');
}

/* Override styling of Codemirror merge view "collapsed lines" indicator */
.git-split-diff-view .ͼ2 .cm-collapsedLines {
    background: var(--interactive-normal);
    border-radius: var(--radius-m);
    color: var(--text-accent);
    font-size: var(--font-small);
    padding: var(--size-4-1) var(--size-4-1);
}
.git-split-diff-view .ͼ2 .cm-collapsedLines:hover {
    background: var(--interactive-hover);
    color: var(--text-accent-hover);
}
 .omnisearch-modal {
}

.omnisearch-result {
  white-space: normal;
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  flex-wrap: nowrap;
}

.omnisearch-result__title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 5px;
  flex-wrap: wrap;
}

.omnisearch-result__title {
  white-space: pre-wrap;
  align-items: center;
  display: flex;
  gap: 5px;
}

.omnisearch-result__title > span {
}

.omnisearch-result__folder-path {
  font-size: 0.75rem;
  align-items: center;
  display: flex;
  gap: 5px;
  color: var(--text-muted);
}

.omnisearch-result__extension {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__counter {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__body {
  white-space: normal;
  font-size: small;
  word-wrap: normal;

  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;

  color: var(--text-muted);
  margin-inline-start: 0.5em;
}

.omnisearch-result__embed {
  margin-left: 1em;
}


.omnisearch-result__image-container {
  flex-basis: 20%;
  text-align: end;
}

.omnisearch-highlight {
}

.omnisearch-default-highlight {
  text-decoration: underline;
  text-decoration-color: var(--text-highlight-bg);
  text-decoration-thickness: 3px;
  text-underline-offset: -1px;
  text-decoration-skip-ink: none;
}

.omnisearch-input-container {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 5px;
}

.omnisearch-result__icon {
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.omnisearch-result__icon svg {
  width: 100%;
  height: 100%;
}

.omnisearch-result__icon--emoji {
  font-size: 16px;
  vertical-align: middle;
  margin-right: 4px;
}

@media only screen and (max-width: 600px) {
  .omnisearch-input-container {
    flex-direction: column;
  }

  .omnisearch-input-container__buttons {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0 1em 0 1em;
    gap: 1em;
  }
  .omnisearch-input-container__buttons > button {
    flex-grow: 1;
  }
}

@media only screen and (min-width: 600px) {
  .omnisearch-input-container__buttons {
    margin-inline-end: 1em;
  }
}

.omnisearch-input-field {
  position: relative;
  flex-grow: 1;
}
 .templater_search {
    width: calc(100% - 20px);
}

.templater_div {
    border-top: 1px solid var(--background-modifier-border);
}

.templater_div > .setting-item {
    border-top: none !important;
    align-self: center;
}

.templater_div > .setting-item > .setting-item-control {
    justify-content: space-around;
    padding: 0;
    width: 100%;
}

.templater_div
    > .setting-item
    > .setting-item-control
    > .setting-editor-extra-setting-button {
    align-self: center;
}

.templater_donating {
    margin: 10px;
}

.templater_title {
    margin: 0;
    padding: 0;
    margin-top: 5px;
    text-align: center;
}

.templater_template {
    align-self: center;
    margin-left: 5px;
    margin-right: 5px;
    width: 70%;
}

.templater_cmd {
    margin-left: 5px;
    margin-right: 5px;
    font-size: 14px;
    width: 100%;
}

.templater_div2 > .setting-item {
    align-content: center;
    justify-content: center;
}

.templater-prompt-div {
    display: flex;
}

.templater-prompt-form {
    display: flex;
    flex-grow: 1;
}

.templater-prompt-input {
    flex-grow: 1;
}

.templater-button-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
}

textarea.templater-prompt-input {
    height: 10rem;
}

textarea.templater-prompt-input:focus {
    border-color: var(--interactive-accent);
}

.cm-s-obsidian .templater-command-bg {
    left: 0px;
    right: 0px;
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command {
    font-size: 0.85em;
    font-family: var(--font-monospace);
    line-height: 1.3;
}

.cm-s-obsidian .templater-inline .cm-templater-command {
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command.cm-templater-opening-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-closing-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-interpolation-tag {
    color: var(--code-property, #008bff);
}

.cm-s-obsidian .cm-templater-command.cm-templater-execution-tag {
    color: var(--code-function, #c0d700);
}

.cm-s-obsidian .cm-templater-command.cm-keyword {
    color: var(--code-keyword, #00a7aa);
    font-weight: normal;
}

.cm-s-obsidian .cm-templater-command.cm-atom {
    color: var(--code-normal, #f39b35);
}

.cm-s-obsidian .cm-templater-command.cm-value,
.cm-s-obsidian .cm-templater-command.cm-number,
.cm-s-obsidian .cm-templater-command.cm-type {
    color: var(--code-value, #a06fca);
}

.cm-s-obsidian .cm-templater-command.cm-def,
.cm-s-obsidian .cm-templater-command.cm-type.cm-def {
    color: var(--code-normal, var(--text-normal));
}

.cm-s-obsidian .cm-templater-command.cm-property,
.cm-s-obsidian .cm-templater-command.cm-property.cm-def,
.cm-s-obsidian .cm-templater-command.cm-attribute {
    color: var(--code-function, #98e342);
}

.cm-s-obsidian .cm-templater-command.cm-variable,
.cm-s-obsidian .cm-templater-command.cm-variable-2,
.cm-s-obsidian .cm-templater-command.cm-variable-3,
.cm-s-obsidian .cm-templater-command.cm-meta {
    color: var(--code-property, #d4d4d4);
}

.cm-s-obsidian .cm-templater-command.cm-callee,
.cm-s-obsidian .cm-templater-command.cm-operator,
.cm-s-obsidian .cm-templater-command.cm-qualifier,
.cm-s-obsidian .cm-templater-command.cm-builtin {
    color: var(--code-operator, #fc4384);
}

.cm-s-obsidian .cm-templater-command.cm-tag {
    color: var(--code-tag, #fc4384);
}

.cm-s-obsidian .cm-templater-command.cm-comment,
.cm-s-obsidian .cm-templater-command.cm-comment.cm-tag,
.cm-s-obsidian .cm-templater-command.cm-comment.cm-attribute {
    color: var(--code-comment, #696d70);
}

.cm-s-obsidian .cm-templater-command.cm-string,
.cm-s-obsidian .cm-templater-command.cm-string-2 {
    color: var(--code-string, #e6db74);
}

.cm-s-obsidian .cm-templater-command.cm-header,
.cm-s-obsidian .cm-templater-command.cm-hr {
    color: var(--code-keyword, #da7dae);
}

.cm-s-obsidian .cm-templater-command.cm-link {
    color: var(--code-normal, #696d70);
}

.cm-s-obsidian .cm-templater-command.cm-error {
    border-bottom: 1px solid #c42412;
}

.CodeMirror-hints {
    position: absolute;
    z-index: 10;
    overflow: hidden;
    list-style: none;

    margin: 0;
    padding: 2px;

    -webkit-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    border: 1px solid silver;

    background: white;
    font-size: 90%;
    font-family: monospace;

    max-height: 20em;
    overflow-y: auto;
}

.CodeMirror-hint {
    margin: 0;
    padding: 0 4px;
    border-radius: 2px;
    white-space: pre;
    color: black;
    cursor: pointer;
}

li.CodeMirror-hint-active {
    background: #08f;
    color: white;
}
 .block-language-dataview {
    overflow-y: auto;
}

/*****************/
/** Table Views **/
/*****************/

/* List View Default Styling; rendered internally as a table. */
.table-view-table {
    width: 100%;
}

.table-view-table > thead > tr, .table-view-table > tbody > tr {
    margin-top: 1em;
    margin-bottom: 1em;
    text-align: left;
}

.table-view-table > tbody > tr:hover {
    background-color: var(--table-row-background-hover);
}

.table-view-table > thead > tr > th {
    font-weight: 700;
    font-size: larger;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: solid;

    max-width: 100%;
}

.table-view-table > tbody > tr > td {
    text-align: left;
    border: none;
    font-weight: 400;
    max-width: 100%;
}

.table-view-table ul, .table-view-table ol {
    margin-block-start: 0.2em !important;
    margin-block-end: 0.2em !important;
}

/** Rendered value styling for any view. */
.dataview-result-list-root-ul {
    padding: 0em !important;
    margin: 0em !important;
}

.dataview-result-list-ul {
    margin-block-start: 0.2em !important;
    margin-block-end: 0.2em !important;
}

/** Generic grouping styling. */
.dataview.result-group {
    padding-left: 8px;
}

/*******************/
/** Inline Fields **/
/*******************/

.dataview.inline-field-key {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-primary-alt);
    color: var(--nav-item-color-selected);
}

.dataview.inline-field-value {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-secondary-alt);
    color: var(--nav-item-color-selected);
}

.dataview.inline-field-standalone-value {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-secondary-alt);
    color: var(--nav-item-color-selected);
}

/***************/
/** Task View **/
/***************/

.dataview.task-list-item, .dataview.task-list-basic-item {
    margin-top: 3px;
    margin-bottom: 3px;
    transition: 0.4s;
}

.dataview.task-list-item:hover, .dataview.task-list-basic-item:hover {
    background-color: var(--text-selection);
    box-shadow: -40px 0 0 var(--text-selection);
    cursor: pointer;
}

/*****************/
/** Error Views **/
/*****************/

div.dataview-error-box {
    width: 100%;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px dashed var(--background-secondary);
}

.dataview-error-message {
    color: var(--text-muted);
    text-align: center;
}

/*************************/
/** Additional Metadata **/
/*************************/

.dataview.small-text {
    font-size: smaller;
    color: var(--text-muted);
    margin-left: 3px;
}

.dataview.small-text::before {
	content: "(";
}

.dataview.small-text::after {
	content: ")";
}
 .periodic-modal {
  min-width: 40vw;
}

.settings-banner {
  background-color: var(--background-primary-alt);
  border-radius: 8px;
  border: 1px solid var(--background-modifier-border);
  margin-bottom: 1em;
  margin-top: 1em;
  padding: 1.5em;
  text-align: left;
}

.settings-banner h3 {
  margin-top: 0;
}

.settings-banner h4 {
  margin-bottom: 0.25em;
}

.has-error {
  color: var(--text-error);
}

input.has-error {
  color: var(--text-error);
  border-color: var(--text-error);
}
 .modal-button-container{display:flex;flex-direction:row-reverse;justify-content:flex-start;margin-top:1rem;gap:.5rem}.configureMacroDiv{display:grid;grid-template-rows:1fr;min-width:12rem}.configureMacroDivItem{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.configureMacroDivItemButton{display:flex;align-content:center;justify-content:center;margin-bottom:10px}.macroContainer{display:grid;grid-template-rows:repeat(auto-fill,120px);grid-gap:40px;overflow-y:auto;max-height:30em;padding:2em}@media screen and (max-width: 540px){.macroContainer1,.macroContainer2,.macroContainer3{grid-template-columns:repeat(1,1fr)}.wideInputPromptInputEl{width:20rem;max-width:100%;height:3rem;direction:inherit;text-align:inherit}}@media screen and (max-width: 540px) and (max-width: 780px){.macroContainer1{grid-template-columns:repeat(1,1fr)}.macroContainer2,.macroContainer3{grid-template-columns:repeat(2,1fr)}.wideInputPromptInputEl{width:30rem;max-width:100%;height:20rem;direction:inherit;text-align:inherit}}@media screen and (min-width: 781px){.macroContainer1{grid-template-columns:repeat(1,1fr)}.macroContainer2,.macroContainer3{grid-template-columns:repeat(2,1fr)}.wideInputPromptInputEl{width:40rem;max-width:100%;height:20rem;direction:inherit;text-align:inherit}}.addMacroBarContainer{display:flex;align-content:center;justify-content:space-around;margin-top:20px}.captureToActiveFileContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.choiceNameHeader{text-align:center}.choiceNameHeader:hover{cursor:pointer}.folderInputContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:8px;gap:4px}.selectMacroDropdownContainer{display:flex;align-content:center;justify-content:center}.quickAddModal .modal{min-width:35%;overflow-y:auto;max-height:70%}.checkboxRowContainer{margin:30px 0;display:grid;grid-template-rows:auto;align-content:center;gap:5px}.checkboxRow{display:flex;justify-content:space-between;align-content:center}.checkboxRow .checkbox-container{flex-shrink:0}.checkboxRow span{font-size:16px;word-break:break-all}.submitButtonContainer{display:flex;align-content:center;justify-content:center;gap:.5rem}.chooseFolderWhenCreatingNoteContainer{display:flex;align-content:center;justify-content:space-between;margin-bottom:10px}.chooseFolderFromSubfolderContainer{margin:20px 0 0}.clickable:hover{cursor:pointer}.quickAddCommandListItem{display:flex;flex:1 1 auto;align-items:center;justify-content:space-between}.quickCommandContainer{display:flex;justify-content:flex-end;align-content:center;margin-bottom:1em;gap:4px}.yesNoPromptButtonContainer{display:flex;align-items:center;justify-content:space-around;margin-top:2rem;gap:.5rem}.yesNoPromptParagraph{text-align:center}.suggestion-container{background-color:var(--modal-background);z-index:100000;overflow-y:auto}.qaFileSuggestionItem{display:flex;align-items:center;justify-content:space-between;padding:6px 12px;margin:1px 0;cursor:pointer;border-radius:3px;transition:all .12s cubic-bezier(.25,.46,.45,.94);position:relative;min-height:32px;font-size:13px;line-height:1.3;width:100%}.qaFileSuggestionItem:hover{background-color:var(--background-modifier-hover);transform:translate(2px)}.qaFileSuggestionItem.is-selected{background-color:var(--interactive-accent);color:var(--text-on-accent);box-shadow:0 2px 8px #0000001a}.qaFileSuggestionItem.is-selected .suggestion-sub-text{color:var(--text-on-accent-inverted);opacity:.8}.qa-suggest-exact:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--interactive-accent);border-radius:0 1px 1px 0}.qa-suggest-alias:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-orange, #ff8c00);border-radius:0 1px 1px 0}.qa-suggest-fuzzy:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--text-faint);border-radius:0 1px 1px 0}.qa-suggest-unresolved:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-red, #e74c3c);border-radius:0 1px 1px 0}.qa-suggest-heading:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-blue, #3498db);border-radius:0 1px 1px 0}.qa-suggest-block:before{content:"";position:absolute;left:0;top:0;bottom:0;width:2px;background:var(--color-purple, #9b59b6);border-radius:0 1px 1px 0}.qa-suggestion-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0}.qaFileSuggestionItem .suggestion-main-text{font-weight:500;color:var(--text-normal);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:200px}.qaFileSuggestionItem .suggestion-sub-text{font-size:11px;color:var(--text-muted);white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:150px;margin-left:auto;padding-left:8px;opacity:.7}.qa-suggestion-pill{display:inline-block;padding:1px 4px;border-radius:2px;font-size:9px;font-weight:600;text-transform:uppercase;letter-spacing:.3px;line-height:1;opacity:.8}.qa-alias-pill{background:var(--color-orange, #ff8c00);color:#fff}.qa-unresolved-pill{background:var(--color-red, #e74c3c);color:#fff}.qa-create-pill{background:var(--color-green, #27ae60);color:#fff}.qa-heading-pill{background:var(--color-blue, #3498db);color:#fff}.qa-block-pill{background:var(--color-purple, #9b59b6);color:#fff}.qa-file-tooltip{background:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:4px;padding:8px 10px;max-width:280px;box-shadow:0 4px 20px #0000001f;z-index:1000;pointer-events:none;font-size:12px}.qa-tooltip-header{font-weight:600;font-size:13px;margin-bottom:4px;color:var(--text-normal)}.qa-tooltip-content{color:var(--text-muted);line-height:1.3}.qa-tooltip-content div{margin-bottom:2px}.qaFileSuggestionItem{will-change:transform,background-color;backface-visibility:hidden}.qaFileSuggestionItem:focus-visible{outline:2px solid var(--interactive-accent);outline-offset:-2px}.theme-dark .qa-file-tooltip{box-shadow:0 4px 20px #0000004d;border-color:var(--background-modifier-border-hover)}.theme-dark .qaFileSuggestionItem:hover{background-color:#ffffff0d}@media (prefers-contrast: high){.qa-suggest-exact:before,.qa-suggest-alias:before,.qa-suggest-fuzzy:before,.qa-suggest-unresolved:before,.qa-suggest-heading:before,.qa-suggest-block:before{width:3px}.qa-suggestion-pill{font-weight:700}}@media (prefers-reduced-motion: reduce){.qaFileSuggestionItem{transition:none}.qaFileSuggestionItem:hover{transform:none}}.choiceListItem{display:flex;font-size:16px;align-items:center;margin:12px 0 0;transition:1s ease-in-out}.choiceListItemName{flex:1 0 0}.choiceListItemName p{margin:0;display:inline}.quickadd-choice-suggestion p{margin:0}.macroDropdownContainer{display:flex;align-content:center;justify-content:center;margin-bottom:10px;gap:10px}.macro-choice-buttonsContainer{display:flex;flex-direction:row;justify-content:center;align-items:center;gap:10px}@media only screen and (max-width: 600px){.macroDropdownContainer{flex-direction:column;align-items:center}.macroDropdownContainer .macro-choice-buttonsContainer{gap:20px}}.quickadd-update-modal-container{display:flex;flex-direction:column;align-items:center;justify-content:center}.quickadd-update-modal{min-width:35%;max-width:90%;max-height:70%;overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;overflow-x:hidden;overflow-y:auto}.quickadd-update-modal pre{white-space:pre-wrap;word-wrap:break-word;overflow-x:auto;max-width:100%}.quickadd-update-modal code{word-wrap:break-word;white-space:pre-wrap;max-width:100%;overflow-x:auto}.quickadd-update-modal img{width:100%;height:auto;margin:5px}.quickadd-bmac-container{display:flex;justify-content:center;align-items:center}
 .choices{position:relative;margin-bottom:24px;font-size:16px}.choices:focus{outline:none}.choices:last-child{margin-bottom:0}.choices.is-disabled .choices__inner,.choices.is-disabled .choices__input{background-color:#eaeaea;cursor:not-allowed;-webkit-user-select:none;-ms-user-select:none;user-select:none}.choices.is-disabled .choices__item{cursor:not-allowed}.choices [hidden]{display:none!important}.choices[data-type*=select-one]{cursor:pointer}.choices[data-type*=select-one] .choices__inner{padding-bottom:7.5px}.choices[data-type*=select-one] .choices__input{display:block;width:100%;padding:10px;border-bottom:1px solid #dddddd;background-color:#fff;margin:0}.choices[data-type*=select-one] .choices__button{background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);padding:0;background-size:8px;position:absolute;top:50%;right:0;margin-top:-10px;margin-right:25px;height:20px;width:20px;border-radius:10em;opacity:.5}.choices[data-type*=select-one] .choices__button:hover,.choices[data-type*=select-one] .choices__button:focus{opacity:1}.choices[data-type*=select-one] .choices__button:focus{box-shadow:0 0 0 2px #00bcd4}.choices[data-type*=select-one] .choices__item[data-value=""] .choices__button{display:none}.choices[data-type*=select-one]:after{content:"";height:0;width:0;border-style:solid;border-color:#333333 transparent transparent transparent;border-width:5px;position:absolute;right:11.5px;top:50%;margin-top:-2.5px;pointer-events:none}.choices[data-type*=select-one].is-open:after{border-color:transparent transparent #333333 transparent;margin-top:-7.5px}.choices[data-type*=select-one][dir=rtl]:after{left:11.5px;right:auto}.choices[data-type*=select-one][dir=rtl] .choices__button{right:auto;left:0;margin-left:25px;margin-right:0}.choices[data-type*=select-multiple] .choices__inner,.choices[data-type*=text] .choices__inner{cursor:text}.choices[data-type*=select-multiple] .choices__button,.choices[data-type*=text] .choices__button{position:relative;display:inline-block;margin:0 -4px 0 8px;padding-left:16px;border-left:1px solid #008fa1;background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);background-size:8px;width:8px;line-height:1;opacity:.75;border-radius:0}.choices[data-type*=select-multiple] .choices__button:hover,.choices[data-type*=select-multiple] .choices__button:focus,.choices[data-type*=text] .choices__button:hover,.choices[data-type*=text] .choices__button:focus{opacity:1}.choices__inner{display:inline-block;vertical-align:top;width:100%;background-color:#f9f9f9;padding:7.5px 7.5px 3.75px;border:1px solid #dddddd;border-radius:2.5px;font-size:14px;min-height:44px;overflow:hidden}.is-focused .choices__inner,.is-open .choices__inner{border-color:#b7b7b7}.is-open .choices__inner{border-radius:2.5px 2.5px 0 0}.is-flipped.is-open .choices__inner{border-radius:0 0 2.5px 2.5px}.choices__list{margin:0;padding-left:0;list-style:none}.choices__list--single{display:inline-block;padding:4px 16px 4px 4px;width:100%}[dir=rtl] .choices__list--single{padding-right:4px;padding-left:16px}.choices__list--single .choices__item{width:100%}.choices__list--multiple{display:inline}.choices__list--multiple .choices__item{display:inline-block;vertical-align:middle;border-radius:20px;padding:4px 10px;font-size:12px;font-weight:500;margin-right:3.75px;margin-bottom:3.75px;background-color:#00bcd4;border:1px solid #00a5bb;color:#fff;word-break:break-all;box-sizing:border-box}.choices__list--multiple .choices__item[data-deletable]{padding-right:5px}[dir=rtl] .choices__list--multiple .choices__item{margin-right:0;margin-left:3.75px}.choices__list--multiple .choices__item.is-highlighted{background-color:#00a5bb;border:1px solid #008fa1}.is-disabled .choices__list--multiple .choices__item{background-color:#aaa;border:1px solid #919191}.choices__list--dropdown{visibility:hidden;z-index:1;position:absolute;width:100%;background-color:#fff;border:1px solid #dddddd;top:100%;margin-top:-1px;border-bottom-left-radius:2.5px;border-bottom-right-radius:2.5px;overflow:hidden;word-break:break-all;will-change:visibility}.choices__list--dropdown.is-active{visibility:visible}.is-open .choices__list--dropdown{border-color:#b7b7b7}.is-flipped .choices__list--dropdown{top:auto;bottom:100%;margin-top:0;margin-bottom:-1px;border-radius:.25rem .25rem 0 0}.choices__list--dropdown .choices__list{position:relative;max-height:300px;overflow:auto;-webkit-overflow-scrolling:touch;will-change:scroll-position}.choices__list--dropdown .choices__item{position:relative;padding:10px;font-size:14px}[dir=rtl] .choices__list--dropdown .choices__item{text-align:right}@media (min-width: 640px){.choices__list--dropdown .choices__item--selectable{padding-right:100px}.choices__list--dropdown .choices__item--selectable:after{content:attr(data-select-text);font-size:12px;opacity:0;position:absolute;right:10px;top:50%;transform:translateY(-50%)}[dir=rtl] .choices__list--dropdown .choices__item--selectable{text-align:right;padding-left:100px;padding-right:10px}[dir=rtl] .choices__list--dropdown .choices__item--selectable:after{right:auto;left:10px}}.choices__list--dropdown .choices__item--selectable.is-highlighted{background-color:#f2f2f2}.choices__list--dropdown .choices__item--selectable.is-highlighted:after{opacity:.5}.choices__item{cursor:default}.choices__item--selectable{cursor:pointer}.choices__item--disabled{cursor:not-allowed;-webkit-user-select:none;-ms-user-select:none;user-select:none;opacity:.5}.choices__heading{font-weight:600;font-size:12px;padding:10px;border-bottom:1px solid #f7f7f7;color:gray}.choices__button{text-indent:-9999px;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:0;background-color:transparent;background-repeat:no-repeat;background-position:center;cursor:pointer}.choices__button:focus{outline:none}.choices__input{display:inline-block;vertical-align:baseline;background-color:#f9f9f9;font-size:14px;margin-bottom:5px;border:0;border-radius:0;max-width:100%;padding:4px 0 4px 2px}.choices__input:focus{outline:0}[dir=rtl] .choices__input{padding-right:2px;padding-left:0}.choices__placeholder{opacity:.5}.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,.08);box-shadow:1px 0 #e6e6e6,-1px 0 #e6e6e6,0 1px #e6e6e6,0 -1px #e6e6e6,0 3px 13px #00000014}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none!important;box-shadow:none!important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:"";height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:#000000e6;fill:#000000e6;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#000000e6;fill:#000000e6}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:#0000001a}.numInputWrapper span:active{background:#0003}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:#00000080}.numInputWrapper:hover{background:#0000000d}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:#0000000d}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\fffd;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#000000e6}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#000000e6}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:#00000080;background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:#0000000d}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:#0000008a;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0,0,0);transform:translateZ(0);opacity:1}.dayContainer+.dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:#3939394d;background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:#3939391a}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 #569ff7,5px 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:#3939394d;background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:700}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:700;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}.workspace-leaf-content[data-type=kanban] .view-content{padding:0}.workspace-leaf-content[data-type=kanban]>.view-header{display:flex!important}.kanban-plugin{--lane-width: 272px}.kanban-plugin{contain:content;height:100%;width:100%;position:relative;display:flex;flex-direction:column}.kanban-plugin a.tag,.kanban-plugin__drag-container a.tag{padding-inline:var(--tag-padding-x);padding-block:var(--tag-padding-y)}.kanban-plugin__table-wrapper{height:100%;width:100%;overflow:auto;padding-block-end:40px;--table-column-first-border-width: 0;--table-column-last-border-width: 0;--table-row-last-border-width: 0}.kanban-plugin__table-wrapper table{width:fit-content;margin-block:0;margin-inline:auto;box-shadow:0 0 0 var(--table-border-width) var(--table-border-color)}.kanban-plugin__table-wrapper tr{width:fit-content}.kanban-plugin__table-wrapper th,.kanban-plugin__table-wrapper td{text-align:start;vertical-align:top;font-size:.875rem;padding:0!important;height:1px}.kanban-plugin__table-wrapper th.mod-has-icon .kanban-plugin__table-cell-wrapper,.kanban-plugin__table-wrapper td.mod-has-icon .kanban-plugin__table-cell-wrapper{padding-inline-end:var(--size-2-2)}.kanban-plugin__table-wrapper th .kanban-plugin__table-cell-wrapper,.kanban-plugin__table-wrapper td .kanban-plugin__table-cell-wrapper{height:100%;padding-inline:var(--size-4-2);padding-block:var(--size-2-2)}.kanban-plugin__table-wrapper th .kanban-plugin__item-prefix-button-wrapper input[type=checkbox],.kanban-plugin__table-wrapper td .kanban-plugin__item-prefix-button-wrapper input[type=checkbox]{margin-block:2px}.kanban-plugin__table-wrapper th:has(.markdown-source-view),.kanban-plugin__table-wrapper td:has(.markdown-source-view){--background-primary: var(--background-primary-alt);background:var(--background-primary);outline:2px solid var(--background-modifier-border-focus)}.kanban-plugin__table-wrapper thead tr>th{height:1px;background-color:var(--background-primary);position:sticky;top:0;z-index:1;overflow:visible}.kanban-plugin__table-wrapper thead tr>th:nth-child(2n+2){background-color:var(--background-primary)}.kanban-plugin__table-wrapper thead tr>th .kanban-plugin__table-cell-wrapper{height:100%;padding-block:var(--size-2-2);padding-inline:var(--size-4-2) var(--size-2-2);box-shadow:0 0 0 var(--table-border-width) var(--table-border-color)}.kanban-plugin__table-wrapper .resizer{position:absolute;top:0;height:100%;width:5px;background:var(--table-selection-border-color);cursor:col-resize;user-select:none;touch-action:none}.kanban-plugin__table-wrapper .resizer.ltr{right:0}.kanban-plugin__table-wrapper .resizer.rtl{left:0}.kanban-plugin__table-wrapper .resizer.isResizing{opacity:1}@media (hover: hover){.kanban-plugin__table-wrapper .resizer{opacity:0}.kanban-plugin__table-wrapper .resizer:hover{opacity:1}}.kanban-plugin__table-wrapper .kanban-plugin__item-tags:not(:empty){margin-block-start:-5px}.kanban-plugin__table-wrapper .kanban-plugin__item-metadata-date-relative{display:block}.kanban-plugin__table-wrapper .kanban-plugin__item-input-wrapper,.kanban-plugin__table-wrapper .cm-table-widget,.kanban-plugin__table-wrapper .kanban-plugin__item-title,.kanban-plugin__table-wrapper .kanban-plugin__item-title-wrapper,.kanban-plugin__table-wrapper .kanban-plugin__item-content-wrapper{height:100%}.kanban-plugin__table-wrapper .kanban-plugin__item-title-wrapper{padding:0}.kanban-plugin .markdown-source-view.mod-cm6{display:block;font-size:.875rem}.kanban-plugin .markdown-source-view.mod-cm6 .cm-scroller{overflow:visible}.kanban-plugin__table-header{display:flex;gap:var(--size-4-2);align-items:center;justify-content:space-between}.kanban-plugin__table-header-sort{line-height:1;color:var(--text-faint);padding:2px;border-radius:4px}.kanban-plugin__table-header-sort>span{display:flex}div:hover>.kanban-plugin__table-header-sort{background-color:var(--background-modifier-hover)}.kanban-plugin__cell-flex-wrapper{display:flex;gap:8px;align-items:flex-start;justify-content:space-between}.kanban-plugin__cell-flex-wrapper .lucide-more-vertical{transform:none}.kanban-plugin__icon-wrapper{display:flex;line-height:1}.kanban-plugin__icon-wrapper>.kanban-plugin__icon{display:flex}.kanban-plugin.something-is-dragging{cursor:grabbing;cursor:-webkit-grabbing}.kanban-plugin.something-is-dragging *{pointer-events:none}.kanban-plugin__item button,.kanban-plugin__lane button,.kanban-plugin button{line-height:1;margin:0;transition:.1s color,.1s background-color}.kanban-plugin__search-wrapper{width:100%;position:sticky;top:0;left:0;padding-block:10px;padding-inline:13px;display:flex;justify-content:flex-end;align-items:center;z-index:2;background-color:var(--background-primary)}button.kanban-plugin__search-cancel-button{display:flex;line-height:1;padding:6px;border:1px solid var(--background-modifier-border);background:var(--background-secondary-alt);color:var(--text-muted);margin-block:0;margin-inline:5px 0;font-size:16px}button.kanban-plugin__search-cancel-button .kanban-plugin__icon{display:flex}.kanban-plugin__icon{display:inline-block;line-height:1;--icon-size: 1em}.kanban-plugin__board{display:flex;width:100%;height:100%}.kanban-plugin__board>div{display:flex;align-items:flex-start;justify-content:flex-start;padding:1rem;width:fit-content;height:100%}.kanban-plugin__board.kanban-plugin__vertical>div{height:fit-content;width:100%;flex-direction:column}.is-mobile .view-content:not(.is-mobile-editing) .kanban-plugin__board>div{padding-bottom:calc(1rem + var(--mobile-navbar-height))}.kanban-plugin__board.is-adding-lane>div{padding-inline-end:calc(250px + 1rem)}.kanban-plugin__lane-wrapper{display:flex;flex-shrink:0;margin-inline-end:10px;max-height:100%;width:var(--lane-width)}.kanban-plugin__vertical .kanban-plugin__lane-wrapper{margin-block-end:10px;margin-inline-end:0}.kanban-plugin__lane{width:100%;display:flex;flex-direction:column;background-color:var(--background-secondary);border-radius:6px;border:1px solid var(--background-modifier-border)}.is-dropping>.kanban-plugin__lane{background-color:hsla(var(--interactive-accent-hsl),.15);border-color:hsla(var(--interactive-accent-hsl),1);outline:1px solid hsla(var(--interactive-accent-hsl),1)}.kanban-plugin__placeholder.kanban-plugin__lane-placeholder{height:100%;flex-grow:1;margin-inline-end:5px}.kanban-plugin__lane.is-hidden{display:none}.kanban-plugin__lane button{padding-block:8px;padding-inline:10px}.kanban-plugin__lane-form-wrapper{position:absolute;top:1rem;right:1rem;width:250px;background-color:var(--background-secondary);border-radius:6px;border:2px solid hsla(var(--interactive-accent-hsl),.7);z-index:var(--layer-popover);box-shadow:0 .5px 1px .5px #0000001a,0 2px 10px #0000001a,0 10px 20px #0000001a}.kanban-plugin__lane-input{--font-text-size: var(--font-ui-small);padding-block:var(--size-4-1);padding-inline:var(--size-4-2);background-color:var(--background-primary);border-radius:var(--radius-s)}.kanban-plugin__lane-input-wrapper{padding:10px}.kanban-plugin__item-input-actions,.kanban-plugin__lane-input-actions{display:flex;align-items:flex-start;justify-content:flex-start;padding-block:0 10px;padding-inline:10px}.kanban-plugin__item-input-actions button,.kanban-plugin__lane-input-actions button{display:block;margin-inline-end:5px}button.kanban-plugin__item-action-add,button.kanban-plugin__lane-action-add{background-color:var(--interactive-accent);color:var(--text-on-accent)}button.kanban-plugin__item-action-add:hover,button.kanban-plugin__lane-action-add:hover{background-color:var(--interactive-accent-hover)}.kanban-plugin__lane-header-wrapper{padding-block:8px;padding-inline:8px 12px;display:flex;align-items:center;gap:var(--size-4-1);flex-shrink:0;flex-grow:0;border-bottom:1px solid var(--background-modifier-border)}.collapse-horizontal .kanban-plugin__lane-header-wrapper,.collapse-vertical .kanban-plugin__lane-header-wrapper,.will-prepend .kanban-plugin__lane-header-wrapper{border-bottom:none}.kanban-plugin__lane-wrapper.collapse-horizontal{width:auto}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-header-wrapper{writing-mode:vertical-lr}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-header-wrapper{gap:var(--size-4-2)}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-title-count,.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-title-text{transform:rotate(180deg)}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-settings-button-wrapper{display:none}.kanban-plugin__lane-wrapper.collapse-vertical .kanban-plugin__lane-settings-button-wrapper{visibility:hidden}.kanban-plugin__lane-collapse{flex-grow:0;color:var(--text-faint)}.kanban-plugin__lane-collapse>span{display:flex}.collapse-vertical .kanban-plugin__lane-collapse>span{transform:rotate(-90deg)}.kanban-plugin__lane-grip{cursor:grab;flex-grow:0;color:var(--text-faint)}.kanban-plugin__lane-grip:active{cursor:grabbing}.kanban-plugin__lane-collapse svg{--icon-size: 1rem}.kanban-plugin__lane-grip>svg{height:1rem;display:block}.kanban-plugin__lane-title{font-weight:600;font-size:.875rem;flex-grow:1;width:100%;display:flex;flex-direction:column}.kanban-plugin__lane-title-text{flex-grow:1}div.kanban-plugin__lane-title-count{border-radius:3px;color:var(--text-muted);display:block;font-size:13px;line-height:1;padding:4px}div.kanban-plugin__lane-title-count.wip-exceeded{font-weight:700;color:var(--text-normal);background-color:rgba(var(--background-modifier-error-rgb),.25)}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu,.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-postfix-button,.kanban-plugin__lane .kanban-plugin__lane-settings-button{--icon-stroke: 2.5px;font-size:13px;line-height:1;color:var(--text-muted);padding:4px;display:flex;margin-inline-end:-4px}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu.is-enabled,.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button.is-enabled,.kanban-plugin__item .kanban-plugin__item-prefix-button.is-enabled,.kanban-plugin__item .kanban-plugin__item-postfix-button.is-enabled,.kanban-plugin__lane .kanban-plugin__lane-settings-button.is-enabled{color:var(--text-accent)}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu{color:var(--text-faint);margin-inline-start:2px;margin-inline-end:0px}.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-prefix-button{margin-inline-end:4px;margin-inline-start:-4px}.kanban-plugin__table-cell-wrapper button.kanban-plugin__item-prefix-button,.kanban-plugin__item button.kanban-plugin__item-prefix-button{margin-block:4px;margin-inline:0 7px;padding:0}.kanban-plugin__lane-action-wrapper,.kanban-plugin__item-edit-archive-button,.kanban-plugin__item-settings-actions .kanban-plugin__icon,.kanban-plugin__item-edit-archive-button>.kanban-plugin__icon,.kanban-plugin__item-prefix-button>.kanban-plugin__icon,.kanban-plugin__item-postfix-button>.kanban-plugin__icon,.kanban-plugin__lane-settings-button>.kanban-plugin__icon{display:flex}.kanban-plugin__lane-settings-button-wrapper{display:flex;gap:4px}button.kanban-plugin__lane-settings-button+button.kanban-plugin__lane-settings-button{margin-inline-start:2px}.kanban-plugin__lane-settings-button svg{width:1em;height:1em}.kanban-plugin__lane-items-wrapper{margin:4px;height:100%}.kanban-plugin__lane-items{padding:4px;margin-block:0;margin-inline:4px;display:flex;flex-direction:column}.kanban-plugin__lane-items>div{margin-block-start:4px}.kanban-plugin__lane-items>.kanban-plugin__placeholder{flex-grow:1}.kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{height:2.55em;border:3px dashed rgba(var(--text-muted-rgb),.1);margin-block-end:4px;border-radius:6px;transition:border .2s ease}.is-sorting .kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{border-color:hsla(var(--interactive-accent-hsl),.6)}.kanban-plugin__item-button-wrapper{border-top:1px solid var(--background-modifier-border);padding:8px;flex-shrink:0;flex-grow:0}.kanban-plugin__item-button-wrapper>button{text-align:left;width:100%}.kanban-plugin__lane-header-wrapper+.kanban-plugin__item-button-wrapper{border-top:none;border-bottom:1px solid var(--background-modifier-border);padding-block:4px 8px;padding-inline:8px}.kanban-plugin__item-form{border-top:1px solid var(--background-modifier-border);padding:8px}.kanban-plugin__item-form .kanban-plugin__item-input-wrapper{padding-block:6px;padding-inline:8px;border:1px solid var(--background-modifier-border);background-color:var(--background-primary);border-radius:var(--input-radius);min-height:var(--input-height)}.kanban-plugin__lane-header-wrapper+.kanban-plugin__item-form{border-top:none;border-bottom:1px solid var(--background-modifier-border);padding-block:4px 8px;padding-inline:8px}.kanban-plugin__item-input-wrapper{--line-height-normal: var(--line-height-tight);display:flex;flex-direction:column;flex-grow:1}.kanban-plugin button.kanban-plugin__item-submit-button{flex-grow:0;flex-shrink:1;font-size:14px;height:auto;line-height:1;margin-block-start:5px;width:auto}.is-mobile .kanban-plugin button.kanban-plugin__item-submit-button{font-size:12px}.is-mobile .kanban-plugin__lane-form-wrapper{--input-height: auto}.is-mobile .kanban-plugin__lane-form-wrapper button{padding-block:var(--size-4-2)}.is-mobile .kanban-plugin__lane-form-wrapper .markdown-source-view.mod-cm6{font-size:var(--font-ui-medium)}.is-mobile .kanban-plugin .kanban-plugin__lane-input-wrapper button.kanban-plugin__item-submit-button{display:none}button.kanban-plugin__new-item-button{background-color:transparent;color:var(--text-muted)}.kanban-plugin__new-item-button:hover{color:var(--text-on-accent);background-color:var(--interactive-accent-hover)}.kanban-plugin__drag-container>.kanban-plugin__item-wrapper .kanban-plugin__item{border-color:var(--interactive-accent);box-shadow:var(--shadow-s),0 0 0 2px hsla(var(--interactive-accent-hsl),.7)}.kanban-plugin__item{font-size:.875rem;border:1px solid var(--background-modifier-border);border-radius:var(--input-radius);overflow:hidden;transition:.3s opacity cubic-bezier(.25,1,.5,1)}.kanban-plugin__item:has(.markdown-source-view){outline:1px solid var(--background-modifier-border-focus);border-color:var(--background-modifier-border-focus)}.kanban-plugin__item-content-wrapper{background:var(--background-primary)}.kanban-plugin__item-title-wrapper{background:var(--background-primary);display:flex;padding-block:6px;padding-inline:8px}.kanban-plugin__item-title-wrapper:not(:only-child){border-bottom:1px solid var(--background-modifier-border)}.kanban-plugin__item-title{width:100%;line-height:var(--line-height-tight);margin-block-start:1px}.kanban-plugin__meta-value,.kanban-plugin__markdown-preview-wrapper{white-space:pre-wrap;white-space:break-spaces;word-break:break-word;overflow-wrap:anywhere;--font-text-size: .875rem;--line-height-normal: var(--line-height-tight);--p-spacing: var(--size-4-2);--list-indent: 1.75em}.kanban-plugin__meta-value .markdown-preview-view,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view{--file-margins: 0}.kanban-plugin__meta-value.inline,.kanban-plugin__markdown-preview-wrapper.inline{display:inline-block}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>*:first-child,.kanban-plugin__markdown-preview-wrapper .kanban-plugin__markdown-preview-view>*:first-child{margin-block-start:0}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>*:last-child,.kanban-plugin__markdown-preview-wrapper .kanban-plugin__markdown-preview-view>*:last-child{margin-block-end:0}.kanban-plugin__meta-value .markdown-preview-view,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view{width:unset;height:unset;position:unset;overflow-y:unset;overflow-wrap:unset;color:unset;user-select:unset;-webkit-user-select:unset;white-space:normal}.kanban-plugin__meta-value .markdown-preview-view .markdown-embed,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view .markdown-embed,.kanban-plugin__meta-value .markdown-preview-view blockquote,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view blockquote{padding-inline:var(--size-4-2) 0;padding-block:var(--size-4-1);margin-block-start:var(--p-spacing);margin-block-end:var(--p-spacing)}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view{display:inline-flex}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>div:first-child>*:first-child{margin-block-start:0}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>div:last-child>*:last-child{margin-block-end:0}.kanban-plugin__embed-link-wrapper{padding:2px;float:right}.kanban-plugin__item-metadata-wrapper:not(:empty){background-color:var(--background-primary-alt);padding-inline:8px;padding-block:6px}.kanban-plugin__item-metadata:not(:empty){padding-block-start:5px;font-size:12px}.kanban-plugin__item-metadata:not(:empty) .markdown-preview-view{line-height:var(--line-height-tight);font-size:inherit}.kanban-plugin__item-metadata>span{display:block}.kanban-plugin__item-metadata>span.kanban-plugin__item-metadata-date-wrapper{display:inline-block}.kanban-plugin__item-metadata .is-button{cursor:var(--cursor)}.kanban-plugin__item-metadata .is-button:hover{color:var(--text-normal)}.kanban-plugin__item-metadata-date-relative:first-letter{text-transform:uppercase}.kanban-plugin__item-metadata a{text-decoration:none}.kanban-plugin__item-task-inline-metadata-item,.kanban-plugin__item-task-metadata-item{display:inline-flex;margin-block:3px 0;margin-inline:0 6px;gap:4px}.kanban-plugin__item-task-inline-metadata-item{padding-inline:2px;background-color:var(--background-secondary);border-radius:var(--radius-s)}.kanban-plugin__table-cell-wrapper .kanban-plugin__item-task-inline-metadata-item{background-color:unset;padding-inline:unset;border-radius:unset}.kanban-plugin__item-tags:not(:empty){padding-block-start:2px}.kanban-plugin__item-tag{display:inline-block;margin-inline-end:4px}.kanban-plugin__item-tags .kanban-plugin__item-tag{font-size:12px;background-color:var(--tag-background, hsla(var(--interactive-accent-hsl), .1));color:var(--tag-color, var(--text-accent));margin-block:3px 0;margin-inline:0 3px}.kanban-plugin__item-tag.is-search-match,.kanban-plugin__item-tags .kanban-plugin__item-tag.is-search-match{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__meta-table{width:100%;margin:0;line-height:var(--line-height-tight);font-size:.75rem}.kanban-plugin__meta-table .markdown-preview-view{font-size:.75rem}.kanban-plugin__meta-table .kanban-plugin__item-tags .kanban-plugin__item-tag{position:relative;inset-block-start:-2px;margin-block:0 3px}.kanban-plugin__meta-table td{vertical-align:top;padding-block:3px 0;padding-inline:0;width:10%}.kanban-plugin__meta-table td+td{width:90%}.kanban-plugin__meta-table td:only-child{width:100%}.kanban-plugin__meta-table td.kanban-plugin__meta-key{white-space:nowrap;padding-inline-end:5px;color:var(--text-muted)}.kanban-plugin__meta-table td.kanban-plugin__meta-key.is-search-match>span{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__meta-value:not(.mod-array){white-space:pre-wrap;display:flex}.kanban-plugin__meta-value>.is-search-match,.kanban-plugin__meta-value.is-search-match{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__item-prefix-button-wrapper,.kanban-plugin__item-postfix-button-wrapper{display:flex;flex-grow:0;flex-shrink:0;align-self:start}.kanban-plugin__item-prefix-button-wrapper>div,.kanban-plugin__item-postfix-button-wrapper>div{display:flex;flex-direction:column;gap:var(--size-4-1)}.kanban-plugin__item-prefix-button-wrapper{flex-direction:column}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button{width:var(--checkbox-size);height:var(--checkbox-size)}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item-prefix-button-wrapper input[type=checkbox]{margin-block:2px;margin-inline:0px 7px}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button+button,.kanban-plugin__item-prefix-button-wrapper input[type=checkbox]+button{margin-block-start:10px}button.kanban-plugin__item-postfix-button{visibility:hidden;opacity:0;transition:.1s opacity;display:flex;align-self:flex-start}button.kanban-plugin__item-postfix-button.is-enabled,.kanban-plugin__item:hover button.kanban-plugin__item-postfix-button{visibility:visible;opacity:1}.kanban-plugin__item-settings-actions{padding:5px;display:flex}.kanban-plugin__item-settings-actions>button{line-height:1;display:flex;align-items:center;justify-content:center;font-size:.75rem;width:100%}.kanban-plugin__lane-action-wrapper button>.kanban-plugin__icon,.kanban-plugin__item-settings-actions button>.kanban-plugin__icon{margin-inline-end:5px}.kanban-plugin__item-settings-actions>button:first-child,.kanban-plugin__lane-action-wrapper>button:first-child{margin-inline-end:2.5px}.kanban-plugin__item-settings-actions>button:last-child,.kanban-plugin__lane-action-wrapper>button:last-child{margin-inline-start:2.5px}.kanban-plugin__archive-lane-button,.kanban-plugin__item-button-archive{color:var(--text-muted);border:1px solid var(--background-modifier-border)}.kanban-plugin__archive-lane-button:hover,.kanban-plugin__item-button-archive:hover{color:var(--text-normal)}.kanban-plugin__item-button-delete{border:1px solid rgba(var(--background-modifier-error-rgb),.15);color:rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__item-button-delete:hover{background-color:rgba(var(--background-modifier-error-rgb),.2);color:var(--text-error)}.theme-dark .kanban-plugin__item-button-delete{background-color:transparent;border:1px solid rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.theme-dark .kanban-plugin__item-button-delete:hover{background-color:rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.kanban-plugin__checkbox-wrapper{border-top:1px solid var(--background-modifier-border);border-bottom:1px solid var(--background-modifier-border);padding:10px;margin-block-end:10px;display:flex;align-items:center}.kanban-plugin__checkbox-wrapper .checkbox-container{flex-shrink:0;flex-grow:0;margin-inline-start:15px}.kanban-plugin__checkbox-label{font-size:.8125rem;line-height:var(--line-height-tight)}.kanban-plugin__lane-setting-wrapper>div{border-top:none;border-bottom:none;padding-block:10px;padding-inline:15px;margin-block-end:0}.kanban-plugin__lane-setting-wrapper>div:last-child{border-bottom:1px solid var(--background-modifier-border);margin-block-end:10px}.kanban-plugin__action-confirm-wrapper{border:1px solid rgba(var(--background-modifier-error-rgb),.2);background-color:rgba(var(--background-modifier-error-rgb),.1);border-radius:4px;padding:10px;margin-block:5px;margin-inline:10px}.theme-dark .kanban-plugin__action-confirm-wrapper{border:1px solid rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__delete-lane-button,.kanban-plugin__archive-lane-button{display:flex;align-items:center;justify-content:center;font-size:.75rem;width:50%}.kanban-plugin__delete-lane-button{border:1px solid rgba(var(--background-modifier-error-rgb),.15);color:rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__delete-lane-button:hover{background-color:rgba(var(--background-modifier-error-rgb),.2);color:var(--text-error)}.theme-dark .kanban-plugin__delete-lane-button{background-color:transparent;border:1px solid rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.theme-dark .kanban-plugin__delete-lane-button:hover{background-color:rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.kanban-plugin__action-confirm-text{font-size:.875rem;color:var(--text-error);margin-block-end:10px;line-height:var(--line-height-tight)}button.kanban-plugin__confirm-action-button{border:1px solid rgba(var(--background-modifier-error-rgb),.2);margin-inline-end:5px;color:var(--text-error)}button.kanban-plugin__confirm-action-button:hover{background-color:rgba(var(--background-modifier-error-rgb),.5)}button.kanban-plugin__cancel-action-button{border:1px solid var(--background-modifier-border)}.modal.kanban-plugin__board-settings-modal{width:var(--modal-width);height:var(--modal-height);max-height:var(--modal-max-height);max-width:var(--modal-max-width);padding:0;display:flex;flex-direction:column}.modal.kanban-plugin__board-settings-modal .modal-content{padding-block:30px;padding-inline:50px;height:100%;overflow-y:auto;overflow-x:hidden;margin:0}.kanban-plugin__board-settings-modal .setting-item{flex-wrap:wrap;justify-content:space-between}.kanban-plugin__board-settings-modal .setting-item-info{max-width:400px;min-width:300px;width:50%}.kanban-plugin__board-settings-modal .setting-item-control{min-width:300px;flex-shrink:0}.kanban-plugin__board-settings-modal .choices{width:100%;text-align:left}.kanban-plugin__board-settings-modal .choices[data-type*=select-one] .choices__inner{background-color:var(--background-primary);border-color:var(--background-modifier-border);padding:0;min-height:0}.kanban-plugin__board-settings-modal .choices[data-type*=select-one] .choices__input{background-color:var(--background-primary);border-bottom-color:var(--background-modifier-border);font-size:14px}.kanban-plugin__board-settings-modal .choices__input{border-radius:0;border-top:none;border-left:none;border-right:none}.kanban-plugin__board-settings-modal .choices__list[role=listbox]{overflow-x:hidden}.kanban-plugin__board-settings-modal .choices__list--single{padding-block:4px;padding-inline:6px 20px}.kanban-plugin__board-settings-modal .is-open .choices__list--dropdown,.kanban-plugin__board-settings-modal .choices__list--dropdown{background-color:var(--background-primary);border-color:var(--background-modifier-border);word-break:normal;max-height:200px;display:flex;flex-direction:column}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item--selectable:after{display:none}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item--selectable{padding-block:4px;padding-inline:6px}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item.is-highlighted{background-color:var(--background-primary-alt)}.kanban-plugin__board-settings-modal .choices__placeholder{opacity:1;color:var(--text-muted)}.kanban-plugin__board-settings-modal .error{border-color:var(--background-modifier-error-hover)!important}.kanban-plugin__date-picker{position:absolute;z-index:var(--layer-popover);--cell-size: 2.4em}.kanban-plugin__date-picker .flatpickr-input{width:0;height:0;opacity:0;border:none;padding:0;display:block;margin-block-end:-1px}.kanban-plugin__date-picker .flatpickr-current-month{color:var(--text-normal);font-weight:600;font-size:inherit;width:100%;position:static;height:auto;display:flex;align-items:center;justify-content:center;padding:0}.kanban-plugin__date-picker .flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:var(--text-normal)}.kanban-plugin__date-picker .flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:var(--text-normal)}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{fill:currentColor}.kanban-plugin__date-picker .flatpickr-calendar{border-radius:var(--radius-m);font-size:13px;overflow:hidden;background-color:var(--background-primary);width:calc(var(--cell-size) * 7 + 8px);box-shadow:0 0 0 1px var(--background-modifier-border),0 15px 25px #0003}.kanban-plugin__date-picker .flatpickr-calendar.inline{top:0}.kanban-plugin__date-picker .flatpickr-months{font-size:13px;padding-block:2px 4px;padding-inline:2px;align-items:center}.kanban-plugin__date-picker .flatpickr-months .flatpickr-current-month input.cur-year,.kanban-plugin__date-picker .flatpickr-months select{border-radius:4px;padding:4px}.kanban-plugin__date-picker .flatpickr-months .numInputWrapper{border-radius:4px}.kanban-plugin__date-picker .flatpickr-months .flatpickr-month{width:100%;height:auto}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month{color:var(--text-normal);fill:currentColor;border-radius:4px;display:flex;align-items:center;justify-content:center;line-height:1;height:auto;padding:5px;position:static;flex-shrink:0}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month:hover,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month:hover{background-color:var(--background-primary-alt);color:var(--text-normal)}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month:hover svg,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month:hover svg{fill:currentColor}.kanban-plugin__date-picker .flatpickr-current-month .flatpickr-monthDropdown-months{box-shadow:none;color:var(--text-normal);font-weight:inherit;margin-inline-end:5px}.kanban-plugin__date-picker .flatpickr-current-month input.cur-year{color:var(--text-normal);font-weight:inherit}.kanban-plugin__date-picker .flatpickr-weekdays{height:auto;padding-block:8px 12px;padding-inline:0}.kanban-plugin__date-picker span.flatpickr-weekday{font-weight:400;color:var(--text-muted)}.kanban-plugin__date-picker .flatpickr-innerContainer{padding:4px}.kanban-plugin__date-picker .flatpickr-day{color:var(--text-normal);display:inline-flex;align-items:center;justify-content:center;width:var(--cell-size);height:var(--cell-size);line-height:1;border-radius:6px}.kanban-plugin__date-picker .flatpickr-day.today{border-color:var(--interactive-accent)}.kanban-plugin__date-picker .flatpickr-day.today:hover{color:var(--text-normal);border-color:var(--interactive-accent);background-color:var(--background-primary-alt)}.kanban-plugin__date-picker .flatpickr-day.selected{border-color:var(--interactive-accent);background-color:var(--interactive-accent);color:var(--text-on-accent)}.kanban-plugin__date-picker .flatpickr-day.selected:hover{border-color:var(--interactive-accent);background-color:var(--interactive-accent)}.kanban-plugin__date-picker .flatpickr-days{width:calc(var(--cell-size) * 7)}.kanban-plugin__date-picker .dayContainer{width:calc(var(--cell-size) * 7);min-width:calc(var(--cell-size) * 7);max-width:calc(var(--cell-size) * 7)}.kanban-plugin__date-picker .flatpickr-day.inRange,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay.inRange,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay.inRange,.kanban-plugin__date-picker .flatpickr-day.today.inRange,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay.today.inRange,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay.today.inRange,.kanban-plugin__date-picker .flatpickr-day:hover,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay:hover,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay:hover,.kanban-plugin__date-picker .flatpickr-day:focus,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay:focus,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay:focus{background-color:var(--background-primary-alt);border-color:var(--background-primary-alt)}.kanban-plugin__date-picker .flatpickr-day.flatpickr-disabled,.kanban-plugin__date-picker .flatpickr-day.flatpickr-disabled:hover,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay,.kanban-plugin__date-picker .flatpickr-day.notAllowed,.kanban-plugin__date-picker .flatpickr-day.notAllowed.prevMonthDay,.kanban-plugin__date-picker .flatpickr-day.notAllowed.nextMonthDay{color:var(--text-faint)}.kanban-plugin__time-picker{position:absolute;max-height:250px;overflow:auto;border-radius:4px;border:1px solid var(--background-modifier-border);box-shadow:0 2px 8px var(--background-modifier-box-shadow);background:var(--background-primary);color:var(--text-normal);font-size:14px;z-index:var(--layer-menu)}.kanban-plugin__time-picker-item{display:flex;align-items:center;color:var(--text-muted);cursor:var(--cursor);line-height:1;padding-block:6px;padding-inline:8px}.kanban-plugin__time-picker-check{visibility:hidden;display:inline-flex;margin-inline-end:5px}.kanban-plugin__time-picker-item.is-hour{color:var(--text-normal);font-weight:600}.kanban-plugin__time-picker-item.is-selected .kanban-plugin__time-picker-check{visibility:visible}.kanban-plugin__time-picker-item:hover,.kanban-plugin__time-picker-item.is-selected{background:var(--background-secondary)}.kanban-plugin mark{background-color:var(--text-highlight-bg)}.kanban-plugin__draggable-setting-container{border-top:0;padding:0;flex-direction:column}.kanban-plugin__draggable-setting-container>div{width:100%;margin-inline-end:0!important}.kanban-plugin__setting-item-wrapper{border-top:1px solid var(--background-modifier-border)}.kanban-plugin__draggable-setting-container>.kanban-plugin__placeholder{border-top:1px solid var(--background-modifier-border)}.kanban-plugin__setting-item{background-color:var(--background-secondary);width:100%;font-size:16px;display:flex;align-items:flex-start;padding:12px;color:var(--text-muted)}.kanban-plugin__drag-container .kanban-plugin__setting-item{border:1px solid hsla(var(--interactive-accent-hsl),.8);box-shadow:0 15px 25px #0003,0 0 0 2px hsla(var(--interactive-accent-hsl),.8)}.kanban-plugin__setting-controls-wrapper{flex-grow:1;flex-shrink:1}.kanban-plugin__setting-input-wrapper{display:flex;flex-wrap:wrap;margin-block-end:1rem}.kanban-plugin__setting-input-wrapper>div{margin-inline-end:10px}.kanban-plugin__setting-toggle-wrapper>div{display:flex;align-items:center;line-height:1;margin-block-end:10px}.kanban-plugin__setting-toggle-wrapper .checkbox-container{margin-inline-end:10px}.kanban-plugin__setting-button-wrapper{display:flex;justify-content:flex-end;flex-grow:1;flex-shrink:0;max-width:25px}.kanban-plugin__setting-button-wrapper>div{margin-inline-start:12px}.kanban-plugin__setting-key-input-wrapper{margin-block:1rem;margin-inline:0}.kanban-plugin__setting-key-input-wrapper>input{margin-inline-end:10px}.kanban-plugin__date-color-input-wrapper,.kanban-plugin__tag-sort-input-wrapper,.kanban-plugin__tag-color-input-wrapper{display:flex;flex-direction:column;flex-grow:1;gap:1rem}.kanban-plugin__tag-sort-input-wrapper .kanban-plugin__setting-key-input-wrapper{margin-block-start:0}.kanban-plugin__tag-sort-input-wrapper .kanban-plugin__setting-input-wrapper{margin:0}.kanban-plugin__add-tag-color-button{align-self:baseline;margin:0}.kanban-plugin__date-color-wrapper,.kanban-plugin__tag-color-input .kanban-plugin__item-tags{background-color:var(--background-primary);padding:10px;margin:0;border-radius:4px}.kanban-plugin__tag-color-input .kanban-plugin__item-tag{margin-block-start:0;font-size:13px;font-weight:500;line-height:1.5}.kanban-plugin__date-color-input-wrapper input[type=number]{width:75px;padding-block:.6em;padding-inline:.8em;height:auto;border-radius:.5em}.kanban-plugin__date-color-input-wrapper .kanban-plugin__setting-item-label{margin-block-end:0}.kanban-plugin__date-color-config{padding-block:0 10px;padding-inline:0;display:flex;flex-wrap:wrap;gap:5px;align-items:center}.kanban-plugin__date-color-wrapper{display:inline-block;margin-block-start:10px}.kanban-plugin__date-color-wrapper .kanban-plugin__item-metadata{padding:0}.kanban-plugin__metadata-setting-desc{font-size:14px}.kanban-plugin__setting-button-spacer{visibility:hidden}.kanban-plugin__setting-item-label{font-size:12px;font-weight:700;margin-block-end:5px}.kanban-plugin__setting-toggle-wrapper .kanban-plugin__setting-item-label{margin-block-end:0}.kanban-plugin__hitbox{border:2px dashed tomato}.kanban-plugin__placeholder{flex-grow:0;flex-shrink:0;width:0;height:0;pointer-events:none}.kanban-plugin__placeholder[data-axis=horizontal]{height:100%}.kanban-plugin__placeholder[data-axis=vertical]{width:100%}body:not(.native-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar{background-color:transparent;width:16px;height:16px}body:not(.native-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar-thumb{border:4px solid transparent;background-clip:content-box}.kanban-plugin__scroll-container{will-change:transform}.kanban-plugin__scroll-container.kanban-plugin__horizontal{overflow-y:hidden;overflow-x:auto}.kanban-plugin__scroll-container.kanban-plugin__vertical{overflow-y:auto;overflow-x:hidden}.kanban-plugin__drag-container{contain:layout size;z-index:10000;pointer-events:none;position:fixed;top:0;left:0}.kanban-plugin__loading{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.sk-pulse{width:60px;height:60px;background-color:var(--text-faint);border-radius:100%;animation:sk-pulse 1.2s infinite cubic-bezier(.455,.03,.515,.955)}@keyframes sk-pulse{0%{transform:scale(0)}to{transform:scale(1);opacity:0}}.kanban-plugin__color-picker-wrapper{position:relative}.kanban-plugin__color-picker{position:absolute;top:-5px;left:0;transform:translateY(-100%)}.kanban-plugin__date,.cm-kanban-time-wrapper,.cm-kanban-date-wrapper{display:inline-block;color:var(--date-color);border-radius:var(--radius-s);background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .05))}.kanban-plugin__date:hover,.cm-kanban-time-wrapper:hover,.cm-kanban-date-wrapper:hover{background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .1))}.kanban-plugin__date.kanban-plugin__preview-date-link,.cm-kanban-time-wrapper.kanban-plugin__preview-date-link,.cm-kanban-date-wrapper.kanban-plugin__preview-date-link{--link-decoration: none;--link-unresolved-decoration-style: unset}.kanban-plugin__date>span,.cm-kanban-time-wrapper>span,.cm-kanban-date-wrapper>span,.kanban-plugin__date>a,.cm-kanban-time-wrapper>a,.cm-kanban-date-wrapper>a{padding-inline:var(--size-2-1)}.completion .kanban-plugin__date.has-background{color:inherit;background-color:transparent}.completion .kanban-plugin__date.has-background:hover{background-color:transparent}.is-date .kanban-plugin__date:not(.has-background){background-color:transparent}.is-date .kanban-plugin__date:not(.has-background):hover{background-color:transparent}.kanban-plugin__meta-value .kanban-plugin__date:hover{background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .05))}
 :root {
  --advanced-tables-helper-size: 28px;
}

.HyperMD-table-row span.cm-inline-code {
  font-size: 100%;
  padding: 0px;
}

.advanced-tables-buttons>div>.title {
  font-weight: var(--font-medium);
  font-size: var(--nav-item-size);
  color: var(--nav-item-color);
  text-decoration: underline;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container {
  column-gap: 0.2rem;
  margin: 0.2rem 0 0.2rem 0;
  justify-content: start;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container::before {
  min-width: 2.6rem;
  line-height: var(--advanced-tables-helper-size);
  font-size: var(--nav-item-size);
  font-weight: var(--nav-item-weight);
  color: var(--nav-item-color);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container>* {
  height: var(--advanced-tables-helper-size);
  line-height: var(--advanced-tables-helper-size);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button {
  width: var(--advanced-tables-helper-size);
  height: var(--advanced-tables-helper-size);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-s);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button:hover {
  background-color: var(--nav-item-background-hover);
  color: var(--nav-item-color-hover);
  font-weight: var(--nav-item-weight-hover);
}

.advanced-tables-row-label {
  width: 50px;
}

.widget-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-muted);
}

.widget-icon:hover {
  fill: var(--text-normal);
}

.advanced-tables-csv-export textarea {
  height: 200px;
  width: 100%;
}

.advanced-tables-donation {
  width: 70%;
  margin: 0 auto;
  text-align: center;
}

.advanced-tables-donate-button {
  margin: 10px;
} /* lists and bullets */
.outliner-plugin-better-lists .cm-s-obsidian .HyperMD-list-line {
  /* padding-top: 0.4em; */
}

.outliner-plugin-better-lists .cm-formatting-list-ul {
  margin-right: 0.3em;
}

.outliner-plugin-better-lists .list-bullet::after {
  width: 0.4em;
  height: 0.4em;
  background-color: var(--text-muted);
}

/* lines */
.outliner-plugin-list-lines-scroller {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: var(--file-margins);
  padding-left: 0;
  pointer-events: none;
  overflow: hidden;
}

.outliner-plugin-list-lines-content-container {
  position: relative;
}

.outliner-plugin-list-line {
  pointer-events: auto;
  position: absolute;
  width: 5px;
  margin-left: 0.5ch;
  margin-top: 1em;
  z-index: 1;
  cursor: pointer;
  background: transparent;
  background-image: linear-gradient(
    to right,
    var(--text-faint) 1px,
    transparent 1px
  );
  background-position-x: 2px;
  background-repeat: no-repeat;
}

.outliner-plugin-better-bullets .outliner-plugin-list-line {
  margin-top: 1.4em;
}

.markdown-source-view.mod-cm6.is-readable-line-width
  .outliner-plugin-list-lines-content-container {
  max-width: 700px;
  margin: auto;
}

.outliner-plugin-list-line:hover {
  background: var(--text-faint);
}

.outliner-plugin-vertical-lines
  .markdown-source-view.mod-cm6
  .cm-hmd-list-indent
  .cm-indent::before {
  content: none;
}

/* drag-n-drop */
.outliner-plugin-dropping-line {
  background-color: hsla(var(--interactive-accent-hsl), 0.4);
}

.outliner-plugin-dragging-line {
  opacity: 0.5;
  background-color: hsla(var(--interactive-accent-hsl), 0.2);
}

.outliner-plugin-drop-zone {
  width: 300px;
  height: 4px;
  background: var(--color-accent);
  z-index: 999;
  position: absolute;
  pointer-events: none;
}

.outliner-plugin-drop-zone-padding {
  position: absolute;
  height: 4px;
}

body.outliner-plugin-dnd:not(.outliner-plugin-dragging) .cm-formatting-list,
body.outliner-plugin-dnd:not(.outliner-plugin-dragging)
  .cm-fold-indicator
  .collapse-indicator {
  cursor: grab !important;
}

html body.outliner-plugin-dnd.outliner-plugin-dragging {
  cursor: grabbing !important;
}
 :root{--admonition-details-icon: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http: //www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M8.59 16.58L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.42z'/></svg>");--admonition-margin-top: 1.5rem;--admonition-margin-bottom: var(--admonition-margin-top);--admonition-margin-top-lp: 0px;--admonition-margin-bottom-lp: .75rem}.admonition{margin-top:var(--admonition-margin-top);margin-bottom:var(--admonition-margin-bottom);box-shadow:0 .2rem .5rem var(--background-modifier-box-shadow)}.admonition.no-title .admonition-content{margin-top:0;margin-bottom:0}.admonition li.task-list-item.is-checked p{text-decoration:line-through}.admonition.no-drop{box-shadow:none}.admonition.no-drop>.admonition-title.no-title+.admonition-content{margin-top:0;margin-bottom:0}.admonition.no-drop .admonition .admonition-content{border-right:.0625rem solid rgba(var(--admonition-color),.2);border-bottom:.0625rem solid rgba(var(--admonition-color),.2)}.admonition.no-drop .admonition .admonition-title.no-title+.admonition-content{border-top:.0625rem solid rgba(var(--admonition-color),.2);margin-top:0;margin-bottom:0}:is(.markdown-source-view.mod-cm6) .admonition .math-block>mjx-container{display:block;text-align:center;padding:1rem}:is(.markdown-reading-view) .admonition .math-block>mjx-container{display:block;text-align:center;padding:.0625rem}*:not(.is-live-preview) .admonition.no-content{display:none}.is-live-preview .admonition{margin-top:var(--admonition-margin-top-lp);margin-bottom:var(--admonition-margin-bottom-lp)}.is-live-preview .admonition.no-content{opacity:.1}.is-live-preview .admonition-content p{line-height:inherit;margin:revert}.is-live-preview .admonition-content p br{display:initial}.is-live-preview .admonition-content p ul>li>ul{border-left:var(--blockquote-border-thickness);border-left-color:var(--list-marker-color);border-left-style:solid}.is-live-preview .admonition-content:first-child{margin-top:.8rem}.is-live-preview .admonition-content:last-child{margin-bottom:.8rem}.admonition-title.no-title{display:none}.admonition-title:hover+.admonition-content .admonition-content-copy{opacity:.7}.admonition-content,.callout-content{position:relative}.admonition-content-copy{color:var(--text-faint);cursor:pointer;opacity:0;position:absolute;margin:.375rem;right:0;top:0;transition:.3s opacity ease-in}.admonition-content-copy:hover{color:var(--text-normal)}.admonition:hover .admonition-content-copy,.callout:hover .admonition-content-copy,.admonition-content-copy:hover{opacity:1}.admonition-settings .additional{margin:.375rem .75rem}.admonition-settings .additional>.setting-item{border-top:0;padding-top:.5625rem}.admonition-settings .coffee{width:60%;color:var(--text-faint);margin:1rem auto;text-align:center}.admonition-settings .coffee img{height:30px}.admonition-settings details>summary{outline:none;display:block!important;list-style:none!important;list-style-type:none!important;min-height:1rem;border-top-left-radius:.1rem;border-top-right-radius:.1rem;cursor:pointer;position:relative}.admonition-settings details>summary>.collapser{position:absolute;top:50%;right:.5rem;transform:translateY(-50%);content:""}.admonition-settings details>summary>.collapser>.handle{transform:rotate(0);transition:transform .25s;background-color:currentColor;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:contain;mask-size:contain;-webkit-mask-image:var(--admonition-details-icon);mask-image:var(--admonition-details-icon);width:20px;height:20px}.admonition-settings details[open]>summary>.collapser>.handle{transform:rotate(90deg)}.setting-item>.admonition{width:50%;margin:0}.unset-align-items{align-items:unset}.admonition-settings-modal .has-invalid-message{display:grid;grid-template-columns:1fr auto;grid-template-rows:1fr 1fr;grid-template-areas:"text image" "inv inv"}.admonition-settings-modal input.is-invalid{border-color:#dc3545!important;background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right calc(0.375em + 0.1875rem) center;background-size:calc(0.75em + 0.375rem) calc(0.75em + 0.375rem)}.admonition-settings-modal .admonition-type-setting input{grid-column:span 2}.admonition-settings-modal .invalid-feedback{display:block;grid-area:inv;width:100%;margin-top:.25rem;font-size:.875em;color:#dc3545}.suggestion-content.admonition-icon{display:flex;align-items:center;justify-content:space-between;flex-flow:row wrap}.suggestion-content.admonition-icon>.suggestion-text.admonition-text{width:fit-content}.suggestion-content.admonition-icon>.suggestion-flair.admonition-suggester-icon{width:min-content;position:relative;top:unset;left:unset;right:unset;bottom:unset;display:flex;align-items:center}.suggestion-content.admonition-icon>.suggestion-note{width:100%}.suggestion-container>.suggestion>.suggestion-item.admonition-suggester-item{color:rgb(var(--callout-color))}.suggestion-container>.suggestion>.suggestion-item.admonition-suggester-item.is-selected{background-color:rgba(var(--callout-color),.1)}.suggestion-container>.suggestion>.suggestion-item.admonition-suggester-item .admonition-suggester-icon{display:inline-block;vertical-align:middle}.suggestion-container>.suggestion>.suggestion-item.admonition-suggester-item .admonition-suggester-icon:not(:empty){padding-right:var(--size-4-1)}.admonition-color-settings .setting-item-control{gap:1rem}.admonition-color-settings input[type=color]:disabled{opacity:.75;cursor:not-allowed}.theme-dark .admonition-color-settings input[type=color]:disabled{opacity:1;cursor:not-allowed}.admonition-convert{display:flex;align-items:center;gap:.25rem}.admonition-convert-icon{display:flex;align-items:center}.admonition-convert-icon .admonition-spin{animation:admonition-convert 1s ease-in-out infinite;fill:currentColor}@keyframes admonition-convert{0%{transform:rotate(-45deg)}to{transform:rotate(315deg)}}.admonition-settings .admonition-convert{color:var(--text-error)}.notice-container .admonition-convert{justify-content:space-between;gap:1rem}.admonition-file-upload{margin-right:0;margin-left:12px}.admonition-file-upload>input[type=file]{display:none}.insert-admonition-modal button:focus,.insert-admonition-modal .clickable-icon:focus{box-shadow:0 0 5px #00000080;border-color:var(--background-modifier-border-focus)}.admonition-settings details>summary::-webkit-details-marker,.admonition-settings details>summary::marker{display:none!important}.admonition-setting-warning{display:flex;gap:.25rem;align-items:center}.admonition-setting-warning.text-warning{color:var(--text-error)}.admonitions-nested-settings{padding-bottom:18px}.admonitions-nested-settings .setting-item{border:0;padding-bottom:0}.admonitions-nested-settings[open] .setting-item-heading,.admonitions-nested-settings:not(details) .setting-item-heading{border-top:0;border-bottom:1px solid var(--background-modifier-border)}.is-live-preview .admonition-content ul,.is-live-preview .admonition-content ol{white-space:normal}.callout:not(.admonition).drop-shadow{box-shadow:0 .2rem .5rem var(--background-modifier-box-shadow)}.callout:not(.admonition) .no-title{display:none}
 @charset "UTF-8";.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,.08);box-shadow:1px 0 #e6e6e6,-1px 0 #e6e6e6,0 1px #e6e6e6,0 -1px #e6e6e6,0 3px 13px #00000014}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none!important;box-shadow:none!important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:"";height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:#000000e6;fill:#000000e6;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#000000e6;fill:#000000e6}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:rgba(0,0,0,.1)}.numInputWrapper span:active{background:rgba(0,0,0,.2)}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:#00000080}.numInputWrapper:hover{background:rgba(0,0,0,.05)}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0px,0px,0px);transform:translateZ(0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:rgba(0,0,0,.05)}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\fffd;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#000000e6}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#000000e6}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:#00000080;background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:rgba(0,0,0,.05)}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:#0000008a;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0px,0px,0px);transform:translateZ(0);opacity:1}.dayContainer+.dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:#3939394d;background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:#3939391a}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 #569ff7,5px 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:#3939394d;background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:700}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:700;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}:root{--tasks-details-icon: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M8.59 16.58L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.42z'/></svg>")}ul.contains-task-list .task-list-item-checkbox{margin-inline-start:calc(var(--checkbox-size) * -1.5)!important}.plugin-tasks-query-explanation{--code-white-space: pre}.tasks-count{color:var(--text-faint);padding-left:20px}.tooltip.pop-up{animation:pop-up-animation .2s forwards ease-in-out}@keyframes pop-up-animation{0%{opacity:0;transform:translateY(-100%) scale(1)}20%{opacity:.7;transform:translateY(-100%) scale(1.02)}40%{opacity:1;transform:translateY(-100%) scale(1.05)}to{opacity:1;transform:translateY(-100%) scale(1)}}.task-cancelled,.task-created,.task-done,.task-due,.task-scheduled,.task-start{cursor:pointer;user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}.tasks-edit,.tasks-postpone{width:1em;height:1em;vertical-align:middle;margin-left:.33em;cursor:pointer;font-family:var(--font-interface);color:var(--text-accent);user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}a.tasks-edit,a.tasks-postpone{text-decoration:none}.tasks-edit:after{content:"\1f4dd"}.tasks-postpone:after{content:"\23e9"}.tasks-urgency{font-size:var(--font-ui-smaller);font-family:var(--font-interface);padding:2px 6px;border-radius:var(--radius-s);color:var(--text-normal);background-color:var(--background-secondary);margin-left:.5em;line-height:1}.internal-link.internal-link-short-mode{text-decoration:none}.tasks-list-text{position:relative}.tasks-list-text .tooltip{position:absolute;top:0;left:0;white-space:nowrap}.task-list-item-checkbox{cursor:pointer}.tasks-layout-hide-tags .task-description a.tag,.task-list-item .task-block-link{display:none}.tasks-modal section+section{margin-top:6px}.tasks-modal hr{margin:6px 0}.tasks-modal .tasks-modal-error{border:1px solid red!important}.tasks-modal .accesskey{text-decoration:underline;text-underline-offset:1pt}.tasks-modal-description-section textarea{width:100%;min-height:calc(var(--input-height) * 2);resize:vertical;margin-top:8px}.tasks-modal-priority-section{display:grid;grid-template-columns:6em auto auto auto;grid-row-gap:.15em}.tasks-modal-priority-section>label{grid-row-start:1;grid-row-end:3}.tasks-modal-priority-section .task-modal-priority-option-container{white-space:nowrap}.tasks-modal-priority-section .task-modal-priority-option-container input+label{font-size:var(--font-ui-small);border-radius:var(--input-radius);padding:2px 3px}.tasks-modal-priority-section .task-modal-priority-option-container input{accent-color:var(--interactive-accent)}.tasks-modal-priority-section .task-modal-priority-option-container input:focus+label{box-shadow:0 0 0 2px var(--background-modifier-border-focus);border-color:var(--background-modifier-border-focus)}.tasks-modal-priority-section .task-modal-priority-option-container input:checked+label{font-weight:700}.tasks-modal-priority-section .task-modal-priority-option-container input:not(:checked)+label>span:nth-child(4){filter:grayscale(100%) opacity(60%)}.tasks-modal-dates-section{display:grid;grid-template-columns:6em 13em auto;column-gap:.5em;row-gap:5px;align-items:center}.tasks-modal-dates-section label{grid-column:1}.tasks-modal-dates-section .tasks-modal-date-input{min-width:15em}.tasks-modal-dates-section .tasks-modal-date-editor-picker{margin-left:.5em}.tasks-modal-dates-section .tasks-modal-parsed-date{grid-column:3;font-size:var(--font-ui-small)}.tasks-modal-dates-section .future-dates-only{grid-column-start:1;grid-column-end:3}.tasks-modal-dates-section .future-dates-only input{margin-left:.67em;top:2px}.tasks-modal-dates-section .status-editor-status-selector{grid-column:2}.tasks-modal-dependencies-section{display:grid;grid-template-columns:6em auto;column-gap:.5em;row-gap:5px;align-items:center}.tasks-modal-dependencies-section .tasks-modal-dependency-input{grid-column:2;width:100%}.tasks-modal-dependencies-section .results-dependency{grid-column:2}.tasks-modal-button-section{position:sticky;bottom:0;background-color:var(--modal-background);padding-bottom:16px;padding-top:16px;display:grid;grid-template-columns:3fr 1fr;column-gap:.5em}.tasks-modal-button-section button:disabled{pointer-events:none!important;opacity:.3!important}@media (max-width: 649px){.tasks-modal-priority-section{grid-template-columns:6em auto auto}.tasks-modal-priority-section>label{grid-row:1/span 3}}@media (max-width: 499px){.tasks-modal-priority-section{grid-template-columns:4em auto auto}.tasks-modal-dates-section{grid-template-columns:1fr;grid-auto-columns:auto}.tasks-modal-dates-section .tasks-modal-date-input{grid-column:1}.tasks-modal-dates-section .tasks-modal-parsed-date{grid-column:2}.tasks-modal-dates-section .status-editor-status-selector,.tasks-modal-dependencies-section label,.tasks-modal-dependencies-section .results-dependency{grid-column:1}}@media (max-width: 399px){.tasks-modal-dates-section .status-editor-status-selector{grid-column:1}.tasks-modal-dates-section>.tasks-modal-parsed-date{grid-column:1}.tasks-modal-priority-section{grid-template-columns:4em auto}.tasks-modal-priority-section>label{grid-row:1/span 6}.tasks-modal-dependencies-section{grid-template-columns:1fr;grid-auto-columns:auto}}@media (max-width: 259px){.tasks-modal-priority-section{grid-template-columns:1fr}.tasks-modal-priority-section>label{grid-row:1}}.task-dependencies-container{grid-column:2;display:flex;flex-wrap:wrap;gap:8px}.task-dependency{display:inline-flex;background-color:var(--interactive-normal);box-shadow:var(--input-shadow);border-radius:28px;padding:4px 4px 4px 8px}.task-dependency-name{font-size:var(--font-ui-small);max-width:160px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-delete{padding:3px;cursor:pointer;height:inherit;box-shadow:none!important;border-radius:50%}.task-dependency-dropdown{list-style:none;position:absolute;top:0;left:0;padding:4px;margin:0;background-color:var(--background-primary);border:1px;border-radius:6px;border-color:var(--background-modifier-border);border-style:solid;z-index:99;max-height:170px;overflow-y:auto}.task-dependency-dropdown li{padding:5px;margin:2px;border-radius:6px;cursor:pointer;display:flex;justify-content:space-between}.task-dependency-dropdown li .dependency-name{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-dropdown li .dependency-name-shared{width:60%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-dropdown li .dependency-path{width:40%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-style:italic;text-align:right;color:var(--italic-color)}.task-dependency-dropdown li.selected{background-color:var(--text-selection)}.tasks-setting-important{color:red;font-weight:700}.tasks-settings-is-invalid{color:var(--text-error)!important;background-color:rgba(var(--background-modifier-error-rgb),.2)!important}.tasks-settings .additional{margin:6px 12px}.tasks-settings .additional>.setting-item{border-top:0;padding-top:9px}.tasks-settings details>summary{outline:none;display:block!important;list-style:none!important;list-style-type:none!important;min-height:1rem;border-top-left-radius:.1rem;border-top-right-radius:.1rem;cursor:pointer;position:relative}.tasks-settings details>summary::-webkit-details-marker,.tasks-settings details>summary::marker{display:none!important}.tasks-settings details>summary>.collapser{position:absolute;top:50%;right:8px;transform:translateY(-50%);content:""}.tasks-settings details>summary>.collapser>.handle{transform:rotate(0);transition:transform .25s;background-color:currentColor;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:contain;mask-size:contain;-webkit-mask-image:var(--tasks-details-icon);mask-image:var(--tasks-details-icon);width:20px;height:20px}.tasks-settings details[open]>summary>.collapser>.handle{transform:rotate(90deg)}.tasks-nested-settings .setting-item{border:0px;padding-bottom:0}.tasks-nested-settings{padding-bottom:18px}.tasks-nested-settings[open] .setting-item-heading,.tasks-nested-settings:not(details) .setting-item-heading{border-top:0px;border-bottom:1px solid var(--background-modifier-border)}.tasks-settings .row-for-status{margin-top:0;margin-bottom:0}.tasks-settings .tasks-presets-wrapper{width:100%;position:relative;transition:all .2s ease}.tasks-settings .tasks-presets-wrapper.tasks-presets-dragging{opacity:.5;transform:rotate(2deg)}.tasks-settings .tasks-presets-wrapper.tasks-presets-drop-above:before{content:"";position:absolute;top:-2px;left:0;right:0;height:4px;background-color:var(--interactive-accent);border-radius:2px;z-index:10}.tasks-settings .tasks-presets-wrapper.tasks-presets-drop-below:after{content:"";position:absolute;bottom:-2px;left:0;right:0;height:4px;background-color:var(--interactive-accent);border-radius:2px;z-index:10}.tasks-settings .tasks-presets-setting .tasks-presets-key{grid-area:key}.tasks-settings .tasks-presets-setting .tasks-presets-key.has-error{border-color:var(--text-error);border-width:2px}.tasks-settings .tasks-presets-setting .tasks-presets-value{grid-area:value;min-width:300px;min-height:3em;font-family:var(--font-monospace);resize:horizontal;overflow-x:auto;overflow-y:hidden;white-space:pre}.tasks-settings .tasks-presets-setting .tasks-presets-drag-handle{grid-area:drag;color:var(--text-muted)}.tasks-settings .tasks-presets-setting .tasks-presets-drag-handle:hover{color:var(--text-normal)}.tasks-settings .tasks-presets-setting .tasks-presets-delete-button{grid-area:delete}.tasks-settings .tasks-presets-setting .setting-item-control{justify-content:start;display:grid;grid-template-columns:200px 1fr auto auto;grid-template-areas:"key value drag delete";gap:4px;align-items:unset;border:1px solid var(--background-modifier-border)!important;padding:.5em!important;background-color:var(--background-secondary)!important;border-radius:4px!important}@container (max-width: 600px){.tasks-settings .tasks-presets-setting .setting-item-control{grid-template-columns:5fr 1fr 1fr;grid-template-areas:"key drag delete" "value value value"}.tasks-settings .tasks-presets-setting .setting-item-control .tasks-presets-key{width:100%}}
 body{--todoist-berry-red: #b8256f;--todoist-red: #db4035;--todoist-orange: #ff9933;--todoist-yellow: #fad000;--todoist-olive-green: #afb83b;--todoist-lime-green: #7ecc49;--todoist-green: #299438;--todoist-mint-green: #6accbc;--todoist-teal: #158fad;--todoist-sky-blue: #14aaf5;--todoist-light-blue: #96c3eb;--todoist-blue: #4073ff;--todoist-grape: #884dff;--todoist-violet: #af38eb;--todoist-lavender: #eb96eb;--todoist-magenta: #e05194;--todoist-salmon: #ff8d85;--todoist-charcoal: #808080;--todoist-grey: #b8b8b8;--todoist-taupe: #ccac93}.theme-dark{--todoist-p1-border: #ff7066;--todoist-p1-border-hover: #ff706680;--todoist-p1-background: rgba(255, 112, 102, .1);--todoist-p2-border: #ff9a14;--todoist-p2-border-hover: #ff9a1480;--todoist-p2-background: rgba(255, 154, 20, .1);--todoist-p3-border: #5297ff;--todoist-p3-border-hover: #5297ff80;--todoist-p3-background: rgba(82, 151, 255, .1);--todoist-p4-border: var(--color-base-50);--todoist-p4-border-hover: var(--color-base-50);--todoist-p4-background: unset;--todoist-task-separator-color: var(--color-base-30)}.theme-light{--todoist-p1-border: #d1453b;--todoist-p1-border-hover: #d1453b80;--todoist-p1-background: rgba(209, 69, 59, .1);--todoist-p2-border: #eb8909;--todoist-p2-border-hover: #eb890980;--todoist-p2-background: rgba(235, 137, 9, .1);--todoist-p3-border: #246fe0;--todoist-p3-border-hover: #246fe080;--todoist-p3-background: rgba(36, 111, 224, .1);--todoist-p4-border: var(--color-base-50);--todoist-p4-border-hover: var(--color-base-50);--todoist-p4-background: unset;--todoist-task-separator-color: var(--color-base-25)}.obsidian-icon{display:flex;align-items:center}.obsidian-icon[data-icon-size=xs]{--icon-size: var(--icon-xs);--icon-stroke: var(--icon-xs-stroke-width)}.obsidian-icon[data-icon-size=s]{--icon-size: var(--icon-s);--icon-stroke: var(--icon-s-stroke-width)}.obsidian-icon[data-icon-size=m]{--icon-size: var(--icon-m);--icon-stroke: var(--icon-m-stroke-width)}.obsidian-icon[data-icon-size=l]{--icon-size: var(--icon-l);--icon-stroke: var(--icon-l-stroke-width)}.obsidian-icon[data-icon-size=xl]{--icon-size: var(--icon-xl);--icon-stroke: var(--icon-xl-stroke-width)}.todoist-callout{margin-top:1em;padding:16px;background-color:var(--todoist-callout-color);border-radius:4px}.todoist-callout .callout-header{display:flex;align-items:center;margin-bottom:4px;font-weight:600}.todoist-callout .callout-header .obsidian-icon{margin-right:.5em}.todoist-callout ul{margin-block-start:0em;margin-block-end:0em}.todoist-query-header{display:flex;align-items:center;justify-content:space-between}.todoist-query-header .todoist-query-title{font-size:1.25em}.todoist-query-header .todoist-query-controls{display:flex;align-items:center;justify-content:end}.todoist-query-header .todoist-query-controls *+*{margin-left:.5em}.todoist-query-header .todoist-query-controls .todoist-query-control-button{padding:var(--size-2-2) var(--size-2-3);color:var(--text-muted);border-radius:var(--radius-s);border:1px solid var(--color-base-40);box-shadow:none;transition:opacity .33s;opacity:0}.block-language-todoist:hover .todoist-query-header .todoist-query-controls .todoist-query-control-button{opacity:1}.todoist-query-header .todoist-query-controls .todoist-query-control-button:hover{background-color:inherit;border:1px solid var(--interactive-accent)}.markdown-reading-view .todoist-query-header .todoist-query-controls .todoist-query-control-button.edit-query{display:none}.todoist-query-header .todoist-query-controls .todoist-query-control-button.refresh-query.is-refreshing>.obsidian-icon{animation:spin 1s linear infinite reverse}@-webkit-keyframes spin{to{-webkit-transform:rotate(360deg)}}.todoist-query-warnings{--todoist-callout-color: rgba(var(--color-yellow-rgb), .2)}.todoist-tasks-list{margin-top:1em}.todoist-tasks-list .todoist-tasks-list{margin-top:0;margin-left:2em}.todoist-tasks-list .todoist-tasks-list+.todoist-task-container{border-top:none}.todoist-tasks-list .todoist-tasks-list .todoist-task-container:first-child{border-top:none}.todoist-task-container{display:flex;padding:.5em 0;border-bottom:1px solid var(--todoist-task-separator-color)}.todoist-task-container:first-child{border-top:1px solid var(--todoist-task-separator-color)}.todoist-task-container[data-priority="1"]{--todoist-checkbox-border: var(--todoist-p4-border);--todoist-checkbox-border-hover: var(--todoist-p4-border-hover);--todoist-checkbox-background: var(--todoist-p4-background)}.todoist-task-container[data-priority="2"]{--todoist-checkbox-border: var(--todoist-p3-border);--todoist-checkbox-border-hover: var(--todoist-p3-border-hover);--todoist-checkbox-background: var(--todoist-p3-background)}.todoist-task-container[data-priority="3"]{--todoist-checkbox-border: var(--todoist-p2-border);--todoist-checkbox-border-hover: var(--todoist-p2-border-hover);--todoist-checkbox-background: var(--todoist-p2-background)}.todoist-task-container[data-priority="4"]{--todoist-checkbox-border: var(--todoist-p1-border);--todoist-checkbox-border-hover: var(--todoist-p1-border-hover);--todoist-checkbox-background: var(--todoist-p1-background)}.todoist-task-container .todoist-task-checkbox{margin-top:3px}.todoist-task-container .todoist-task-checkbox div{height:16px;width:16px;border-radius:50%;border:1px solid var(--todoist-checkbox-border);background-color:var(--todoist-checkbox-background)}.todoist-task-container .todoist-task-checkbox div:hover{border:1px solid var(--todoist-checkbox-border-hover)}.todoist-task-container .todoist-task{margin-left:.5em;width:100%}.todoist-task-container .todoist-task .todoist-task-description{font-size:var(--font-small);color:var(--text-muted)}.todoist-task-container .todoist-task .todoist-task-metadata{display:flex;justify-content:space-between;font-size:var(--font-smaller);margin-top:.25em;color:var(--text-muted)}.todoist-task-container .todoist-task .todoist-task-metadata>div{display:flex}.todoist-task-container .todoist-task .todoist-task-metadata>div>*+*{margin-left:1em}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item{display:flex;align-items:center}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item>*+*{margin-left:.25em}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-task-metadata-kind=project] .obsidian-icon{color:var(--todoist-project-color)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-task-metadata-kind=labels]{color:var(--todoist-label-color)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=berry-red]{--todoist-project-color: var(--todoist-berry-red)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=berry-red]{--todoist-label-color: var(--todoist-berry-red)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=red]{--todoist-project-color: var(--todoist-red)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=red]{--todoist-label-color: var(--todoist-red)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=orange]{--todoist-project-color: var(--todoist-orange)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=orange]{--todoist-label-color: var(--todoist-orange)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=yellow]{--todoist-project-color: var(--todoist-yellow)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=yellow]{--todoist-label-color: var(--todoist-yellow)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=olive-green]{--todoist-project-color: var(--todoist-olive-green)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=olive-green]{--todoist-label-color: var(--todoist-olive-green)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=lime-green]{--todoist-project-color: var(--todoist-lime-green)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=lime-green]{--todoist-label-color: var(--todoist-lime-green)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=green]{--todoist-project-color: var(--todoist-green)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=green]{--todoist-label-color: var(--todoist-green)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=mint-green]{--todoist-project-color: var(--todoist-mint-green)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=mint-green]{--todoist-label-color: var(--todoist-mint-green)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=teal]{--todoist-project-color: var(--todoist-teal)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=teal]{--todoist-label-color: var(--todoist-teal)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=sky-blue]{--todoist-project-color: var(--todoist-sky-blue)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=sky-blue]{--todoist-label-color: var(--todoist-sky-blue)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=light-blue]{--todoist-project-color: var(--todoist-light-blue)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=light-blue]{--todoist-label-color: var(--todoist-light-blue)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=blue]{--todoist-project-color: var(--todoist-blue)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=blue]{--todoist-label-color: var(--todoist-blue)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=grape]{--todoist-project-color: var(--todoist-grape)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=grape]{--todoist-label-color: var(--todoist-grape)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=violet]{--todoist-project-color: var(--todoist-violet)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=violet]{--todoist-label-color: var(--todoist-violet)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=lavender]{--todoist-project-color: var(--todoist-lavender)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=lavender]{--todoist-label-color: var(--todoist-lavender)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=magenta]{--todoist-project-color: var(--todoist-magenta)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=magenta]{--todoist-label-color: var(--todoist-magenta)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=salmon]{--todoist-project-color: var(--todoist-salmon)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=salmon]{--todoist-label-color: var(--todoist-salmon)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=charcoal]{--todoist-project-color: var(--todoist-charcoal)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=charcoal]{--todoist-label-color: var(--todoist-charcoal)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=grey]{--todoist-project-color: var(--todoist-grey)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=grey]{--todoist-label-color: var(--todoist-grey)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-project-color=taupe]{--todoist-project-color: var(--todoist-taupe)}.todoist-task-container .todoist-task .todoist-task-metadata .task-metadata-item[data-label-color=taupe]{--todoist-label-color: var(--todoist-taupe)}.todoist-task-container[data-due-metadata=overdue] .task-metadata-item[data-task-metadata-kind=due]{color:var(--todoist-red)}.todoist-group+.todoist-group{margin-top:2em}.todoist-group-title{margin:1em 0;font-weight:600}.todoist-no-tasks{--todoist-callout-color: rgba(var(--color-green-rgb), .2)}.todoist-query-error{--todoist-callout-color: rgba(var(--color-red-rgb), .2)}.modal-popover{overflow-y:auto}.task-creation-modal-root .task-content-input textarea{width:100%;border:0;background-color:unset;box-shadow:none;resize:none;padding:0}.task-creation-modal-root .task-content-input textarea:focus,.task-creation-modal-root .task-content-input textarea:hover{border:0;background-color:unset;box-shadow:none}.task-creation-modal-root .task-name textarea{font-size:var(--font-ui-large);font-weight:var(--font-semibold)}.task-creation-modal-root .task-description textarea{font-size:var(--font-ui-small)}.task-creation-modal-root .task-creation-selectors{margin-top:.5em;display:flex;align-items:center;justify-content:space-between}.task-creation-modal-root .task-creation-selectors .task-creation-selectors-group{display:flex;align-items:center}.task-creation-modal-root .task-creation-selectors .task-creation-selectors-group>*+*{margin-left:.5em}.task-creation-modal-root .task-creation-selectors button{box-shadow:none;background-color:unset;border:1px solid var(--color-base-25);color:var(--text-muted)}.task-creation-modal-root .task-creation-selectors button>*+*{margin-left:.5em}.task-creation-modal-root .task-creation-selectors button:hover,.task-creation-modal-root .task-creation-selectors button:focus{border:1px solid var(--interactive-accent);box-shadow:var(--box-shadow-hover)}.task-creation-modal-root .task-creation-notes ul{padding-inline-start:16px;font-size:var(--font-smallest);color:var(--text-muted)}.task-creation-modal-root .task-creation-controls{display:flex;justify-content:space-between}.is-mobile .task-creation-modal-root .task-creation-controls{flex-direction:column}.is-mobile .task-creation-modal-root .task-creation-controls>*+*{margin-top:.5em}.task-creation-modal-root .task-creation-controls .task-creation-action{display:flex}.task-creation-modal-root .task-creation-controls .task-creation-action>*+*{margin-left:1em}.is-mobile .task-creation-modal-root .task-creation-controls .task-creation-action{justify-content:end}.task-creation-modal-root hr{margin:1em 0;border-color:var(--color-base-25)}.task-option-dialog{background-color:var(--modal-background);border:var(--modal-border-width) solid var(--modal-border-color);border-radius:4px;min-width:200px;box-shadow:var(--shadow-s);padding:.5em 0}.task-date-menu .react-aria-MenuItem[data-focused]{background-color:var(--background-modifier-cover)}.task-date-menu .date-suggestion-elem{padding:8px 1em;display:flex;justify-content:space-between;align-items:center;font-size:var(--font-smaller)}.task-date-menu .date-suggestion-elem .date-suggestion-label{display:flex;align-items:center;font-weight:var(--font-semibold)}.task-date-menu .date-suggestion-elem .date-suggestion-label .obsidian-icon{margin-right:1em}.task-date-menu .date-suggestion-elem .date-suggestion-day{color:var(--text-faint)}.task-date-menu hr{width:100%;border-color:var(--color-base-25);margin:1em 0}.task-date-menu .date-picker{padding:0 1em;font-size:var(--font-small)}.task-date-menu .date-picker header{display:flex;align-items:center;justify-content:space-between}.task-date-menu .date-picker h4{margin-left:.5em;font-size:var(--font-small);font-weight:var(--font-semibold)}.task-date-menu .date-picker .date-picker-controls{display:flex;align-items:center;justify-content:right}.task-date-menu .date-picker .date-picker-controls button{box-shadow:none;background-color:unset;border:1px solid rgba(0,0,0,0);color:var(--text-muted)}.task-date-menu .date-picker .date-picker-controls button[data-disabled]{opacity:.5}.task-date-menu .date-picker .date-picker-controls button:hover:not([data-disabled]){border:1px solid var(--interactive-accent);box-shadow:var(--box-shadow-hover)}.task-date-menu .date-picker .react-aria-CalendarCell{text-align:center;padding:6px;border-radius:2px}.task-date-menu .date-picker .react-aria-CalendarCell:hover{background-color:var(--background-modifier-cover)}.task-date-menu .date-picker .react-aria-CalendarCell[data-outside-month]{display:none}.task-date-menu .date-picker .react-aria-CalendarCell[data-disabled]{color:var(--text-faint)}.task-date-menu .date-picker .react-aria-CalendarCell[data-selected]{background-color:var(--interactive-accent);color:var(--text-on-accent)}.task-date-menu .time-picker-container{display:flex;justify-content:center;align-items:center;gap:.5em;margin:0 1em 1em}.task-date-menu .time-picker-container .time-picker-button{flex-grow:1;color:var(--text-muted);box-shadow:none;background-color:unset;border:1px solid var(--color-base-25);display:flex;align-items:center;justify-content:center;gap:.5em}.task-date-menu .time-picker-container .time-picker-button:hover,.task-date-menu .time-picker-container .time-picker-button:focus{border:1px solid var(--interactive-accent);box-shadow:var(--box-shadow-hover)}.task-date-menu .time-picker-container .time-picker-clear-button{padding:var(--size-4-2);margin:0;border:none;background:none;color:var(--text-muted);display:flex;align-items:center;justify-content:center;border-radius:4px}.task-date-menu .time-picker-container .time-picker-clear-button:hover{color:var(--text-normal);background-color:var(--background-modifier-cover)}.task-priority-menu .priority-option{padding:8px 1em;font-size:var(--font-smaller)}.task-priority-menu .priority-option:hover{background-color:var(--background-modifier-cover)}.task-priority-menu .priority-option.is-selected{background-color:var(--interactive-accent);color:var(--text-on-accent)}.task-label-menu .label-option{padding:8px 1em;font-size:var(--font-smaller);display:flex;align-items:center;justify-content:space-between}.task-label-menu .label-option:hover{background-color:var(--background-modifier-cover)}.task-options-menu .task-option-dialog-item{padding:8px 1em;font-size:var(--font-smaller)}.task-options-menu .task-option-dialog-item:hover{background-color:var(--background-modifier-cover)}.task-options-menu .task-option-dialog-item.is-selected{background-color:var(--interactive-accent);color:var(--text-on-accent)}button.project-selector{box-shadow:none;background-color:unset;border:1px solid var(--color-base-25);color:var(--text-muted);display:flex;align-items:center}button.project-selector>*+*{margin-left:.5em}button.project-selector:hover,button.project-selector:focus{border:1px solid var(--interactive-accent);box-shadow:var(--box-shadow-hover)}.task-project-menu .search-filter-container{display:flex;align-items:center;justify-content:center}.task-project-menu .search-filter-container input{flex-grow:1;margin:0 4px}.task-project-menu .search-filter-container input:hover{box-shadow:none;border:1px solid var(--interactive-accent)}.task-project-menu hr{margin:.5em 0;border-color:var(--color-base-10)}.task-project-menu .project-option{padding:8px 1em;font-size:var(--font-smaller);display:flex;align-items:center;--project-padding: 8px;padding-left:var(--project-padding)}.task-project-menu .project-option[data-depth="1"]{--project-padding: 32px}.task-project-menu .project-option[data-depth="2"]{--project-padding: 56px}.task-project-menu .project-option[data-depth="3"]{--project-padding: 80px}.task-project-menu .project-option[data-depth="3"]{--project-padding: 104px}.task-project-menu .project-option>*+*{margin-left:.5em}.task-project-menu .project-option:hover{background-color:var(--background-modifier-cover)}.task-project-menu .project-option[data-filtered=true]{display:none}.todoist-project-icon{color:var(--todoist-project-color)}.todoist-project-icon[data-project-color=berry-red]{--todoist-project-color: var(--todoist-berry-red)}.todoist-project-icon[data-label-color=berry-red]{--todoist-label-color: var(--todoist-berry-red)}.todoist-project-icon[data-project-color=red]{--todoist-project-color: var(--todoist-red)}.todoist-project-icon[data-label-color=red]{--todoist-label-color: var(--todoist-red)}.todoist-project-icon[data-project-color=orange]{--todoist-project-color: var(--todoist-orange)}.todoist-project-icon[data-label-color=orange]{--todoist-label-color: var(--todoist-orange)}.todoist-project-icon[data-project-color=yellow]{--todoist-project-color: var(--todoist-yellow)}.todoist-project-icon[data-label-color=yellow]{--todoist-label-color: var(--todoist-yellow)}.todoist-project-icon[data-project-color=olive-green]{--todoist-project-color: var(--todoist-olive-green)}.todoist-project-icon[data-label-color=olive-green]{--todoist-label-color: var(--todoist-olive-green)}.todoist-project-icon[data-project-color=lime-green]{--todoist-project-color: var(--todoist-lime-green)}.todoist-project-icon[data-label-color=lime-green]{--todoist-label-color: var(--todoist-lime-green)}.todoist-project-icon[data-project-color=green]{--todoist-project-color: var(--todoist-green)}.todoist-project-icon[data-label-color=green]{--todoist-label-color: var(--todoist-green)}.todoist-project-icon[data-project-color=mint-green]{--todoist-project-color: var(--todoist-mint-green)}.todoist-project-icon[data-label-color=mint-green]{--todoist-label-color: var(--todoist-mint-green)}.todoist-project-icon[data-project-color=teal]{--todoist-project-color: var(--todoist-teal)}.todoist-project-icon[data-label-color=teal]{--todoist-label-color: var(--todoist-teal)}.todoist-project-icon[data-project-color=sky-blue]{--todoist-project-color: var(--todoist-sky-blue)}.todoist-project-icon[data-label-color=sky-blue]{--todoist-label-color: var(--todoist-sky-blue)}.todoist-project-icon[data-project-color=light-blue]{--todoist-project-color: var(--todoist-light-blue)}.todoist-project-icon[data-label-color=light-blue]{--todoist-label-color: var(--todoist-light-blue)}.todoist-project-icon[data-project-color=blue]{--todoist-project-color: var(--todoist-blue)}.todoist-project-icon[data-label-color=blue]{--todoist-label-color: var(--todoist-blue)}.todoist-project-icon[data-project-color=grape]{--todoist-project-color: var(--todoist-grape)}.todoist-project-icon[data-label-color=grape]{--todoist-label-color: var(--todoist-grape)}.todoist-project-icon[data-project-color=violet]{--todoist-project-color: var(--todoist-violet)}.todoist-project-icon[data-label-color=violet]{--todoist-label-color: var(--todoist-violet)}.todoist-project-icon[data-project-color=lavender]{--todoist-project-color: var(--todoist-lavender)}.todoist-project-icon[data-label-color=lavender]{--todoist-label-color: var(--todoist-lavender)}.todoist-project-icon[data-project-color=magenta]{--todoist-project-color: var(--todoist-magenta)}.todoist-project-icon[data-label-color=magenta]{--todoist-label-color: var(--todoist-magenta)}.todoist-project-icon[data-project-color=salmon]{--todoist-project-color: var(--todoist-salmon)}.todoist-project-icon[data-label-color=salmon]{--todoist-label-color: var(--todoist-salmon)}.todoist-project-icon[data-project-color=charcoal]{--todoist-project-color: var(--todoist-charcoal)}.todoist-project-icon[data-label-color=charcoal]{--todoist-label-color: var(--todoist-charcoal)}.todoist-project-icon[data-project-color=grey]{--todoist-project-color: var(--todoist-grey)}.todoist-project-icon[data-label-color=grey]{--todoist-label-color: var(--todoist-grey)}.todoist-project-icon[data-project-color=taupe]{--todoist-project-color: var(--todoist-taupe)}.todoist-project-icon[data-label-color=taupe]{--todoist-label-color: var(--todoist-taupe)}.task-time-menu{padding:1em;display:flex;flex-direction:column}.task-time-menu>*+*{margin-top:1em}.task-time-menu .task-time-picker{display:flex;align-items:center;justify-content:space-between;font-size:var(--font-ui-small)}.task-time-menu .task-time-picker .task-time-picker-label{font-weight:600}.task-time-menu .task-time-picker .task-time-picker-input{display:flex;padding:4px;border-radius:6px;min-width:100px;border:1px solid var(--color-base-25)}.task-time-menu .task-time-picker .task-time-picker-input .task-time-picker-input-segment{padding:0 2px}.task-time-menu .task-time-picker .task-time-picker-input .task-time-picker-input-segment:focus{background-color:var(--interactive-accent);border-radius:4px;color:var(--text-on-accent)}.task-time-menu .task-duration-select{display:flex;align-items:center;justify-content:space-between;font-size:var(--font-ui-small)}.task-time-menu .task-duration-select .task-duration-picker-label{font-weight:600}.task-time-menu .task-duration-select .task-duration-button{justify-content:start;min-width:100px}.task-time-menu .task-time-controls{display:flex;justify-content:end}.task-time-menu .task-time-controls>*+*{margin-left:1em}.task-duration-menu .duration-option{padding:8px 1em;font-size:var(--font-smaller)}.task-duration-menu .duration-option:hover{background-color:var(--background-modifier-cover)}.task-duration-menu .duration-option.is-selected{background-color:var(--interactive-accent);color:var(--text-on-accent)}.obsidian-icon.token-validation-error{color:var(--text-error)}.obsidian-icon.token-validation-success{color:var(--text-success)}.obsidian-icon.token-validation-in-progress{color:var(--text-warning);animation-name:spin;animation-duration:.5s;animation-iteration-count:infinite;animation-timing-function:linear}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.todoist-onboarding-token-form{margin:2em 0}.todoist-onboarding-token-form .react-aria-TextField{display:flex;flex-direction:column}.todoist-onboarding-token-form .react-aria-TextField>*+*{margin-top:.5em}.todoist-onboarding-token-form .react-aria-TextField .react-aria-Group{display:flex;align-items:center}.todoist-onboarding-token-form .react-aria-TextField .react-aria-Group>*+*{margin-left:.5em}.todoist-onboarding-token-form .react-aria-TextField input{flex-grow:1}.todoist-onboarding-token-form .react-aria-TextField input[data-invalid]{border:1px solid var(--background-modifier-error)}.todoist-onboarding-token-form .react-aria-TextField .react-aria-Label{font-weight:600}.todoist-onboarding-token-form .react-aria-Button{float:right;margin-top:1em}.todoist-onboarding-token-form .react-aria-FieldError{color:var(--text-error);font-size:var(--font-small)}.mod-cta .setting-button-icon{margin-right:.5em}.setting-item-control .react-aria-TextField{display:flex;flex-direction:column}.setting-item-control .react-aria-TextField>*+*{margin-top:.5em}.setting-item-control .react-aria-TextField input[data-invalid]{border:1px solid var(--background-modifier-error)}.setting-item-control .react-aria-TextField .react-aria-FieldError{color:var(--text-error);font-size:var(--font-small)}.setting-item-deprecation-notice{display:flex;background-color:rgba(var(--color-yellow-rgb),.2);padding:4px 12px;border-radius:8px;margin-top:.5em}.setting-item-deprecation-notice .setting-item-deprecation-notice-message{margin-left:.5em}.setting-item-build-stamp{font-size:var(--font-smaller)}
 /* deprecated positioning, use bottom bar instead */
.view-content > .sc-brand {
  position: fixed;
  bottom: 0;
  right: 0;
  background-color: var(--titlebar-background);
}

.sc-brand {
  > svg,
  > p {
    display: inline;
    margin: 0 0.1rem 0 0.3rem;
    color: var(--text-muted);
    font-size: var(--font-smallest);
    line-height: 1;
    height: 0.88rem;
    width: auto;
  }

  > p > a {
    color: var(--text-muted);
  }
}

.sc-list {
  padding-bottom: 20px;

  .tree-item-self {
    cursor: pointer;

    small {
      color: var(--color-gray-40);
    }
  }

  > .sc-collapsed ul {
    display: none;
  }

  > .sc-collapsed span svg {
    transform: rotate(-90deg);
  }

  > :not(.sc-collapsed) span svg {
    transform: rotate(0deg);
  }

  > div {
    span svg {
      height: auto;
      margin: auto 0.5em auto 0;
      flex: none;
    }

    > span {
      display: inline-flex;
      width: 100%;
      padding-left: 0;
    }

    ul {
      margin: 0;
      padding-left: 1.3rem;
    }

    > a {
      display: block;
    }

    > ul > li {
      display: block;
    }
  }
  .sc-result {
    > ul {
      list-style: none;
      padding-left: 0;
    }
  }

  .sc-result.sc-result-plaintext {
    font-size: var(--font-ui-smaller);
    line-height: var(--line-height-tight);
    background-color: var(--search-result-background);
    border-radius: var(--radius-s);
    overflow: hidden;
    margin: var(--size-4-1) 0 var(--size-4-2);
    color: var(--text-muted);
    box-shadow: 0 0 0 1px var(--background-modifier-border);

    & > * li {
      cursor: var(--cursor);
      position: relative;
      padding: var(--size-4-2) var(--size-4-5) var(--size-4-2) var(--size-4-3);
      white-space: pre-wrap;
      width: 100%;
      border-bottom: 1px solid var(--background-modifier-border);
    }
  }

  .sc-result:not(.sc-result-plaintext) {
    cursor: pointer;
    padding: var(--nav-item-padding);
    padding-left: 10px;
    margin-bottom: 1px;
    align-items: baseline;
    border-radius: var(--radius-s);
    font-weight: var(--nav-item-weight);

    &:hover {
      color: var(--nav-item-color-active);
      background-color: var(--nav-item-background-active);
      font-weight: var(--nav-item-weight-active);
    }

    span {
      color: var(--h5-color);
    }

    small {
      color: var(--h5-color);
      font-size: 0.8rem;
      font-weight: 500;
    }

    p {
      margin-top: 0.3rem;
      margin-bottom: 0.3rem;
    }

    ul > li {
      h1 {
        font-size: 1.3rem;
      }

      h2 {
        font-size: 1.25rem;
      }

      h3 {
        font-size: 1.2rem;
      }

      h4 {
        font-size: 1.15rem;
      }

      h5 {
        font-size: 1.1rem;
      }

      h6 {
        font-size: 1.05rem;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-block-start: calc(var(--p-spacing)/2);
        margin-block-end: calc(var(--p-spacing)/2);
      }
    }
  }
} /* end .sc-list */

/* Only on right sidebar */
.mod-right-split .sc-list .sc-result {
  font-size: var(--font-text-size);
  font-size: 0.88rem;
}

.sc-top-bar {
  display: flex;
  width: 100%;
  justify-content: end;

  .sc-context {
    color: var(--nav-item-color);
    font-size: var(--nav-item-size);
    margin: 0.5em 0.5em 1em;
    width: 100%;
  }
}

/* Chat */
.sc-chat-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  height: 100%;

  .sc-top-bar-container {
    align-self: flex-end;
    display: flex;
    width: 100%;

    .sc-chat-name-input {
      flex-grow: 1;
      min-width: 20px;
    }
  }

  .sc-thread {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    height: 100%;
    width: 100%;
    overflow: hidden;
    user-select: text;
    overflow-y: auto;

    .sc-message-container {
      border: 1px solid var(--divider-color);
      border-radius: 10px;
      margin: 0.5rem 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
      height: 100%;
      overflow-y: auto;
      background-color: var(--background-primary-alt);

      .sc-message {
        max-width: 90ch;
        width: 90%;
        margin: 10px;
        padding: 10px;
        border-radius: 1.5rem;
        word-break: break-word;

        &.user {
          align-self: flex-end;
          color: var(--text-normal);
          background-color: var(--background-primary);
        }

        &.assistant,
        &.system {
          background-color: var(--background-primary-alt);
          color: var(--text-normal);
        }

        .sc-message-content {
          margin: 0;
          padding: 1rem;

          > * p {
            margin: 0;
          }
        }
      }
    }

    .sc-chat-form {
      display: flex;
      padding: 0 10px 1rem 0;
      width: 100%;
      max-height: 50%;

      .sc-chat-input {
        flex-grow: 1;
        padding: 0.88rem;
        border: none;
        border-radius: 1.5rem;
        resize: none;
        height: auto;
        min-height: 4.2lh;
        max-height: 100%;
        background-color: var(--background-primary);
        color: var(--text-normal);
        margin-right: -2.7rem;
        padding-right: 3rem;
      }

      .sc-btn-container {
        width: 2rem;
        height: 2rem;
        margin: auto;

        > button.send-button {
          border-radius: 99999px;
          cursor: pointer;
          outline: 2px solid transparent;
          padding: 0;
          outline-offset: 2px;
          background: none !important;

          &:focus-visible {
            outline-color: var(--text-faint);
            box-shadow: none;
          }

          > svg {
            > circle {
              fill: var(--text-faint);
            }
            > path {
              fill: var(--background-primary);
            }
          }
        }
      }
    }
  }

  #settings {
    display: flex;
    flex-direction: column;
    max-width: 100%;
    width: 100%;
  }
}

.sc-system {
  align-self: center;
  font-style: italic;
  color: var(--text-faint);
}

.sc-msg-button {
  cursor: pointer;
  float: right;
  margin-left: 5px;
  opacity: 0.8;

  &.cycle-branch {
    float: left;
    display: flex;
  }

  &:hover {
    opacity: 1;
  }
}

#sc-abort-button {
  cursor: pointer;
  padding: 10px;
  border-radius: 5px;

  &:hover {
    background-color: var(--background-primary);
  }
}

.markdown-source-view.mod-cm6 .cm-embed-block:not(.cm-table-widget):hover:has(.sc-change) {
  overflow: unset;
  box-shadow: unset;
  cursor: unset;
}

.notice .sc-notice-actions {
  display: flex;
  justify-content: space-between;
  flex-direction: row-reverse;
}

.sc-chat-container {
  #settings {
    display: flex;
    flex-direction: column;
    max-width: 100%;
    width: 100%;
  }

  .sc-config-error-notice {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background-color: #ffcccc;
    border: 1px solid #ff0000;
    border-radius: 5px;
    margin: 10px 0;
    font-size: 14px;
    font-weight: bold;
    color: #ff0000;
    width: 100%;

    span {
      flex-grow: 1;
    }

    button {
      margin-left: 10px;
    }
  }
}

.sc-bottom-bar {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--titlebar-background);
  padding: 0 0.5rem;

  .sc-brand {
    flex-shrink: 0;
  }

  .sc-context {
    flex-grow: 1;
    font-size: var(--font-smallest);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.setting-component[data-setting*="/"][data-setting*="api_key"] {
  display: none;
}

.setting-component[data-setting*="gpu"]:not([data-setting*="/"]) {
  display: none;
}

/* SINCE COMPONENT PATTERN SETTINGS */

.setting-component[data-setting="smart_change.active"] {
  display: none;
}

.group-header {
  display: flex;
  text-wrap: nowrap;
  flex-wrap: wrap;
  align-items: baseline;

  > h2 {
    width: 100%;
    margin-bottom: 0;
  }

  > * {
    flex-grow: 1;
    margin-bottom: 10px;
  }
}

/* SMART CHAT v2 */
.sc-context-list {
  list-style: none;
  margin: 0;
  padding: 0 1rem 1rem;
  display: none;
  flex-direction: column;
  gap: 0.5rem;
}

.sc-context-header[aria-expanded="true"] + .sc-context-list {
  display: flex;
}

.sc-context-header[aria-expanded="false"] + .sc-context-list {
  display: none;
}

.sc-context-toggle-icon {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.sc-context-header[aria-expanded="true"] .sc-context-toggle-icon {
  transform: rotate(180deg);
}

.sc-context-container {
  border: 1px solid var(--divider-color);
  border-radius: 10px;
  margin: 0.5rem 0;
  background-color: var(--background-primary-alt);
  overflow: auto;
  max-width: 95%;
  margin: 10px;
  flex-shrink: 0;

  .sc-context-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.88rem 1rem;
    background-color: var(--background-primary);
    color: var(--text-muted);
    font-weight: var(--font-medium);
    cursor: pointer;
    user-select: none;
  
    &:hover {
      background-color: var(--background-primary-hover);
    }
  
    &:focus {
      outline: 2px solid var(--text-muted);
      outline-offset: 2px;
    }
  }
}


.sc-context-item {
  padding: 0.5rem;
  border-radius: var(--radius-s);
  background-color: var(--background-secondary);
  color: var(--text-normal);
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  font-size: var(--font-smallest);
  flex-wrap: wrap;

  &:hover {
    background-color: var(--background-secondary-hover);
  }
}

.sc-context-item-path {
  font-weight: var(--font-medium);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 70%;
}

.sc-context-item-score {
  font-size: var(--font-small);
  color: var(--color-gray-40);
}

/* Tool Calls Component Styles */
.sc-tool-calls-container {
  border: 1px solid var(--divider-color);
  border-radius: 10px;
  margin: 0.5rem 0;
  background-color: var(--background-primary-alt);
  overflow: auto;
  max-width: 95%;
  margin: 10px;
  flex-shrink: 0;
}

.sc-tool-call {
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.sc-tool-call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.88rem 1rem;
  background-color: var(--background-primary);
  color: var(--text-muted);
  font-weight: var(--font-medium);
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: var(--background-primary-hover);
  }

  &:focus {
    outline: 2px solid var(--text-muted);
    outline-offset: 2px;
  }

  &[aria-expanded="true"] .sc-tool-call-toggle-icon {
    transform: rotate(180deg);
  }
}

.sc-tool-call-content {
  padding: 0.5rem 1rem;
  background-color: var(--background-secondary);
  font-size: var(--font-smallest);

  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
  }

  code {
    font-family: var(--font-monospace);
  }
}

/* Hide content when collapsed */
.sc-tool-call-header[aria-expanded="false"] + .sc-tool-call-content {
  display: none;
}

/* Show content when expanded */
.sc-tool-call-header[aria-expanded="true"] + .sc-tool-call-content {
  display: block;
}

/* System Message Styles */
.sc-system-message-container {
  margin: 1rem 0;
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  background: var(--background-secondary);
  flex-shrink: 0;
}

.sc-system-message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--background-modifier-hover);
  }

  span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .sc-system-message-toggle-icon {
    transition: transform 0.2s ease;
  }

  &[aria-expanded="true"] {
    border-bottom-color: var(--background-modifier-border);
  }
}

.sc-system-message-content {
  padding: 1rem;
  position: relative;
  background: var(--background-primary);
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;

  .sc-system-message-text {
    font-family: var(--font-monospace);
    white-space: pre-wrap;
    word-break: break-word;
    margin-right: 2rem;
  }

  .sc-system-message-copy {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.4rem;
    background: transparent;
    border: none;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }

    &.sc-copied {
      color: var(--text-accent);
    }
  }
}

.sc-chat-container {
  .smart-chat-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-primary-alt);
    z-index: 100;
    overflow: auto;

    .smart-chat-overlay-header {
      display: flex;
      justify-content: flex-end;
    }
    .setting-item {
      flex-direction: column;
      align-items: flex-start;
    }
  }
}

.sc-typing-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  display: none;

  &.visible {
    display: flex;
  }

  .sc-typing-dots {
    display: flex;
    gap: 4px;

    .sc-typing-dot {
      width: 8px;
      height: 8px;
      background: var(--text-muted);
      border-radius: 50%;
      animation: typing-bounce 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: 0s;
      }

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

}
/* keyframes must be at root level */
@keyframes typing-bounce {
  0%,
  80%,
  100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1);
  }
}


/* Lookup */
#sc-lookup-view {
  .sc-container {
    .sc-textarea-container {
      display: flex;
      padding: 0 10px 1rem 0;
      width: 100%;
      max-height: 50%;

      > textarea {
        flex-grow: 1;
        padding: 0.88rem;
        border: none;
        border-radius: 1.5rem;
        resize: none;
        height: auto;
        min-height: 4.2lh;
        max-height: 100%;
        background-color: var(--background-primary);
        color: var(--text-normal);
        margin-right: -2.7rem;
        padding-right: 3rem;
      }
      .sc-textarea-btn-container {
        width: 2rem;
        height: 2rem;
        margin: auto;

        > button.send-button {
          border-radius: 99999px;
          cursor: pointer;
          outline: 2px solid transparent;
          padding: 0;
          outline-offset: 2px;
          background: none !important;

          &:focus-visible {
            outline-color: var(--text-faint);
            box-shadow: none;
          }

          > svg {
            > circle {
              fill: var(--text-faint);
            }
            > path {
              fill: var(--background-primary);
            }
          }
        }
      }
    }
  }
}

/* Side Panes */
div.workspace-leaf-content[data-type^="smart-"] {
  > .view-content {
    display: flex;
    flex-direction: column;
  }
}

.sc-inline-confirm-row {
  margin-top: 10px;
  padding: 6px;
  border: 1px solid var(--interactive-normal);
}
.sc-inline-confirm-row-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;

  & .sc-inline-confirm-yes {
    font-weight: bold;
  }
  & .sc-inline-confirm-cancel {
    font-weight: normal;
  }
}

.sc-story-modal {
  width: 80% !important;
  height: 80% !important;
}
.sc-story-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sc-other-plugins {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;

  button {
    padding: 10px;
    border-radius: var(--radius-s);
    background-color: var(--background-primary);
    color: var(--text-normal);
    border: none;
    cursor: pointer;
    font-size: var(--font-small);

    &:hover {
      background-color: var(--background-primary-hover);
    }
  }
} .smtcmp-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--size-4-2);
}

.smtcmp-chat-header-title {
  font-size: var(--font-ui-medium);
  font-weight: var(--font-medium);
  margin: 0;
}

.smtcmp-chat-header-buttons {
  display: flex;
  gap: var(--size-2-1);

  svg {
    height: var(--icon-l);
    width: var(--icon-l);
    stroke-width: var(--icon-stroke);
  }
}

.smtcmp-markdown {
  line-height: var(--line-height-normal);
  font-size: var(--font-ui-small);

  h1 {
    font-size: var(--font-ui-large);
  }

  h2 {
    font-size: var(--font-ui-medium);
  }

  h3 {
    font-size: var(--font-ui-small);
  }

  h4 {
    font-size: var(--font-ui-smaller);
  }

  h5 {
    font-size: var(--font-ui-smallest);
  }

  h6 {
    font-size: var(--font-ui-smallest);
  }

  p {
    font-size: var(--font-ui-small);
  }

  ul {
    font-size: var(--font-ui-small);
    padding-left: var(--size-4-4);
  }

  ol {
    font-size: var(--font-ui-small);
    padding-left: var(--size-4-4);
  }

  li {
    font-size: var(--font-ui-small);
  }

  blockquote {
    font-size: var(--font-ui-small);
    font-style: var(--blockquote-style);
    background-color: var(--blockquote-background-color);
    margin: 0;
    padding-left: var(--size-4-2);
    border-left: var(--blockquote-border-thickness) solid
      var(--blockquote-border-color);
  }

  code {
    font-size: var(--font-ui-small);
    border-radius: var(--code-radius);
    padding: 0.1em 0.25em;
    color: var(--code-normal);
    font-size: var(--code-size);
    background-color: var(--code-background);
    vertical-align: baseline;
  }

  table {
    font-size: var(--font-ui-small);
  }

  thead {
    font-size: var(--font-ui-small);
  }

  tbody {
    font-size: var(--font-ui-small);
  }

  tr {
    font-size: var(--font-ui-small);
  }

  td {
    font-size: var(--font-ui-small);
  }

  th {
    font-size: var(--font-ui-small);
  }
}

.smtcmp-chat-container {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;

  .smtcmp-stop-gen-btn {
    z-index: 1000;
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: var(--size-4-1);
  }
}

.smtcmp-chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  user-select: text;
  display: flex;
  flex-direction: column;
  gap: var(--size-4-1);
  padding: var(--size-4-1);
  margin: calc(var(--size-4-1) * -1);

  .smtcmp-chat-messages-user {
    display: flex;
    flex-direction: column;
    gap: var(--size-4-1);
  }

  .smtcmp-chat-messages-assistant {
    display: flex;
    flex-direction: column;
  }

  .smtcmp-assistant-tool-message-group {
    padding-bottom: var(--size-4-2);
    display: flex;
    flex-direction: column;
    gap: var(--size-4-1);
  }
}

.obsidian-default-textarea {
  -webkit-app-region: no-drag;
  background: var(--background-modifier-form-field);
  color: var(--text-normal);
  font-family: inherit;
  padding: 0;
  font-size: var(--font-ui-small);
  outline: none;
  min-height: 80px;
  max-height: 200px;
  overflow-y: auto;
  font-size: var(--font-ui-small);
  padding: var(--size-2-1);
}

@media (hover: hover) {
  .obsidian-default-textarea:hover {
    border-color: var(--background-modifier-border-hover);
    transition:
      box-shadow 0.15s ease-in-out,
      border 0.15s ease-in-out;
  }
}
.obsidian-default-textarea:active {
  border-color: var(--background-modifier-border-focus);
  transition:
    box-shadow 0.15s ease-in-out,
    border 0.15s ease-in-out;
}

/* .obsidian-default-textarea::placeholder {
  color: var(--text-faint);
} */
.obsidian-default-textarea {
  line-height: var(--line-height-tight);
}

.smtcmp-chat-user-input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  -webkit-app-region: no-drag;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  color: var(--text-normal);
  font-family: inherit;
  padding: calc(var(--size-2-3) + 1px);
  font-size: var(--font-ui-small);
  border-radius: var(--radius-s);
  outline: none;

  &:focus-within,
  &:focus,
  &:focus-visible,
  &:active {
    box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
    transition: box-shadow 0.15s ease-in-out;
  }
}

.smtcmp-chat-user-input-files {
  display: flex;
  flex-direction: row;
  gap: var(--size-4-1);
  flex-wrap: wrap;
  padding-bottom: var(--size-4-1);
}

.smtcmp-chat-user-input-controls {
  display: flex;
  flex-direction: row;
  gap: var(--size-4-1);
  justify-content: space-between;
  align-items: center;
  height: var(--size-4-4);

  .smtcmp-chat-user-input-controls__model-select-container {
    flex-shrink: 1;
    overflow: hidden;
  }

  .smtcmp-chat-user-input-controls__buttons {
    flex-shrink: 0;
    display: flex;
    gap: var(--size-4-2);
    align-items: center;
  }
}

.smtcmp-chat-user-input-controls .smtcmp-chat-user-input-submit-button {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
  font-size: var(--font-smallest);
  color: var(--text-muted);
  padding: 0 var(--size-2-1);
  cursor: pointer;
  transition: all 0.1s ease-in-out;

  &:hover {
    color: var(--text-normal);
  }

  .smtcmp-chat-user-input-submit-button-icons {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.smtcmp-chat-user-input-file-badge {
  display: flex;
  align-items: center;
  background-color: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  font-size: var(--font-smallest);
  padding: var(--size-2-1) var(--size-4-1);
  gap: var(--size-2-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;

  &.smtcmp-chat-user-input-file-badge-focused {
    border: 1px solid var(--interactive-accent);
  }

  .smtcmp-excluded-content {
    text-decoration: line-through;
    font-style: italic;
  }

  svg {
    flex-shrink: 0;
  }
}

.smtcmp-chat-user-input-file-badge:hover {
  background-color: var(--background-modifier-hover);
}

.smtcmp-chat-user-input-file-badge-delete {
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--text-muted);
}

.smtcmp-chat-user-input-file-badge-eye {
  height: 100%;
  display: flex;
  align-items: center;
  margin: 0 var(--size-2-1) 0 var(--size-4-1);
}

.smtcmp-chat-user-input-file-badge-name {
  display: flex;
  flex-direction: row;
  gap: var(--size-2-1);
  flex-grow: 1;
  overflow: hidden;
  align-items: center;
}

.smtcmp-chat-user-input-file-badge-name-icon {
  color: var(--text-muted);
}

.smtcmp-chat-user-input-file-badge-name span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.smtcmp-chat-user-input-file-badge-name-suffix {
  color: var(--text-faint);
  flex-grow: 0;
}

.smtcmp-chat-user-input-file-badge-current {
  color: var(--color-base-50);
}

.smtcmp-chat-user-input-file-content-preview {
  background-color: var(--background-secondary);
  border-radius: var(--radius-s);
  border: 1px solid var(--background-modifier-border);
  max-height: 350px;
  overflow-y: auto;
  padding: 0 var(--size-4-2);

  img {
    max-width: 100%;
    max-height: 350px;
  }
}

/**
 * ChatUserInput
 */

.smtcmp-lexical-content-editable-root .mention {
  background-color: var(--tag-background);
  color: var(--tag-color);
  padding: var(--size-2-1) calc(var(--size-2-1));
  border-radius: var(--radius-s);
  background-color: var(--tag-background);
  color: var(--tag-color);
  padding: 0 calc(var(--size-2-1));
  border-radius: var(--radius-s);
  word-break: break-all;
}

.smtcmp-lexical-content-editable-paragraph {
  margin: 0;
  line-height: 1.6;
}

.smtcmp-popover {
  z-index: 1000;
  background: var(--background-primary);
  box-shadow: var(--shadow-s);
  border-radius: var(--radius-m);
  border: 1px solid var(--background-modifier-border);
  overflow: hidden;
}

.smtcmp-popover ul {
  padding: 0;
  list-style: none;
  margin: 0;
  max-height: 200px;
  overflow-y: scroll;
}

.smtcmp-popover ul {
  padding: var(--size-4-1) 0;
}

.smtcmp-popover ul li {
  margin: 0;
  min-width: 180px;
  font-size: var(--font-ui-smaller);
  outline: none;
  cursor: pointer;
  border-radius: 0;
}

.smtcmp-popover ul li.selected {
  background: var(--background-modifier-hover);
}

.smtcmp-popover li {
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  margin: 0 8px 0 8px;
  padding: var(--size-2-3) var(--size-4-2);
  color: var(--text-normal);
  cursor: pointer;
  line-height: var(--line-height-tight);
  font-size: var(--font-ui-smaller);
  display: flex;
  align-content: center;
  flex-direction: row;
  flex-shrink: 0;
  background-color: var(--background-primary);
  border-radius: 8px;
  border: 0;
  min-height: 20px;
  align-items: center;
  gap: var(--size-4-1);
  align-items: start;
}

.smtcmp-popover li.active {
  display: flex;
  width: 20px;
  height: 20px;
  background-size: contain;
}

.smtcmp-popover li:hover {
  background-color: var(--background-modifier-hover);
}

.smtcmp-popover-item-icon {
  display: flex;
  user-select: none;
  line-height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  height: 14px;
  padding-top: 1px;
  align-items: center;
  color: var(--text-muted);
  min-width: fit-content;
}

.smtcmp-popover li:hover {
  background-color: var(--background-modifier-hover);
  cursor: pointer;
}
.smtcmp-popover li .smtcmp-chat-list-dropdown-item-icon {
  visibility: hidden;
}
.smtcmp-popover li:hover .smtcmp-chat-list-dropdown-item-icon {
  visibility: visible;
}
.smtcmp-popover li .smtcmp-chat-list-dropdown-item-icon:hover {
  background-color: var(--background-modifier-hover);
  border-radius: var(--radius-s);
}

.smtcmp-chat-list-dropdown-empty {
  background: transparent;
  cursor: default;
  color: var(--text-faint);
}

.smtcmp-chat-list-dropdown-content {
  width: 280px;
  max-width: 280px;
}

.smtcmp-chat-list-dropdown-content li {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.smtcmp-chat-list-dropdown-item-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

input[type='text'].smtcmp-chat-list-dropdown-item-title-input {
  width: 100%;
  font-size: var(--font-ui-smaller);
}

.smtcmp-chat-list-dropdown-item-actions {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);

  svg {
    height: var(--icon-xs);
    width: var(--icon-xs);
    stroke-width: var(--icon-stroke);
  }
}

.smtcmp-code-block {
  position: relative;
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
}

.smtcmp-code-block code {
  padding: 0;
}

.smtcmp-code-block-header {
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-smallest);
  display: flex;
  border-bottom: 1px solid var(--background-modifier-border);
  background-color: var(--background-primary);
  border-radius: var(--radius-s) var(--radius-s) 0 0;
  height: calc(var(--size-4-8) - var(--size-4-1));
}

.smtcmp-code-block-header-filename {
  padding-left: var(--size-4-2);
  font-size: var(--font-medium);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.smtcmp-code-block-header-button-container {
  margin-left: auto;
  min-width: fit-content;
  height: 100%;
  display: flex;
  gap: 0;
  overflow: hidden;

  .smtcmp-code-block-header-button {
    height: 100%;
    padding: 0 var(--size-4-2);
    display: flex;
    gap: var(--size-4-1);
    font-size: var(--font-small);
  }

  /* Override clickable-icon color */
  .smtcmp-code-block-header-button.clickable-icon {
    color: var(--text-color) !important;
  }
}

.smtcmp-code-block-content {
  margin: 0;
}

.smtcmp-code-block-obsidian-markdown {
  padding: var(--size-4-3);
}

#smtcmp-apply-view {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  font-family: var(--font-interface);

  --smtcmp-current-color-rgb: 185, 28, 28; /* red-700 */
  --smtcmp-incoming-color-rgb: 4, 120, 87; /* emerald-700 */

  .view-content {
    padding: 0;
  }

  .markdown-source-view.mod-cm6 {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .cm-editor {
    flex: 1 1 0;
    min-height: 0;
    position: relative !important;
    box-sizing: border-box;
    display: flex !important;
    flex-direction: column;
  }

  .cm-scroller {
    padding: var(--file-margins);
    display: flex !important;
    align-items: flex-start !important;
    line-height: 1.4;
    height: 100%;
    overflow-x: auto;
    position: relative;
    z-index: 0;
  }

  .cm-sizer {
    max-width: var(--file-line-width);
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-bottom: 488px;
  }

  .view-header {
    height: var(--header-height);
    display: flex;
    border-bottom: var(--file-header-border);
    background-color: var(--background-primary);
    z-index: 1;
    position: relative;
    gap: var(--size-4-2);
    padding: 0 var(--size-4-3);
  }

  .view-header-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
  }

  .view-actions {
    button {
      color: var(--text-normal);
      font-weight: var(--font-medium);
      gap: 2px;
    }
  }

  .smtcmp-diff-block {
    white-space: pre-wrap;
    word-break: break-word;
    display: flex;
    align-items: start;
    gap: var(--size-4-1);
    color: var(--text-normal);
    line-height: var(--line-height-normal);
  }

  .smtcmp-diff-block.added {
    background-color: rgba(var(--smtcmp-incoming-color-rgb), 0.3);
  }

  .smtcmp-diff-block.removed {
    background-color: rgba(var(--smtcmp-current-color-rgb), 0.3);
  }

  .smtcmp-diff-block-container {
    position: relative;
    scroll-margin-top: var(--size-4-8);
  }

  .smtcmp-diff-block-actions {
    position: absolute;
    right: 0;
    top: 0;
    transform: translateY(-100%);
    display: flex;
    gap: 0;
    border-radius: var(--radius-s) var(--radius-s) 0 0;
    overflow: hidden;

    button {
      padding: var(--size-4-1) var(--size-4-2);
      font-size: var(--font-ui-smaller);
      border-radius: 0;
      height: fit-content;
    }
  }

  .smtcmp-accept {
    color: white;
    background: rgba(var(--smtcmp-incoming-color-rgb), 0.8);
    &:hover {
      background: rgba(var(--smtcmp-incoming-color-rgb), 1);
    }
  }

  .smtcmp-exclude {
    color: white;
    background: rgba(var(--smtcmp-current-color-rgb), 0.8);
    &:hover {
      background: rgba(var(--smtcmp-current-color-rgb), 1);
    }
  }

  .smtcmp-diff-navigation {
    display: flex;
    align-items: center;
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

button.smtcmp-chat-input-model-select {
  background-color: transparent !important;
  box-shadow: none !important;
  border: 0 !important;
  padding: 0 !important;
  font-size: var(--font-smallest) !important;
  font-weight: var(--font-medium) !important;
  color: var(--text-muted) !important;
  cursor: pointer !important;
  height: auto !important;

  &:hover {
    color: var(--text-normal) !important;
  }

  .smtcmp-chat-input-model-select__model-name {
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .smtcmp-chat-input-model-select__icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.smtcmp-query-progress {
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
}

.smtcmp-query-progress-detail {
  font-size: var(--font-smallest);
  color: var(--text-faint);
}

.smtcmp-dot-loader {
  display: inline-block;
  text-align: left;
}

.smtcmp-dot-loader::after {
  content: '...';
  animation: dotFade 0.75s steps(4, end) infinite;
  color: var(--text-muted);
}

@keyframes dotFade {
  0%,
  100% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
}

.smtcmp-tooltip-content {
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  padding: var(--size-4-1) var(--size-4-2);
  font-size: var(--font-smallest);
  animation: fadeIn 0.1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.smtcmp-settings-textarea {
  display: block;
  border-top: none;
  padding: 0;

  .smtcmp-item-control {
    width: 100%;
  }

  .setting-item-control {
    padding-bottom: var(--size-4-3);
  }

  textarea {
    width: 100%;
    min-height: 100px;
    resize: none;
  }
}

/* prevent setting-item:first-child overwriting padding-top and border-top */
.smtcmp-settings-textarea-header {
  padding-top: 0.75em !important;
  border-top: 1px solid var(--background-modifier-border) !important;
}

.smtcmp-settings-model-container {
  margin: var(--size-4-2) 0;
  padding: var(--size-4-2) var(--size-4-4);
  border-left: 2px solid var(--interactive-accent);
  background-color: var(--background-secondary);
  border-radius: var(--radius-s);

  .setting-item {
    border-top: none;

    &:first-child {
      margin-top: var(--size-4-2);
    }
  }
}

.smtcmp-dialog-content {
  position: fixed;
  left: calc(50% - var(--size-4-4));
  top: 50%;
  z-index: 50;
  display: grid;
  width: calc(100% - var(--size-4-8));
  max-width: 32rem;
  transform: translate(-50%, -50%);
  gap: var(--size-4-2);
  border: var(--border-width) solid var(--background-modifier-border);
  background-color: var(--background-secondary);
  padding: var(--size-4-5);
  transition-duration: 200ms;
  border-radius: var(--radius-m);
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  margin: var(--size-4-4);

  .smtcmp-dialog-header {
    margin-bottom: var(--size-4-2);
    display: grid;
    gap: var(--size-2-3);
  }

  .smtcmp-dialog-title {
    font-size: var(--font-ui-medium);
    font-weight: var(--font-semibold);
    line-height: var(--line-height-tight);
    margin: 0;
  }

  .smtcmp-dialog-input {
    display: grid;
    gap: var(--size-4-1);

    & label {
      font-size: var(--font-ui-smaller);
    }
  }

  .smtcmp-dialog-description {
    font-size: var(--font-ui-small);
    color: var(--text-muted);
    margin: 0;
  }

  .smtcmp-dialog-footer {
    margin-top: var(--size-4-2);
    display: flex;
    justify-content: flex-end;
  }

  .smtcmp-dialog-close {
    position: absolute;
    right: var(--size-4-4);
    top: var(--size-4-4);
    cursor: var(--cursor);
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

.smtcmp-template-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--size-4-1);
  width: 100%;

  .smtcmp-template-menu-item-delete {
    display: flex;
    align-items: center;
    padding: var(--size-4-1);
    margin: calc(var(--size-4-1) * -1);
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

.smtcmp-assistant-message-actions {
  display: flex;
  align-items: center;
  justify-content: end;

  /* button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    width: 20px;
    padding: 0;
    background-color: transparent;
    border-color: transparent;
    box-shadow: none;
    color: var(--text-faint);
    cursor: pointer;

    &:hover {
      background-color: var(--background-modifier-hover);
    }
  }

  .smtcmp-assistant-message-actions-icon--copied {
    color: var(--text-muted);
  } */
}

.smtcmp-popover-content {
  z-index: 1000;
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  padding: var(--size-4-2);
  font-size: var(--font-smallest);
  animation: fadeIn 0.1s ease-in-out;
}

.smtcmp-similarity-search-results {
  display: flex;
  flex-direction: column;
  font-size: var(--font-smaller);
  padding-top: var(--size-4-1);
  padding-bottom: var(--size-4-1);
  user-select: none;

  .smtcmp-similarity-search-results__trigger {
    display: flex;
    align-items: center;
    gap: var(--size-4-1);
    padding: var(--size-4-1);
    border-radius: var(--radius-s);
    cursor: pointer;
    &:hover {
      background-color: var(--background-modifier-hover);
    }
  }

  .smtcmp-similarity-search-item {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: var(--size-4-2);
    padding: var(--size-4-1);
    border-radius: var(--radius-s);
    cursor: pointer;

    &:hover {
      background-color: var(--background-modifier-hover);
    }

    .smtcmp-similarity-search-item__similarity {
      flex-shrink: 0;
      font-size: var(--font-smallest);
      color: var(--text-muted);
    }

    .smtcmp-similarity-search-item__path {
      flex-shrink: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: var(--font-smallest);
    }

    .smtcmp-similarity-search-item__line-numbers {
      flex-shrink: 0;
      margin-left: auto;
      font-size: var(--font-smallest);
    }
  }
}

.smtcmp-llm-info-content {
  width: 320px;
  display: grid;
  gap: var(--size-4-3);
}

.smtcmp-llm-info-header {
  display: grid;
  gap: var(--size-2-2);
  font-size: var(--font-ui-small);
  font-weight: var(--font-semibold);
}

.smtcmp-llm-info-tokens {
  display: grid;
  gap: var(--size-4-2);
}

.smtcmp-llm-info-tokens-header {
  font-size: var(--font-ui-small);
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-tokens-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: var(--size-4-5);
  row-gap: var(--size-4-2);
  font-size: var(--font-ui-small);
}

.smtcmp-llm-info-token-row {
  display: flex;
  align-items: center;
  gap: var(--size-2-3);
}

.smtcmp-llm-info-token-value {
  margin-left: auto;
  color: var(--text-muted);
}

.smtcmp-llm-info-token-total {
  grid-column: span 2;
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-footer-row {
  display: flex;
  align-items: center;
  gap: var(--size-2-3);
  font-size: var(--font-ui-small);
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-footer-value {
  margin-left: auto;
}

.smtcmp-llm-info-model {
  color: var(--text-muted);
}

.smtcmp-llm-info-icon--input {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--color-green);
}

.smtcmp-llm-info-icon--output {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--color-blue);
}

.smtcmp-llm-info-icon--total {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--text-normal);
}

.smtcmp-llm-info-icon--footer {
  height: var(--size-4-4);
  width: var(--size-4-4);
}

/* Settings */

.smtcmp-settings-support-smart-composer {
  border-top: none;
}

.smtcmp-settings-section:not(:first-child) {
  margin-top: var(--size-4-10);
}

.smtcmp-settings-header {
  color: var(--text-normal);
  font-size: var(--h1-size);
  line-height: var(--line-height-tight);
  font-weight: var(--h1-weight);
  margin: var(--size-4-2) 0;
}

.smtcmp-settings-sub-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.smtcmp-settings-sub-header {
  color: var(--text-normal);
  font-size: var(--h4-size);
  line-height: var(--line-height-tight);
  font-weight: var(--h4-weight);
  margin: var(--size-4-3) 0;
}

.smtcmp-settings-desc {
  color: var(--text-muted);
  font-size: var(--font-ui-small);
  line-height: var(--line-height-tight);
  margin: var(--size-4-1) 0;
}

.smtcmp-settings-callout {
  padding-inline-start: var(--size-4-2);
  border-inline-start: var(--blockquote-border-thickness) solid
    var(--blockquote-border-color);
}

.smtcmp-settings-required::after {
  color: var(--color-red);
  content: '*';
  display: inline-block;
  font-size: var(--font-ui-medium);
  font-weight: var(--font-bold);
  margin-left: var(--size-4-1);
}

/* Settings: Embedding DB Manage */

.smtcmp-settings-embedding-db-manage-root {
  padding: var(--size-4-2);
}

.smtcmp-settings-embedding-db-manage-header {
  display: flex;
  align-items: center;
  gap: var(--size-4-2);
  font-size: var(--font-ui-small);
}

.smtcmp-settings-embedding-db-manage-table {
  width: 100%;
  border-collapse: collapse;
}

.smtcmp-settings-embedding-db-manage-table tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-embedding-db-manage-table th,
.smtcmp-settings-embedding-db-manage-table td {
  padding: var(--size-4-2);
  text-align: left;
}

.smtcmp-settings-embedding-db-manage-table th {
  font-weight: var(--font-medium);
  color: var(--text-muted);
  padding-bottom: var(--size-4-3);
}

.smtcmp-settings-embedding-db-manage-table td {
  padding-top: var(--size-4-3);
  padding-bottom: var(--size-4-3);
  vertical-align: middle;
}

.smtcmp-settings-embedding-db-manage-actions {
  display: flex;
  gap: var(--size-4-2);
}

.smtcmp-settings-embedding-db-manage-actions-loading {
  display: flex;
  align-items: center;
  gap: var(--size-2-2);
  font-size: var(--font-ui-smaller);
}

/* Settings: tables */

.smtcmp-settings-table-container {
  overflow-x: auto;
}

.smtcmp-settings-table {
  margin: var(--size-4-3) 0;
  width: 100%;
  border-collapse: collapse;
}

.smtcmp-settings-table th {
  padding: var(--size-4-1) var(--size-4-1) var(--size-4-2) var(--size-4-1);
  text-align: left;
  vertical-align: middle;
  font-weight: var(--font-medium);
  color: var(--text-muted);
}

.smtcmp-settings-table tbody td {
  height: var(--size-4-10);
  padding: var(--size-4-1);
  text-align: left;
  vertical-align: middle;
}

.smtcmp-settings-table tfoot td {
  padding: var(--size-4-1);
  text-align: right;
  vertical-align: middle;
}

.smtcmp-settings-table thead tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-table tbody tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-actions {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);

  svg {
    height: var(--icon-l);
    width: var(--icon-l);
    stroke-width: var(--icon-stroke);
  }
}

.smtcmp-settings-table-api-key {
  cursor: pointer;
  color: var(--text-muted);
  font-size: var(--font-ui-small);
}

.smtcmp-settings-table-api-key:hover {
  text-decoration: underline;
}

.smtcmp-error-modal {
  min-width: 60vw;
}

.smtcmp-error-modal-content {
  max-height: 50vh;
  display: flex;
  flex-direction: column;
  user-select: text;
}

.smtcmp-error-modal-message {
  white-space: pre-line;
}

.smtcmp-error-modal-log {
  white-space: pre-wrap;
  word-break: break-word;
  margin-top: 1rem;
  flex-grow: 1;
  overflow-y: auto;
  user-select: text;
  cursor: text;
  font-size: var(--font-ui-small);
}

.smtcmp-error-modal-buttons {
  margin-top: 1rem;
}

.smtcmp-assistant-message-metadata {
  display: flex;
  flex-direction: column;
  margin-top: var(--size-4-1);
  border-left: 2px solid var(--background-modifier-border);
  padding-left: var(--size-4-1);
}

.smtcmp-assistant-message-metadata-content {
  color: var(--text-muted);
  padding-left: var(--size-4-1);
  font-size: var(--font-ui-small);
}

.smtcmp-assistant-message-metadata-annotations {
  display: flex;
  flex-direction: column;
  gap: var(--size-2-1);
}

.smtcmp-assistant-message-metadata-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--size-4-1);
  font-size: var(--font-ui-small);
  color: var(--text-muted);
  background-color: transparent;
  cursor: pointer;
  border-radius: var(--radius-s);
  padding: var(--size-2-3) var(--size-4-1);
  user-select: none;

  &:hover {
    background-color: var(--background-modifier-hover);
  }
}

.smtcmp-assistant-message-metadata-toggle-icon {
  width: var(--size-4-4);
  height: var(--size-4-4);
}

/* Overrides default .markdown-rendered class styles from Obsidian */
.smtcmp-markdown-rendered {
  &.markdown-rendered {
    --render-scale: 1;
    font-size: calc(var(--render-scale) * 1rem);
    line-height: 1.5;

    &.smtcmp-scale-xs {
      --render-scale: 0.8;
    }

    &.smtcmp-scale-sm {
      --render-scale: 0.85;
    }

    &.smtcmp-scale-base {
      --render-scale: 1;
    }

    /* override default variables */
    --p-spacing: calc(var(--render-scale) * 1rem);
    --heading-spacing: calc(var(--p-spacing) * 2.5);
    --checkbox-size: calc(var(--render-scale) * 1rem);
    --checkbox-radius: calc(var(--render-scale) * var(--radius-s));
    --icon-size: calc(var(--render-scale) * var(--icon-m));

    /* adjust list indent */
    --list-indent: 1.5em;

    blockquote {
      padding-inline-start: calc(var(--render-scale) * 1.5rem);
    }

    pre {
      padding: calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 1rem);
    }

    hr {
      margin: calc(var(--render-scale) * 2rem) 0;
    }

    th,
    td {
      font-size: calc(var(--render-scale) * 1rem);
      padding: calc(var(--render-scale) * 0.25rem)
        calc(var(--render-scale) * 0.5rem);
    }

    .callout {
      padding: calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 0.75rem) calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 1.5rem);
    }

    .callout-icon {
      height: calc(var(--render-scale) * 1rem);
      width: calc(var(--render-scale) * 1rem);
    }
  }

  /* Show frontmatter which is hidden by default */
  .frontmatter {
    display: block !important;
    background-color: transparent;
    border-top: 1px solid var(--background-modifier-border);
    border-bottom: 1px solid var(--background-modifier-border);
  }
}

.smtcmp-mention-popover {
  width: 360px;
  max-width: 360px;
}

.smtcmp-mention-popover-folder-path {
  margin-left: auto;
  padding-left: var(--size-4-2);
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  direction: rtl;
  white-space: nowrap;
  color: var(--text-muted);
}

.smtcmp-settings-description-preserve-whitespace {
  .setting-item-description {
    white-space: pre-wrap;
  }
}

.smtcmp-obsidian-code-block {
  pre {
    margin: 0;
  }
}

.smtcmp-toolcall-container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  border-radius: var(--radius-s);

  .smtcmp-toolcall-border-top {
    border-top: var(--input-border-width) solid
      var(--background-modifier-border);
  }
}

.smtcmp-toolcall {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: var(--size-4-1);
  padding: var(--size-4-1);
  font-size: var(--font-ui-small);
}

.smtcmp-toolcall-header {
  padding: var(--size-2-3) var(--size-4-1);
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
  border-radius: var(--radius-s);
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: var(--background-modifier-hover);
  }

  .smtcmp-toolcall-header-content {
    flex-shrink: 1;
    word-break: break-word;
  }

  .smtcmp-toolcall-header-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .smtcmp-toolcall-header-icon--status {
    margin-left: auto;
  }

  .smtcmp-toolcall-header-tool-name {
    font-family: var(--font-monospace);
    font-weight: 600;
  }
}

.smtcmp-toolcall-content {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: var(--size-4-1);
  max-height: 300px;
  overflow: auto;
  padding: var(--size-4-1);

  .smtcmp-toolcall-content-section {
    display: flex;
    flex-direction: column;
    gap: var(--size-4-1);
  }
}

.smtcmp-toolcall-footer {
  display: flex;
  justify-content: end;
  align-items: center;
  padding: var(--size-4-1);

  .smtcmp-toolcall-footer-actions {
    display: flex;
    align-items: center;
    gap: var(--size-4-1);

    button {
      background-color: var(--background-primary-alt);
      border: 1px solid var(--background-modifier-border);
      box-shadow: none;

      &:hover {
        background-color: var(--background-modifier-hover);
        border: 1px solid var(--background-modifier-border-hover);
        box-shadow: none;
      }
    }
  }
}

.smtcmp-split-button {
  display: flex;
  align-items: center;

  .smtcmp-split-button-primary {
    border-radius: var(--button-radius) 0 0 var(--button-radius) !important;
  }

  .smtcmp-split-button-toggle {
    padding: var(--size-4-1);
    border-radius: 0 var(--button-radius) var(--button-radius) 0 !important;
    border-left: none !important;
  }
}

/* MCP Section Styles */
.smtcmp-mcp-servers-container {
  display: flex;
  flex-direction: column;
}

.smtcmp-mcp-servers-header {
  display: grid;
  grid-template-columns: 1fr 128px 64px 112px; /* Match columns with .smtcmp-mcp-server-row to ensure proper alignment */
  gap: var(--size-4-4);
  align-items: center;
  min-height: var(--size-4-10);
  border-bottom: var(--border-width) solid var(--background-modifier-border);
  font-weight: var(--font-medium);
  color: var(--text-muted);

  & > div {
    padding: var(--size-4-1);
  }
  & > div:nth-child(1) {
    text-align: left;
  }
  & > div:nth-child(2) {
    text-align: left;
  }
  & > div:nth-child(3) {
    text-align: center;
  }
  & > div:nth-child(4) {
    text-align: center;
  }
}

.smtcmp-mcp-servers-empty {
  padding: var(--size-4-4) 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-muted);
}

.smtcmp-mcp-server {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-mcp-server-row {
  display: grid;
  grid-template-columns: 1fr 128px 64px 112px; /* Match columns with .smtcmp-mcp-servers-header to ensure proper alignment */
  gap: var(--size-4-4);
  align-items: center;
  min-height: var(--size-4-10);

  & > div {
    padding: var(--size-4-1);
  }
}

.smtcmp-mcp-server-name {
  overflow: hidden;
  text-overflow: ellipsis;
}

.smtcmp-mcp-server-status {
  display: flex;
  justify-content: flex-start;
}

.smtcmp-mcp-server-toggle {
  display: flex;
  justify-content: center;
}

.smtcmp-mcp-server-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--size-4-1);

  svg {
    height: var(--icon-l);
    width: var(--icon-l);
    stroke-width: var(--icon-stroke);
  }
}

.smtcmp-server-expanded-info {
  margin-bottom: var(--size-4-4);
  padding-left: var(--size-4-4);
  border-left: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-server-expanded-info-header {
  font-weight: var(--font-medium);
  margin-bottom: var(--size-4-3);
}

.smtcmp-server-tools-container {
  display: flex;
  flex-direction: column;
  gap: var(--size-4-4);
}

.smtcmp-server-error-message {
  color: var(--text-error);
  font-weight: var(--font-medium);
  font-family: var(--font-monospace);
  font-size: var(--font-ui-small);
  user-select: text;
}

.smtcmp-mcp-server-status-badge {
  border-radius: 9999px;
  padding: var(--size-2-1) var(--size-4-2);
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
  line-height: 1;
}

.smtcmp-mcp-server-status-badge-label {
  font-size: var(--font-ui-smaller);
}

.smtcmp-mcp-server-status-badge--connected {
  background-color: color-mix(in srgb, var(--text-success) 10%, transparent);
  color: var(--text-success);
}

.smtcmp-mcp-server-status-badge--connecting {
  background-color: color-mix(in srgb, var(--text-muted) 10%, transparent);
  color: var(--text-muted);
}

.smtcmp-mcp-server-status-badge--error {
  background-color: color-mix(in srgb, var(--text-error) 10%, transparent);
  color: var(--text-error);
}

.smtcmp-mcp-server-status-badge--disconnected {
  background-color: color-mix(in srgb, var(--text-muted) 10%, transparent);
  color: var(--text-muted);
}

.smtcmp-mcp-tool {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--size-4-4);
  align-items: center;
}

.smtcmp-mcp-tool-info {
  display: flex;
  flex-direction: column;
}

.smtcmp-mcp-tool-name {
  font-family: var(--font-monospace);
  font-size: var(--font-ui-smaller);
}

.smtcmp-mcp-tool-description {
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
}

.smtcmp-mcp-tool-toggle {
  display: flex;
  align-items: center;
  gap: var(--size-4-2);
}

.smtcmp-mcp-tool-toggle-label {
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
}

/* MCP Server Modal Styles */
.smtcmp-mcp-server-modal-textarea {
  width: 100%;
  font-family: var(--font-monospace);
  resize: none;
  word-break: break-all;
}

.smtcmp-mcp-server-modal-validation {
  font-family: var(--font-monospace);
  font-size: var(--font-ui-small);
  padding: var(--size-4-1) 0;
  white-space: pre-wrap;

  &.smtcmp-mcp-server-modal-validation--error {
    color: var(--text-error);
  }

  &.smtcmp-mcp-server-modal-validation--success {
    color: var(--text-success);
  }
}

.smtcmp-continue-response-button-container {
  display: flex;
  justify-content: center;
  padding: var(--size-4-1) 0;
}

.smtcmp-continue-response-button {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
}
 
.pandoc-plugin-error {
    color: red;
}
 .puml-settings-area {
    margin-left: 5px;
    margin-right: 5px;
    font-size: 14px;
    width: 100%;
}

.plantuml-source-view .cm-activeLine {
    background-color: unset !important;
}

.plantuml-source-view .cm-gutters {
    background-color: unset !important;
}

.plantuml-source-view .cm-cursor {
    border-left: 1.2px solid var(--text-muted);
}

.plantuml-source-view .cm-selectionBackground {
    background-color: var(--text-selection) !important;
}

.puml-loading {
    color: var(--text-accent);
}

.internal-embed.file-embed[src$=".puml"] {
    display: none;
}

.internal-embed.file-embed[src$=".pu"] {
    display: none;
}

.puml-error {
    color: var(--text-error);
}
 .mermaid-toolbar-container, .mermaid-toolbar-container * {
    max-width: 100%;
    max-height: 100%;
}

.mermaid-toolbar-top-row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
}

.mermaid-toolbar-elements-container {
    padding-top: 1rem;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.mermaid-toolbar-element {
    font-size: var(--font-ui-small);
    cursor: pointer;
    padding: 2px 2px 2px 5px;
    border-radius: 3px;
    flex: 1 0 auto;
}

.mermaid-toolbar-element:hover {
    background-color: var(--interactive-hover);
}

.mermaid-tools-element-category-header::before {
    content: "▼  ";
    font-size: 70%;
    padding-bottom: 2px;
}

.mermaid-tools-element-category-header.collapsed::before {
    content: "▶  ";
    font-size: 70%;
    padding-bottom: 2px;
}

.mermaid-tools-element-container {
    padding-top: 6px;
    border-bottom: var(--border-width) solid var(--color-base-35);
}

.mermaid-tools-edit-element-modal > div {
    margin-bottom: 0.5rem;
}

.mermaid-tools-edit-element-modal label {
    margin-right: 1rem;
}

/* Custom Category Management Styles */
.mermaid-tools-category-management {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid var(--color-base-25);
    border-radius: 8px;
    background-color: var(--color-base-00);
}

.mermaid-tools-category-management h3 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: var(--text-accent);
}

.mermaid-tools-category-management button.mod-cta {
    margin-bottom: 1rem;
}

/* Edit Category Modal Styles */
.mermaid-tools-edit-category-modal {
    min-width: 500px;
}

.mermaid-tools-edit-category-modal .setting-item {
    padding: 8px 0;
    border: none;
}

.mermaid-tools-edit-category-modal .setting-item-info {
    flex-grow: 1;
    margin-right: 12px;
}

.mermaid-tools-edit-category-modal .setting-item-name {
    font-weight: 600;
    color: var(--text-normal);
}

.mermaid-tools-edit-category-modal .setting-item-description {
    color: var(--text-muted);
    font-size: var(--font-ui-smaller);
}

.mermaid-tools-edit-category-modal input,
.mermaid-tools-edit-category-modal textarea {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid var(--color-base-30);
    border-radius: 4px;
    background-color: var(--color-base-00);
    color: var(--text-normal);
}

.mermaid-tools-edit-category-modal input:focus,
.mermaid-tools-edit-category-modal textarea:focus {
    border-color: var(--color-accent);
    outline: none;
    box-shadow: 0 0 0 2px var(--color-accent-2);
}

.modal-button-container {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid var(--color-base-25);
}

.modal-button-container button {
    padding: 6px 16px;
    border: 1px solid var(--color-base-30);
    border-radius: 4px;
    background-color: var(--color-base-10);
    color: var(--text-normal);
    cursor: pointer;
    font-size: var(--font-ui-small);
}

.modal-button-container button:hover {
    background-color: var(--color-base-20);
}

.modal-button-container button.mod-cta {
    background-color: var(--color-accent);
    color: var(--text-on-accent);
    border-color: var(--color-accent);
}

.modal-button-container button.mod-cta:hover {
    background-color: var(--color-accent-hover);
}  undefined
</style>
    </head>
    <body>

<p dir="auto">Hi Jordan</p>
<p dir="auto">Thanks for reaching out to Xero support. We tried to reach you over the phone today but you were unavailable.</p>
<p dir="auto">From your description it sounds like you want to disconnect a bank feed for a Suncorp Bank account. We can see that the Trial for the Xero organisation PaceySpace Labs has now been suspended so any bank feeds connected to this organisation will have been cancelled too.</p>
<p dir="auto">If I have misunderstood your query or you need more help please reach out again. If you would prefer a call please reply with the best number to reach you on and a time frame including timezone for when you're available for a call. We’ll arrange for someone in your timezone to give you a call. Please keep any upcoming daylight saving time changes in mind when selecting your call time.</p>
<p dir="auto">When our Specialist makes the call, their regional phone number will appear. Our regional numbers are as follows:</p>
<p dir="auto">AU - (03) 8797 1250</p>
<p dir="auto">NZ - (04) 890 5700</p>
<p dir="auto">UK - 0118 370 1640</p>
<p dir="auto">We're sorry to hear Xero wasn't the right choice for you.</p>
<p dir="auto">Kind regards</p>
<p dir="auto">Reece</p>
<p dir="auto">Loading</p>
<p dir="auto">Case – Xero Central</p>
    </body>
</html>