---
creation_date: 2025-06-27
modification_date: 2025-06-27
type: area
status: active
area: Administration
area_owner: Jordan
responsibility_level: high
review_frequency: monthly
tags:
  - para/areas
  - administration
  - h
last_review_date: 2025-06-27
next_review_date: 2025-07-27
related: Privacy Tools,
---

# Unauthorized Access Security Audit

## Overview
<!-- Brief description of this area of responsibility -->
- how can i view all users who have connected to my system remotely to audit
## Current Focus
<!-- What's the current focus in this area? -->
#### **✅ Check SSH Logins in Auth Logs**

On most systems, SSH login attempts are logged to:
- **Debian/Ubuntu**: /var/log/auth.log
- **CentOS/RHEL**: /var/log/secure
    

	 Use the following comxmands to filter for SSH logins:
```bash
grep 'sshd' /var/log/auth.log | grep 'Accepted'
```
grep 'sshd' /var/log/auth.log | grep 'Accepted'
```
Jun 27 00:12:03 server sshd[1234]: Accepted password for user1 from ************ port 54321 ssh2
```
 aaaaaaAAA
## Key Responsibilities
<!-- List the key responsibilities in this area -->
-

## Regular Tasks
<!-- Recurring tasks in this area -->
- [ ] Daily:
- [ ] Weekly:
- [ ] Monthly:

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "Administration" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Related Resources
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(file.content, "[[Unauthorized Access Security Audit]]") OR area = "Administration"
SORT file.mtime DESC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[Unauthorized Access Security Audit]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
-

## Quick Links
- [[Unauthorized Access Security Audit Project|New Project]]
- [[Unauthorized Access Security Audit Resource|New Resource]]
- [[Unauthorized Access Security Audit Meeting|New Meeting]]
- [[2-Areas|All Areas]]
