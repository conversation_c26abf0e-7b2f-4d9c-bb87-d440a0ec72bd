---
creation_date: 2024-06-12
modification_date: 2024-06-12
type: note
aliases:
  - Dot<PERSON>les Backup Script
  - Configuration Backup
tags:
  - para/resources
  - linux
  - dotfiles
  - configuration
  - endeavouros
  - scripts
area: System
project: Dotfiles Management
resource: Linux
archive: 
status: active
priority: 1
links:
  - "[[Brain/3-Resources/Dotfiles/Dotfiles-Management]]"
related:
  - "[[Computer Education 1]]"
---

# Dotfiles Backup Script

This document provides information about the backup script used to maintain the dotfiles repository.

## Table of Contents
- [[#Script Overview]]
- [[#Usage Instructions]]
- [[#Script Components]]
- [[#Customization]]
- [[#Automation]]

## Script Overview

The backup script (`backup.sh`) is designed to:

1. Copy current configuration files to the dotfiles repository
2. Organize them into the appropriate directories
3. Commit changes to the repository (optional)
4. Push changes to GitHub (optional)

## Usage Instructions

### Basic Usage

```bash
~/dotfiles/backup.sh
```

### Options

```bash
~/dotfiles/backup.sh [options]
```

| Option | Description |
| ------ | ----------- |
| `--dry-run` | Show what would be copied without making changes |
| `--commit` | Automatically commit changes to the repository |
| `--push` | Push changes to the remote repository (implies --commit) |
| `--all` | Backup all configured components |
| `--component NAME` | Backup only the specified component |

### Examples

Backup all components:
```bash
~/dotfiles/backup.sh --all
```

Backup only the fish shell configuration:
```bash
~/dotfiles/backup.sh --component fish
```

Backup, commit, and push changes:
```bash
~/dotfiles/backup.sh --all --push
```

## Script Components

The backup script consists of several components:

### Configuration File

The script uses a configuration file (`~/.dotfiles.conf`) to determine which files to back up:

```ini
[fish]
source=~/.config/fish
target=fish/.config/fish

[i3]
source=~/.config/i3
target=i3/.config/i3

[xfce4]
source=~/.config/xfce4
target=xfce4/.config/xfce4

# Additional components...
```

### Main Script

The main script (`backup.sh`) handles the backup process:

```bash
#!/bin/bash

# Dotfiles backup script
# Usage: ./backup.sh [options]

# Script implementation...
```

### Helper Functions

The script includes several helper functions:

- `backup_component()`: Backs up a specific component
- `parse_config()`: Parses the configuration file
- `commit_changes()`: Commits changes to the repository
- `push_changes()`: Pushes changes to GitHub

## Customization

You can customize the backup script by:

1. Editing the configuration file to add or remove components
2. Modifying the script to change backup behavior
3. Adding hooks for pre/post-backup actions

### Adding a New Component

To add a new component to the backup process:

1. Add an entry to the configuration file:
```ini
[new-component]
source=~/.config/new-component
target=new-component/.config/new-component
```

2. Run the backup script with the new component:
```bash
~/dotfiles/backup.sh --component new-component
```

## Automation

You can automate the backup process using:

### Cron Jobs

Set up a cron job to run the backup script periodically:

```bash
# Run backup every day at 3:00 AM
0 3 * * * ~/dotfiles/backup.sh --all --commit
```

### Git Hooks

Use Git hooks to automatically backup before committing:

```bash
# In ~/dotfiles/.git/hooks/pre-commit
~/dotfiles/backup.sh --all
```

### Systemd Timers

Use systemd timers for more flexible scheduling:

```ini
# ~/.config/systemd/user/dotfiles-backup.service
[Unit]
Description=Backup dotfiles

[Service]
Type=oneshot
ExecStart=%h/dotfiles/backup.sh --all --commit

[Install]
WantedBy=default.target
```

```ini
# ~/.config/systemd/user/dotfiles-backup.timer
[Unit]
Description=Backup dotfiles daily

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
```

Enable with:
```bash
systemctl --user enable --now dotfiles-backup.timer
```

## Tasks
- [ ] Implement dry-run functionality
- [ ] Add error handling for failed backups
- [ ] Create configuration file template
- [ ] Add support for excluding specific files

## Metadata
- **Original Creation**: 2024-06-12
- **Source**: Personal configuration
- **Context**: System management and portability
