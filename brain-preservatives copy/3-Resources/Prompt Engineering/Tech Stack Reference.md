---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
source: Personal Information
tags: [para/resources, tech-stack, development, reference, programming]
related: ["Developer Profile", "Prompt Engineering Fundamentals", "MCP Setup Guide"]
area: Software-Development
keywords: [tech-stack, c#, asp.net, aws, javascript, git, bash, python]
last_used: 2025-04-16
---

# Tech Stack Reference

## Overview
A comprehensive reference of current technology stack and learning priorities for use in LLM prompts and personalized responses.

## Primary Technologies

### .NET & C#

#### Current Proficiency
- Basic to intermediate understanding of C# syntax and concepts
- Familiarity with object-oriented programming principles
- Experience with basic LINQ queries
- Understanding of async/await patterns

#### Key Components
- **.NET 8**: Latest version of the .NET framework
- **C# 12**: Latest version of the C# language
- **Visual Studio / VS Code**: Development environments

#### Learning Resources
- [Microsoft Learn - C# Documentation](https://learn.microsoft.com/en-us/dotnet/csharp/)
- [C# Programming Guide](https://learn.microsoft.com/en-us/dotnet/csharp/programming-guide/)
- [.NET API Browser](https://learn.microsoft.com/en-us/dotnet/api/)

### ASP.NET Core

#### Current Proficiency
- Basic understanding of MVC pattern
- Experience creating simple web applications
- Familiarity with Razor pages
- Basic API development

#### Key Components
- **ASP.NET Core MVC**: Web application framework
- **Razor Pages**: Page-focused development model
- **Web API**: RESTful service development
- **Entity Framework Core**: ORM for database access

#### Learning Resources
- [ASP.NET Core Documentation](https://learn.microsoft.com/en-us/aspnet/core/)
- [ASP.NET Core Fundamentals](https://learn.microsoft.com/en-us/aspnet/core/fundamentals/)
- [Entity Framework Core](https://learn.microsoft.com/en-us/ef/core/)

### AWS

#### Current Proficiency
- Basic understanding of core AWS services
- Experience with simple deployments
- Familiarity with AWS console

#### Key Services
- **EC2**: Virtual servers in the cloud
- **S3**: Object storage
- **RDS**: Managed relational database service
- **Lambda**: Serverless compute
- **API Gateway**: API management service

#### Learning Resources
- [AWS Documentation](https://docs.aws.amazon.com/)
- [AWS Skill Builder](https://skillbuilder.aws/)
- [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/)

## Web Development

### HTML/CSS

#### Current Proficiency
- Solid understanding of HTML5 structure and semantics
- Basic to intermediate CSS skills
- Familiarity with responsive design principles
- Experience with CSS frameworks

#### Key Components
- **HTML5**: Latest version of HTML
- **CSS3**: Latest version of CSS
- **Bootstrap**: CSS framework for responsive design
- **Flexbox/Grid**: Modern CSS layout techniques

#### Learning Resources
- [MDN Web Docs - HTML](https://developer.mozilla.org/en-US/docs/Web/HTML)
- [MDN Web Docs - CSS](https://developer.mozilla.org/en-US/docs/Web/CSS)
- [CSS-Tricks](https://css-tricks.com/)

### JavaScript

#### Current Proficiency
- Basic understanding of JavaScript syntax
- Familiarity with DOM manipulation
- Limited experience with modern JS features
- Currently a focus area for improvement

#### Key Components
- **ES6+**: Modern JavaScript features
- **DOM API**: Document Object Model interaction
- **Fetch API**: Network request handling
- **JSON**: Data interchange format

#### Learning Resources
- [MDN Web Docs - JavaScript](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
- [JavaScript.info](https://javascript.info/)
- [Eloquent JavaScript](https://eloquentjavascript.net/)

## DevOps & Infrastructure

### Git

#### Current Proficiency
- Comfortable with basic Git operations
- Experience with branching and merging
- Familiarity with GitHub workflow
- Understanding of pull requests and code reviews

#### Key Components
- **Git CLI**: Command-line interface for Git
- **GitHub**: Web-based Git repository hosting
- **Branching Strategies**: Feature branches, main/develop workflow
- **Pull Requests**: Code review and collaboration

#### Learning Resources
- [Git Documentation](https://git-scm.com/doc)
- [GitHub Docs](https://docs.github.com/en)
- [Pro Git Book](https://git-scm.com/book/en/v2)

### Bash

#### Current Proficiency
- Basic command-line operations
- Simple script creation
- Currently a focus area for improvement

#### Key Components
- **Bash Scripting**: Automation and task execution
- **File System Operations**: Managing files and directories
- **Process Management**: Running and controlling processes
- **Text Processing**: Working with text files and streams

#### Learning Resources
- [GNU Bash Manual](https://www.gnu.org/software/bash/manual/)
- [Bash Guide for Beginners](https://tldp.org/LDP/Bash-Beginners-Guide/html/)
- [Advanced Bash-Scripting Guide](https://tldp.org/LDP/abs/html/)

### SSH

#### Current Proficiency
- Basic remote server access
- Key-based authentication
- Simple configuration

#### Key Components
- **SSH Client**: Remote server access
- **SSH Keys**: Secure authentication
- **SSH Config**: Client configuration
- **SCP/SFTP**: Secure file transfer

#### Learning Resources
- [OpenSSH Documentation](https://www.openssh.com/manual.html)
- [SSH Academy](https://www.ssh.com/academy/)

## Secondary Skills

### Python

#### Current Proficiency
- Basic syntax understanding
- Simple script creation
- Limited experience with libraries and frameworks

#### Key Components
- **Python 3**: Latest major version
- **Standard Library**: Built-in modules and functions
- **pip**: Package management
- **Virtual Environments**: Isolated development environments

#### Learning Resources
- [Python Documentation](https://docs.python.org/3/)
- [Real Python](https://realpython.com/)
- [Python Crash Course](https://nostarch.com/pythoncrashcourse2e)

### Docker

#### Current Proficiency
- Basic understanding of containerization concepts
- Limited experience with Docker commands
- Familiarity with simple Dockerfiles

#### Key Components
- **Docker Engine**: Core containerization platform
- **Dockerfile**: Container definition
- **Docker Compose**: Multi-container applications
- **Docker Hub**: Container registry

#### Learning Resources
- [Docker Documentation](https://docs.docker.com/)
- [Docker Get Started Guide](https://docs.docker.com/get-started/)

## Learning Roadmap

### Current Focus (Next 3 Months)
1. **JavaScript**: Strengthen core concepts and modern features
2. **AWS**: Deepen understanding of key services
3. **Bash**: Improve scripting capabilities
4. **ASP.NET Core**: Build more complex applications

### Medium-Term Goals (3-6 Months)
1. **React**: Learn fundamentals and build simple applications
2. **CI/CD**: Implement basic pipelines
3. **Serverless**: Develop AWS Lambda functions
4. **Database Design**: Improve data modeling skills

### Long-Term Goals (6-12 Months)
1. **Full-Stack**: Achieve balanced proficiency
2. **Cloud Architecture**: Design scalable solutions
3. **DevOps**: Integrate development and operations
4. **Open Source**: Contribute to projects

## Related
- [[Prompt Engineering TOC]]
- [[Developer Profile]]
- [[Preferred Coding Styles]]
- [[MCP Setup Guide]]
