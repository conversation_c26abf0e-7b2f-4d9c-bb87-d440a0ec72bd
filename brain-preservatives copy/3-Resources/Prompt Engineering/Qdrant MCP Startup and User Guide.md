---
creation_date: 2025-09-06
modification_date: 2025-09-06
type: documentation
aliases: [Qdrant MCP Server Guide, Vector Database MCP, Semantic Search MCP, AI Memory System]
tags: [cruca, mcp, qdrant, vector-database, semantic-search, ai-agents, claude, vscode, embedding, fastmcp, configuration, user-guide]
status: active
version: 1.0
author: <PERSON> Pacey
related: [AGENTS.md, MCP Configuration, AI Tools]
category: AI Tools & Configuration
priority: high
difficulty: intermediate
estimated_setup_time: 15-30 minutes
---

# Qdrant MCP Server — Complete User Guide for AI Agents

Tags: #mcp #qdrant #vector-database #semantic-search #ai-agents #claude #vscode #embedding #configuration #user-guide

---

## Overview

### What is Qdrant MCP Server?

The Qdrant MCP Server is a **Model Context Protocol (MCP) server** that provides AI agents with **semantic memory capabilities** through vector database storage and retrieval. It enables your AI assistants to:

- **Store information** with rich metadata for later retrieval
- **Search semantically** using natural language queries  
- **Remember context** across conversations and sessions
- **Build knowledge bases** that grow over time
- **Enhance agent capabilities** with persistent memory

### Key Capabilities

| Feature | Description | Use Cases |
|---------|-------------|-----------|
| **Semantic Storage** | Store text with vector embeddings | Code snippets, documentation, notes |
| **Intelligent Search** | Find relevant information using natural language | "Find the authentication code", "Show me React patterns" |
| **Metadata Support** | Attach structured data to entries | Tags, categories, timestamps, source info |
| **Multiple Collections** | Organize data into separate namespaces | Projects, topics, clients, knowledge domains |
| **Flexible Deployment** | Local, cloud, or in-memory options | Development, production, testing |

---

## Available Tools

### 🔍 `qdrant-find`
**Search for relevant information using natural language queries**

**Parameters:**
- `query` (string, required) — Natural language search query
- `collection_name` (string, conditional) — Target collection (required if no default set)

**Example Usage:**
```
Find authentication patterns in React
Search for database connection examples
Show me error handling code
```

### 💾 `qdrant-store`  
**Store information with optional metadata for later retrieval**

**Parameters:**
- `information` (string, required) — Text content to store
- `metadata` (JSON, optional) — Structured metadata object
- `collection_name` (string, conditional) — Target collection (required if no default set)

**Example Usage:**
```json
{
  "information": "React authentication hook using JWT tokens",
  "metadata": {
    "type": "code",
    "language": "javascript", 
    "framework": "react",
    "tags": ["auth", "jwt", "hooks"]
  }
}
```

---

## Installation & Setup

### Method 1: Quick Install with uvx (Recommended)

```bash
# Install globally with uvx (easiest)
uvx mcp-server-qdrant

# Or install via pip
pip install mcp-server-qdrant
```

### Method 2: Docker Deployment

```bash
# Build the container
docker build -t mcp-server-qdrant .

# Run with environment variables
docker run -p 8000:8000 \
  -e FASTMCP_HOST="0.0.0.0" \
  -e QDRANT_URL="http://localhost:6333" \
  -e COLLECTION_NAME="my-knowledge" \
  mcp-server-qdrant
```

### Method 3: Development Setup

```bash
# Clone and install for development
git clone https://github.com/qdrant/mcp-server-qdrant.git
cd mcp-server-qdrant
uv sync

# Run in development mode with inspector
QDRANT_URL=":memory:" COLLECTION_NAME="dev" \
uv run fastmcp dev src/mcp_server_qdrant/server.py
```

---

## Configuration

### Core Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `QDRANT_URL` | Qdrant server URL or `:memory:` | None | Yes* |
| `QDRANT_LOCAL_PATH` | Local file storage path | None | Yes* |
| `COLLECTION_NAME` | Default collection name | None | Recommended |
| `QDRANT_API_KEY` | API key for managed Qdrant | None | Cloud only |
| `EMBEDDING_MODEL` | Embedding model name | `sentence-transformers/all-MiniLM-L6-v2` | No |

*Either `QDRANT_URL` or `QDRANT_LOCAL_PATH` must be set, but not both.

### Advanced Configuration

| Variable | Description | Default |
|----------|-------------|---------|
| `QDRANT_SEARCH_LIMIT` | Max search results | 10 |
| `QDRANT_READ_ONLY` | Disable store operations | false |
| `FASTMCP_PORT` | Server port (SSE/HTTP) | 8000 |
| `FASTMCP_HOST` | Bind address | 127.0.0.1 |
| `TOOL_STORE_DESCRIPTION` | Custom store tool description | Default memory prompt |
| `TOOL_FIND_DESCRIPTION` | Custom find tool description | Default search prompt |

### Transport Modes

```bash
# stdio (default) - for local MCP clients
uvx mcp-server-qdrant

# SSE - for remote/web clients  
uvx mcp-server-qdrant --transport sse

# Streamable HTTP - modern alternative to SSE
uvx mcp-server-qdrant --transport streamable-http
```

---

## Client Configuration

### Claude Desktop

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "qdrant": {
      "command": "uvx",
      "args": ["mcp-server-qdrant"],
      "env": {
        "QDRANT_URL": "http://localhost:6333",
        "COLLECTION_NAME": "claude-memory",
        "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2"
      }
    }
  }
}
```

### Claude Dev/Cline (VS Code Extension)

For the file you have open (`cline_mcp_settings.json`):

```json
{
  "mcpServers": {
    "qdrant": {
      "command": "uvx",
      "args": ["mcp-server-qdrant"],
      "env": {
        "QDRANT_URL": "http://localhost:6333",
        "COLLECTION_NAME": "cline-knowledge",
        "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2",
        "TOOL_STORE_DESCRIPTION": "Store code snippets, solutions, and technical knowledge for later retrieval. Include relevant metadata like language, framework, and tags.",
        "TOOL_FIND_DESCRIPTION": "Search for relevant code examples, solutions, or technical knowledge using natural language queries."
      }
    }
  }
}
```

### VS Code MCP Extension

Create `.vscode/mcp.json` in your workspace:

```json
{
  "inputs": [
    {
      "type": "promptString",
      "id": "qdrantUrl",
      "description": "Qdrant URL (or :memory: for testing)"
    },
    {
      "type": "promptString", 
      "id": "collectionName",
      "description": "Collection Name"
    }
  ],
  "servers": {
    "qdrant": {
      "command": "uvx",
      "args": ["mcp-server-qdrant"],
      "env": {
        "QDRANT_URL": "${input:qdrantUrl}",
        "COLLECTION_NAME": "${input:collectionName}"
      }
    }
  }
}
```

### Cursor IDE

Add to Cursor's MCP configuration:

```json
{
  "qdrant": {
    "command": "uvx", 
    "args": ["mcp-server-qdrant"],
    "env": {
      "QDRANT_URL": "http://localhost:6333",
      "COLLECTION_NAME": "cursor-workspace"
    }
  }
}
```

---

## Qdrant Database Setup

### Option 1: In-Memory (Testing)

```bash
# No external dependencies - perfect for testing
QDRANT_URL=":memory:" COLLECTION_NAME="test" uvx mcp-server-qdrant
```

### Option 2: Local Qdrant Server

```bash
# Start Qdrant with Docker
docker run -d -p 6333:6333 -p 6334:6334 qdrant/qdrant:latest

# Verify it's running
curl http://localhost:6333/healthz

# Configure MCP server to use it
QDRANT_URL="http://localhost:6333" COLLECTION_NAME="local" uvx mcp-server-qdrant
```

### Option 3: Qdrant Cloud

```bash
# Get your cluster URL and API key from Qdrant Cloud
QDRANT_URL="https://xyz-example.eu-central.aws.cloud.qdrant.io:6333" \
QDRANT_API_KEY="your-api-key" \
COLLECTION_NAME="production" \
uvx mcp-server-qdrant
```

### Option 4: Local File Storage

```bash
# Store data in local files (no server needed)
QDRANT_LOCAL_PATH="/path/to/qdrant/data" \
COLLECTION_NAME="file-storage" \
uvx mcp-server-qdrant
```

---

## Usage Patterns & Workflows

### 1. Code Knowledge Base

**Configuration:**
```bash
QDRANT_URL="http://localhost:6333" \
COLLECTION_NAME="code-snippets" \
TOOL_STORE_DESCRIPTION="Store reusable code snippets with descriptions and metadata" \
TOOL_FIND_DESCRIPTION="Search for code examples by functionality or technology" \
uvx mcp-server-qdrant
```

**Usage Examples:**
```
Store: "React authentication hook using JWT"
Metadata: {"language": "javascript", "framework": "react", "type": "hook"}

Find: "authentication patterns in React"
Find: "JWT token handling"
```

### 2. Project Documentation

**Configuration:**
```bash
QDRANT_URL="http://localhost:6333" \
COLLECTION_NAME="project-docs" \
TOOL_STORE_DESCRIPTION="Store project documentation, decisions, and notes" \
TOOL_FIND_DESCRIPTION="Search project knowledge and documentation" \
uvx mcp-server-qdrant
```

**Usage Examples:**
```
Store: "Database schema design decisions for user authentication"
Metadata: {"type": "decision", "date": "2025-09-06", "component": "auth"}

Find: "database design decisions"
Find: "authentication architecture"
```

### 3. Learning & Research

**Configuration:**
```bash
QDRANT_URL="http://localhost:6333" \
COLLECTION_NAME="learning" \
TOOL_STORE_DESCRIPTION="Store learning notes, concepts, and insights" \
TOOL_FIND_DESCRIPTION="Search learning materials and concepts" \
uvx mcp-server-qdrant
```

**Usage Examples:**
```
Store: "Vector databases use embeddings to enable semantic search"
Metadata: {"topic": "vector-db", "source": "documentation", "difficulty": "intermediate"}

Find: "vector database concepts"
Find: "semantic search explanation"
```

---

## Advanced Configuration

### Custom Embedding Models

```bash
# Use different embedding models
EMBEDDING_MODEL="sentence-transformers/all-mpnet-base-v2" uvx mcp-server-qdrant
EMBEDDING_MODEL="BAAI/bge-small-en-v1.5" uvx mcp-server-qdrant
EMBEDDING_MODEL="intfloat/e5-small-v2" uvx mcp-server-qdrant
```

### Multiple Collections Setup

```bash
# Different collections for different purposes
COLLECTION_NAME="code" uvx mcp-server-qdrant &
COLLECTION_NAME="docs" uvx mcp-server-qdrant --transport sse &
COLLECTION_NAME="research" uvx mcp-server-qdrant --transport streamable-http &
```

### Production Docker Deployment

```dockerfile
# docker-compose.yml
version: '3.8'
services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
      
  mcp-server:
    image: mcp-server-qdrant
    ports:
      - "8000:8000"
    environment:
      - FASTMCP_HOST=0.0.0.0
      - QDRANT_URL=http://qdrant:6333
      - COLLECTION_NAME=production
    depends_on:
      - qdrant

volumes:
  qdrant_data:
```

---

## Troubleshooting

### Common Issues & Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **Connection Refused** | `curl: (7) Failed to connect to localhost:6333` | Start Qdrant: `docker run -d -p 6333:6333 qdrant/qdrant:latest` |
| **Module Not Found** | `ModuleNotFoundError: No module named 'qdrant_client'` | Install dependencies: `uv sync` or `pip install mcp-server-qdrant` |
| **Both URL and Path Set** | `ValueError: If 'local_path' is set, 'location' and 'api_key' must be None` | Use only `QDRANT_URL` OR `QDRANT_LOCAL_PATH`, not both |
| **Collection Name Required** | Tools prompt for collection name | Set `COLLECTION_NAME` environment variable |
| **SSE Connection Issues** | Empty reply from server | Use proper MCP client, not raw curl requests |

### Health Checks

```bash
# Check Qdrant server
curl -sS http://localhost:6333/healthz

# Check MCP server (stdio mode)
echo '{"method": "tools/list", "params": {}}' | QDRANT_URL=":memory:" COLLECTION_NAME="test" uvx mcp-server-qdrant

# Check MCP server (SSE mode)
curl -sS http://localhost:8000/sse  # Should show SSE pings
```

### Debug Mode

```bash
# Enable debug logging
export PYTHONPATH=src
QDRANT_URL=":memory:" COLLECTION_NAME="debug" \
python -m logging.basicConfig --level=DEBUG \
uv run fastmcp dev src/mcp_server_qdrant/server.py
```

---

## Best Practices

### 1. Collection Organization

```bash
# Organize by purpose/domain
COLLECTION_NAME="work-projects"     # Work-related code and docs
COLLECTION_NAME="personal-learning" # Learning materials
COLLECTION_NAME="research-notes"    # Research and experiments
```

### 2. Metadata Standards

```json
{
  "type": "code|documentation|note|decision",
  "language": "python|javascript|markdown",
  "framework": "react|django|fastapi", 
  "tags": ["auth", "api", "frontend"],
  "date": "2025-09-06",
  "source": "project|documentation|learning",
  "priority": "high|medium|low"
}
```

### 3. Search Query Patterns

```
# Effective search queries
"authentication using JWT tokens"           # Specific functionality
"error handling patterns in Python"        # Language-specific patterns  
"React component lifecycle methods"         # Framework concepts
"database connection pooling"               # Technical concepts
"API rate limiting implementation"          # Implementation details
```

### 4. Tool Descriptions

```bash
# Customize for your use case
TOOL_STORE_DESCRIPTION="Store code snippets, solutions, and technical knowledge. Include language, framework, and functionality in metadata."

TOOL_FIND_DESCRIPTION="Search technical knowledge base using natural language. Describe what you're looking for functionally rather than by exact keywords."
```

---

## Performance & Scaling

### Embedding Model Selection

| Model | Size | Speed | Quality | Use Case |
|-------|------|-------|---------|----------|
| `all-MiniLM-L6-v2` | Small | Fast | Good | General purpose, development |
| `all-mpnet-base-v2` | Medium | Medium | Better | Production, better accuracy |
| `bge-small-en-v1.5` | Small | Fast | Good | English-only, fast retrieval |
| `e5-small-v2` | Small | Fast | Good | Multilingual support |

### Collection Size Guidelines

| Collection Size | Recommended Setup | Performance |
|----------------|-------------------|-------------|
| < 1,000 entries | In-memory or local file | Excellent |
| 1,000 - 10,000 | Local Qdrant server | Very good |
| 10,000 - 100,000 | Dedicated Qdrant server | Good |
| > 100,000 | Qdrant Cloud or cluster | Scalable |

---

## Security Considerations

### 1. API Key Management

```bash
# Use environment files for sensitive data
echo "QDRANT_API_KEY=your-secret-key" > .env
source .env
uvx mcp-server-qdrant
```

### 2. Network Security

```bash
# Bind to localhost only for local use
FASTMCP_HOST="127.0.0.1" uvx mcp-server-qdrant --transport sse

# Use HTTPS for production
QDRANT_URL="https://your-secure-qdrant.com:6333" uvx mcp-server-qdrant
```

### 3. Read-Only Mode

```bash
# Prevent accidental data modification
QDRANT_READ_ONLY=true uvx mcp-server-qdrant
```

---

## Integration Examples

### With GitHub Copilot

```json
// .vscode/settings.json
{
  "github.copilot.advanced": {
    "debug.overrideEngine": "mcp-server-qdrant",
    "debug.testOverrideProxyUrl": "http://localhost:8000"
  }
}
```

### With Obsidian

```bash
# Store Obsidian notes in Qdrant
COLLECTION_NAME="obsidian-vault" \
TOOL_STORE_DESCRIPTION="Store Obsidian notes and knowledge" \
uvx mcp-server-qdrant
```

### With Jupyter Notebooks

```python
# In Jupyter cell
import os
os.environ['QDRANT_URL'] = 'http://localhost:6333'
os.environ['COLLECTION_NAME'] = 'jupyter-experiments'

# Your notebook code here...
```

---

## Quick Reference

### Essential Commands

```bash
# Start with in-memory database (testing)
QDRANT_URL=":memory:" COLLECTION_NAME="test" uvx mcp-server-qdrant

# Start with local Qdrant (development)  
docker run -d -p 6333:6333 qdrant/qdrant:latest
QDRANT_URL="http://localhost:6333" COLLECTION_NAME="dev" uvx mcp-server-qdrant

# Start with SSE transport (remote clients)
QDRANT_URL="http://localhost:6333" COLLECTION_NAME="remote" uvx mcp-server-qdrant --transport sse

# Development mode with inspector
QDRANT_URL=":memory:" COLLECTION_NAME="dev" uv run fastmcp dev src/mcp_server_qdrant/server.py
```

### Environment Template

```bash
# Copy and customize this template
export QDRANT_URL="http://localhost:6333"
export COLLECTION_NAME="my-knowledge"
export EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"
export TOOL_STORE_DESCRIPTION="Store information with rich metadata"
export TOOL_FIND_DESCRIPTION="Search using natural language queries"

uvx mcp-server-qdrant
```

---

## Related Resources

- [[AGENTS.md]] — Agent configuration guidelines
- [[MCP Configuration]] — General MCP setup
- [Qdrant Documentation](https://qdrant.tech/documentation/) — Vector database docs
- [FastMCP Documentation](https://gofastmcp.com/) — MCP framework docs
- [Model Context Protocol](https://modelcontextprotocol.io/) — Official MCP spec

---

## Tags & Categories

### Primary Tags
#mcp #qdrant #vector-database #semantic-search #ai-agents

### Technology Tags  
#claude #vscode #embedding #fastmcp #configuration

### Use Case Tags
#knowledge-management #code-storage #documentation #learning #research

### Difficulty Tags
#intermediate #configuration #setup #troubleshooting

---

## Changelog

| Version | Date | Changes |
|---------|------|---------|
| 1.0 | 2025-09-06 | Initial comprehensive user guide |

---

**Note**: This guide covers the complete setup and usage of Qdrant MCP Server for AI agents. For specific client configurations or advanced use cases, refer to the related documentation links above.

---
