---
creation_date: 2025-08-14
modification_date: 2025-08-14
type: resource
status: active
priority: low
area_category: Software-Development
owner: Jordan
tags:
  - para/resources
  - reference
  - aws
  - cli
  - ghcr
  - github
  - actions
  - ci
  - yaml
  - docker
  - compose
  - ci/cd
source: Tool
difficulty: advanced
resource_type: reference
url: 
last_verified: 2025-08-14
related_projects: 
related_areas:
  - "[[Software-Development]]"
related_people:
  - "[[Jordan]]"
references: 
inspired_by:
---
# AWS CLI and account Details

## Overview
<!-- Brief description of this resource -->
Github Actions Details for AWS ECR CI/CD file (.ci) for automatic build pushing with github commits

```mermaid
graph TD
    A[GitHub Push/Manual Trigger] --> B[Checkout Code]
    B --> C[Setup Docker Buildx]
    C --> D[Configure AWS Credentials]
    D --> E[Login to ECR]
    E --> F[Compute SHA Tag]
    F --> G[Build API Image]
    F --> H[Build Frontend Image]
    F --> I[Build Uploader Image]
    G --> J[Push to ECR]
    H --> J
    I --> J
    J --> K[Output Image Tags]
    
    style A fill:#e1f5fe
    style J fill:#c8e6c9
    style K fill:#fff3e0
```

```ci
# Tags applied to each image:
- latest                    # Always points to most recent build
- sha-{git-commit-sha}     # Specific commit reference
- {branch-name}            # Branch-based tag (e.g., 'main')
```
## Key Points
<!-- Main takeaways or important information -->
- 

## Details
<!-- Detailed information -->
### #AWS Account #ID:
```json
aws sts get-caller-identity
{
    "UserId": "************",
    "Account": "************",
    "Arn": "arn:aws:iam::************:root"
}
```
### #AWS #AccessKey #ID:
```
AKIAQLVQQ7GZRJOR7PGK
```

### #github repo #yendorcats #github/actions #actions 
```json
gh repo view yendorcats --json nameWithOwner
{
  "nameWithOwner": "fjord-an/yendorcats"
}
```

## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## 🎯 Projects Using This Resource
```dataview

TABLE WITHOUT ID
  file.link as "🎯 Used By",
  status as "Status",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE contains(related_resources, "AWS CLI and account Details")
  AND status != "completed"
SORT choice(status = "active", 1, 2) ASC, priority ASC
LIMIT 4

```



## 📋 Related Areas
```dataview

TABLE WITHOUT ID
  file.link as "📋 Area",
  area_category as "Category"
FROM "2-Areas"
WHERE contains(related_resources, "AWS CLI and account Details")
   OR area_category = "Software-Development"
SORT choice(contains(related_resources, "AWS CLI and account Details"), 1, 2) ASC
LIMIT 3

```



## 📚 Related Learning Resources
```dataview

LIST
FROM "3-Resources"
WHERE (area_category = "Software-Development"
   OR contains(tags, "reference"))
  AND file.name != "AWS CLI and account Details"
  AND difficulty <= "advanced"
SORT file.mtime DESC
LIMIT 3

```



## Notes
<!-- Any additional notes -->

## Quick Links
- [[AWS CLI and account Details Project|New Project]]
- [[AWS CLI and account Details Reference|Quick Reference]]
- [[3-Resources|All Resources]]