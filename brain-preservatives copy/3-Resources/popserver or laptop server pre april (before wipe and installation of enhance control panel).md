$ sudo ufw status
Status: active

To                         Action      From
--                         ------      ----
80/tcp                     ALLOW       Anywhere                  
443/tcp                    ALLOW       Anywhere                  
81/tcp                     ALLOW       ***********/16            
9993/udp                   ALLOW       Anywhere                  
6881/tcp                   ALLOW       Anywhere                  
6881/udp                   ALLOW       Anywhere                  
Anywhere on docker0        ALLOW       Anywhere                  
Anywhere on br-983c6f9ef3d5 ALLOW       Anywhere                  
Anywhere on br-            ALLOW       Anywhere                  
Anywhere on ztmjfcpubr     ALLOW       Anywhere                  
Anywhere                   ALLOW       ***********/16            
904                        ALLOW       172.16.0.0/16             
9000                       ALLOW       172.16.0.0/16             
41066                      ALLOW       172.16.0.0/16             
300                        ALLOW       172.16.0.0/16             
Anywhere                   ALLOW       172.16.0.0/16             
41066                      ALLOW       172.16.0.0/16             
Anywhere                   ALLOW       172.18.0.0/16             
8080                       ALLOW       172.16.0.0/16             
Anywhere                   ALLOW       172.18.0.3                
41066                      ALLOW       ***********/16            
9993/tcp on ztuzerbv62     ALLOW       Anywhere                  
22/tcp (v6)                ALLOW       Anywhere (v6)             
80/tcp (v6)                ALLOW       Anywhere (v6)             
443/tcp (v6)               ALLOW       Anywhere (v6)             
9993/udp (v6)              ALLOW       Anywhere (v6)             
6881/tcp (v6)              ALLOW       Anywhere (v6)             
6881/udp (v6)              ALLOW       Anywhere (v6)             
Anywhere (v6) on docker0   ALLOW       Anywhere (v6)             
Anywhere (v6) on br-983c6f9ef3d5 ALLOW       Anywhere (v6)             
Anywhere (v6) on br-       ALLOW       Anywhere (v6)             
Anywhere (v6) on ztmjfcpubr ALLOW       Anywhere (v6)             
9993/tcp (v6) on ztuzerbv62 ALLOW       Anywhere (v6)   

---
```bash
# /etc/ufw/user.rules.20250415_020018
*filter
:ufw-user-input - [0:0]
:ufw-user-output - [0:0]
:ufw-user-forward - [0:0]
:ufw-before-logging-input - [0:0]
:ufw-before-logging-output - [0:0]
:ufw-before-logging-forward - [0:0]
:ufw-user-logging-input - [0:0]
:ufw-user-logging-output - [0:0]
:ufw-user-logging-forward - [0:0]
:ufw-after-logging-input - [0:0]
:ufw-after-logging-output - [0:0]
:ufw-after-logging-forward - [0:0]
:ufw-logging-deny - [0:0]
:ufw-logging-allow - [0:0]
:ufw-user-limit - [0:0]
:ufw-user-limit-accept - [0:0]
### RULES ###

### tuple ### allow tcp 22 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p tcp --dport 22 -s ***********/16 -j ACCEPT

### tuple ### allow any any 0.0.0.0/0 any 0.0.0.0/0 in_ztmjfcpubr
-A ufw-user-input -i ztmjfcpubr -j ACCEPT

### tuple ### allow tcp 80,443 0.0.0.0/0 any ***********/24 in
-A ufw-user-input -p tcp -m multiport --dports 80,443 -s ***********/24 -j ACCEPT

### tuple ### allow tcp 81 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p tcp --dport 81 -s ***********/16 -j ACCEPT

### tuple ### allow tcp 8096 0.0.0.0/0 any ***********/24 in
-A ufw-user-input -p tcp --dport 8096 -s ***********/24 -j ACCEPT

### tuple ### allow tcp 8096 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p tcp --dport 8096 -s ***********/16 -j ACCEPT

### tuple ### allow udp 1900,7359 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p udp -m multiport --dports 1900,7359 -s ***********/16 -j ACCEPT

### tuple ### allow tcp 47989 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p tcp --dport 47989 -s ***********/16 -j ACCEPT

### tuple ### allow udp 47989 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p udp --dport 47989 -s ***********/16 -j ACCEPT

### tuple ### allow udp 48000:48010 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p udp -m multiport --dports 48000:48010 -s ***********/16 -j ACCEPT

### tuple ### allow tcp 9696 0.0.0.0/0 any ***********/24 in
-A ufw-user-input -p tcp --dport 9696 -s ***********/24 -j ACCEPT

### tuple ### allow tcp 9696 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p tcp --dport 9696 -s ***********/16 -j ACCEPT

### tuple ### allow tcp 8055 0.0.0.0/0 any ***********/24 in
-A ufw-user-input -p tcp --dport 8055 -s ***********/24 -j ACCEPT

### tuple ### allow tcp 8055 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p tcp --dport 8055 -s ***********/16 -j ACCEPT

### tuple ### allow any any 0.0.0.0/0 any 0.0.0.0/0 in_br-fc93a4406e3f
-A ufw-user-input -i br-fc93a4406e3f -j ACCEPT

### tuple ### allow any 41066 0.0.0.0/0 any ************50 in
-A ufw-user-input -p tcp --dport 41066 -s ************50 -j ACCEPT
-A ufw-user-input -p udp --dport 41066 -s ************50 -j ACCEPT

### tuple ### allow tcp 41066 0.0.0.0/0 any ************50 in
-A ufw-user-input -p tcp --dport 41066 -s ************50 -j ACCEPT

### tuple ### allow tcp 41066 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p tcp --dport 41066 -s ***********/16 -j ACCEPT

### tuple ### allow udp 7359 0.0.0.0/0 any ***********/16 in
-A ufw-user-input -p udp --dport 7359 -s ***********/16 -j ACCEPT

### tuple ### allow any 8096 0.0.0.0/0 any 172.18.0.0/16 in
-A ufw-user-input -p tcp --dport 8096 -s 172.18.0.0/16 -j ACCEPT
-A ufw-user-input -p udp --dport 8096 -s 172.18.0.0/16 -j ACCEPT

### tuple ### allow any 8920 0.0.0.0/0 any 172.18.0.0/16 in
-A ufw-user-input -p tcp --dport 8920 -s 172.18.0.0/16 -j ACCEPT
-A ufw-user-input -p udp --dport 8920 -s 172.18.0.0/16 -j ACCEPT

### tuple ### allow any 8055 0.0.0.0/0 any 172.18.0.0/16 in
-A ufw-user-input -p tcp --dport 8055 -s 172.18.0.0/16 -j ACCEPT
-A ufw-user-input -p udp --dport 8055 -s 172.18.0.0/16 -j ACCEPT

### tuple ### allow any 9696 0.0.0.0/0 any 172.18.0.0/16 in
-A ufw-user-input -p tcp --dport 9696 -s 172.18.0.0/16 -j ACCEPT
-A ufw-user-input -p udp --dport 9696 -s 172.18.0.0/16 -j ACCEPT

### tuple ### allow any 7878 0.0.0.0/0 any 172.18.0.0/16 in
-A ufw-user-input -p tcp --dport 7878 -s 172.18.0.0/16 -j ACCEPT
-A ufw-user-input -p udp --dport 7878 -s 172.18.0.0/16 -j ACCEPT

### tuple ### allow any 8989 0.0.0.0/0 any 172.18.0.0/16 in
-A ufw-user-input -p tcp --dport 8989 -s 172.18.0.0/16 -j ACCEPT
-A ufw-user-input -p udp --dport 8989 -s 172.18.0.0/16 -j ACCEPT

### END RULES ###

### LOGGING ###
-A ufw-after-logging-input -j LOG --log-prefix "[UFW BLOCK] " -m limit --limit 3/min --limit-burst 10
-I ufw-logging-deny -m conntrack --ctstate INVALID -j RETURN -m limit --limit 3/min --limit-burst 10
-A ufw-logging-deny -j LOG --log-prefix "[UFW BLOCK] " -m limit --limit 3/min --limit-burst 10
-A ufw-logging-allow -j LOG --log-prefix "[UFW ALLOW] " -m limit --limit 3/min --limit-burst 10
### END LOGGING ###

### RATE LIMITING ###
-A ufw-user-limit -m limit --limit 3/minute -j LOG --log-prefix "[UFW LIMIT BLOCK] "
-A ufw-user-limit -j REJECT
-A ufw-user-limit-accept -j ACCEPT
### END RATE LIMITING ###
COMMIT
```