---

# How Has<PERSON><PERSON>orp Vault Works - Complete Guide

## Overview

HashiCorp Vault is a secrets management system that provides secure storage, dynamic secrets, encryption as a service, and more. This guide explains how it works internally and shows practical manual intervention examples.

## Tags
#vault #architecture #manual-intervention #troubleshooting #operations #secrets #security

---

## Core Architecture

### **1. Vault Server Components**

```
┌─────────────────────────────────────────────────────────────┐
│                    Vault Server                            │
├─────────────────────────────────────────────────────────────┤
│  HTTP API Layer                                            │
│  ├── Authentication (Token, AppRole, LDAP, etc.)          │
│  ├── Authorization (Policies)                             │
│  └── Audit Logging                                        │
├─────────────────────────────────────────────────────────────┤
│  Core System                                              │
│  ├── Barrier (Encryption/Decryption)                     │
│  ├── Storage Backend (File, Consul, etc.)                │
│  └── Seal/Unseal Mechanism                               │
├─────────────────────────────────────────────────────────────┤
│  Secrets Engines                                          │
│  ├── KV (Key-Value) - Your B2 credentials                │
│  ├── Database - Dynamic DB credentials                    │
│  ├── PKI - Certificates                                   │
│  └── AWS, Azure, GCP - Cloud credentials                 │
└─────────────────────────────────────────────────────────────┘
```

### **2. Encryption Flow**

```
Your Secret → Encryption Key → Encrypted Data → Storage Backend
     ↑              ↑              ↑               ↑
  "my-b2-key"   AES-256-<PERSON><PERSON>    Binary blob    ~/.vault-data
```

---

## Vault States and Lifecycle

### **1. Vault States**

```bash
# Check current state
vault status

# Possible states:
# - Uninitialized: Fresh installation
# - Initialized but Sealed: Has master key but encrypted
# - Unsealed: Ready for operations
# - Standby: In HA cluster, not active
```

### **2. Initialization Process**

```bash
# Initialize Vault (only done once)
vault operator init -key-shares=5 -key-threshold=3

# This creates:
# - Master key (split into 5 shares)
# - Root token (administrative access)
# - Encryption key (derived from master key)
```

### **3. Seal/Unseal Process**

```
Sealed State:
┌─────────────────┐    ┌─────────────────┐
│   Master Key    │───▶│ Encryption Key  │ (Encrypted)
│   (Split into   │    │                 │
│    5 shares)    │    └─────────────────┘
└─────────────────┘

Unsealed State:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   3 Key Shares  │───▶│ Encryption Key  │───▶│   Your Secrets  │
│   (Provided)    │    │   (Available)   │    │  (Accessible)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## Manual Intervention Examples

### **Scenario 1: Vault Server Crashed**

**Problem**: Your application can't connect to Vault

```bash
# Check if Vault is running
vault status
# Error: Get "http://127.0.0.1:8200/v1/sys/seal-status": dial tcp 127.0.0.1:8200: connect: connection refused

# Check process
ps aux | grep vault
# No vault process found

# Check logs
cat ~/.vault-server.log
# [ERROR] core: failed to acquire lock: error="..."
```

**Manual Intervention**:

```bash
# 1. Start Vault server
vault server -config=~/.vault-config.hcl &

# 2. Wait for startup
sleep 3

# 3. Check status
vault status
# Key                Value
# ---                -----
# Seal Type          shamir
# Initialized        true
# Sealed             true  ← Needs unsealing
# Total Shares       5
# Threshold          3

# 4. Unseal Vault (need 3 keys)
vault operator unseal <key1>
vault operator unseal <key2>
vault operator unseal <key3>

# 5. Verify unsealed
vault status
# Sealed             false ← Now ready
```

### **Scenario 2: Vault is Sealed After Restart**

**Problem**: Vault restarted but is sealed

```bash
vault status
# Sealed             true
# Total Shares       5
# Threshold          3
# Unseal Progress    0/3  ← Need to provide 3 keys
```

**Manual Intervention**:

```bash
# Method 1: Manual unsealing
vault operator unseal
# Unseal Key (will be hidden): [paste key 1]
# Sealed: true
# Unseal Progress: 1/3

vault operator unseal
# Unseal Key (will be hidden): [paste key 2]
# Sealed: true
# Unseal Progress: 2/3

vault operator unseal
# Unseal Key (will be hidden): [paste key 3]
# Sealed: false ← Success!

# Method 2: Scripted unsealing
head -3 ~/.vault-unseal-keys.txt | while read key; do
    vault operator unseal "$key"
done
```

### **Scenario 3: Token Expired**

**Problem**: Application can't authenticate

```bash
# Test current token
vault token lookup
# Error: permission denied

# Check token in your app config
grep "Token" backend/YendorCats.API/appsettings.Development.json
# "Token": "hvs.expired-token-here"
```

**Manual Intervention**:

```bash
# 1. Authenticate with root token
vault auth $(cat ~/.vault-root-token.txt)

# 2. Create new application token
NEW_TOKEN=$(vault token create -policy=default -ttl=8760h -format=json | jq -r '.auth.client_token')

# 3. Update application configuration
sed -i.bak "s/hvs\.[^\"]*/$NEW_TOKEN/g" backend/YendorCats.API/appsettings.Development.json

# 4. Test new token
VAULT_TOKEN=$NEW_TOKEN vault token lookup
# Success!

# 5. Restart your application
cd backend/YendorCats.API
dotnet run
```

### **Scenario 4: Secrets Accidentally Deleted**

**Problem**: Someone deleted your B2 credentials

```bash
# Try to read secrets
vault kv get secret/yendorcats/app-secrets
# Error: * No value found at secret/data/yendorcats/app-secrets

# Check if path exists
vault kv list secret/
# No output (empty)
```

**Manual Intervention**:

```bash
# 1. Check if secrets engine is enabled
vault secrets list
# Path          Type         Accessor              Description
# ----          ----         --------              -----------
# cubbyhole/    cubbyhole    cubbyhole_xxx         per-token private secret storage
# identity/     identity     identity_xxx          identity store
# sys/          system       system_xxx            system endpoints used for control

# 2. Re-enable KV secrets engine
vault secrets enable -path=secret kv-v2

# 3. Restore from backup (if available)
vault kv put secret/yendorcats/app-secrets @backup-secrets.json

# 4. Or recreate manually
vault kv put secret/yendorcats/app-secrets \
    DbConnectionString="Server=localhost;Database=YendorCats;User=root;Password=password;" \
    JwtSecret="$(openssl rand -base64 64)" \
    S3AccessKey="your-b2-key-id" \
    S3SecretKey="your-b2-secret-key"

# 5. Verify restoration
vault kv get secret/yendorcats/app-secrets
```

### **Scenario 5: Vault Storage Corruption**

**Problem**: Vault data is corrupted

```bash
vault status
# Error: failed to decode key: invalid character 'x' looking for beginning of value

# Check storage directory
ls -la ~/.vault-data/
# -rw-------  1 <USER>  <GROUP>   corrupted-file
```

**Manual Intervention**:

```bash
# 1. Stop Vault
pkill vault

# 2. Backup corrupted data
cp -r ~/.vault-data ~/.vault-data-corrupted-$(date +%Y%m%d)

# 3. Restore from backup
rm -rf ~/.vault-data
cp -r ~/.vault-data-backup-20241201 ~/.vault-data

# 4. Start Vault
vault server -config=~/.vault-config.hcl &

# 5. Unseal and verify
head -3 ~/.vault-unseal-keys.txt | while read key; do
    vault operator unseal "$key"
done

vault kv get secret/yendorcats/app-secrets
```

### **Scenario 6: Performance Issues**

**Problem**: Vault is slow to respond

```bash
# Test response time
time vault kv get secret/yendorcats/app-secrets
# real    0m5.234s  ← Too slow!

# Check Vault metrics
vault read sys/metrics
# Shows high latency
```

**Manual Intervention**:

```bash
# 1. Check system resources
top
# Look for high CPU/memory usage

# 2. Check Vault logs
tail -f ~/.vault-server.log
# Look for errors or warnings

# 3. Restart Vault with debug logging
pkill vault
vault server -config=~/.vault-config.hcl -log-level=debug &

# 4. Monitor performance
while true; do
    time vault kv get secret/yendorcats/app-secrets >/dev/null 2>&1
    sleep 1
done

# 5. If needed, recreate Vault data
# (This is a last resort - backup first!)
```

---

## Advanced Manual Operations

### **1. Secret Versioning**

```bash
# View secret history
vault kv metadata secret/yendorcats/app-secrets

# Get specific version
vault kv get -version=2 secret/yendorcats/app-secrets

# Restore previous version
vault kv rollback -version=1 secret/yendorcats/app-secrets

# Delete specific version
vault kv delete -versions=3 secret/yendorcats/app-secrets

# Permanently destroy version
vault kv destroy -versions=3 secret/yendorcats/app-secrets
```

### **2. Policy Management**

```bash
# Create custom policy for your app
vault policy write yendorcats-policy - << EOF
# Allow reading app secrets
path "secret/data/yendorcats/*" {
  capabilities = ["read"]
}

# Allow token self-renewal
path "auth/token/renew-self" {
  capabilities = ["update"]
}
EOF

# Create token with specific policy
vault token create -policy=yendorcats-policy -ttl=24h

# Test policy
VAULT_TOKEN=<new-token> vault kv get secret/yendorcats/app-secrets
```

### **3. Audit Logging**

```bash
# Enable audit logging
vault audit enable file file_path=~/.vault-audit.log

# View audit logs
tail -f ~/.vault-audit.log | jq .

# Disable audit logging
vault audit disable file
```

### **4. Backup and Restore**

```bash
# Create snapshot (Raft storage only)
vault operator raft snapshot save backup.snap

# Restore from snapshot
vault operator raft snapshot restore backup.snap

# Manual backup (file storage)
tar -czf vault-backup.tar.gz ~/.vault-data

# Manual restore
tar -xzf vault-backup.tar.gz -C ~/
```

---

## Troubleshooting Commands

### **Diagnostic Commands**

```bash
# Comprehensive status
vault status -detailed

# List all mounts
vault secrets list -detailed

# List all auth methods
vault auth list

# Check token capabilities
vault token capabilities secret/yendorcats/app-secrets

# View server configuration
vault read sys/config/state/sanitized

# Check seal status
vault read sys/seal-status

# View health status
vault read sys/health
```

### **Emergency Procedures**

```bash
# Emergency seal (locks Vault immediately)
vault operator seal

# Generate new root token (if root token lost)
vault operator generate-root -init
vault operator generate-root -nonce=<nonce> <unseal-key>

# Rekey Vault (change unseal keys)
vault operator rekey -init
vault operator rekey -nonce=<nonce> <old-unseal-key>

# Step down from active (in HA setup)
vault operator step-down
```

---

## Monitoring and Alerting

### **Health Checks**

```bash
# Simple health check
vault status >/dev/null 2>&1 && echo "Healthy" || echo "Unhealthy"

# Detailed health check
vault read sys/health | jq .

# Check specific secret accessibility
vault kv get secret/yendorcats/app-secrets >/dev/null 2>&1 && echo "Secrets OK" || echo "Secrets FAIL"
```

### **Performance Monitoring**

```bash
# Monitor response times
while true; do
    start=$(date +%s%N)
    vault kv get secret/yendorcats/app-secrets >/dev/null 2>&1
    end=$(date +%s%N)
    echo "Response time: $(( (end - start) / 1000000 ))ms"
    sleep 5
done

# Monitor token usage
vault read auth/token/accessors | jq '.data.keys | length'
```

---

## Best Practices for Manual Intervention

### **Safety First**
1. **Always backup** before making changes
2. **Test in development** before production
3. **Document all changes** with timestamps
4. **Have rollback plan** ready
5. **Monitor after changes** for issues

### **Common Patterns**
1. **Check status first**: `vault status`
2. **Review logs**: `cat ~/.vault-server.log`
3. **Test connectivity**: `vault read sys/health`
4. **Verify secrets**: `vault kv get secret/yendorcats/app-secrets`
5. **Update application**: Restart after Vault changes

### **Emergency Contacts**
- Keep unseal keys in separate secure locations
- Document who has access to what
- Have 24/7 contact information
- Maintain emergency procedures documentation

---
