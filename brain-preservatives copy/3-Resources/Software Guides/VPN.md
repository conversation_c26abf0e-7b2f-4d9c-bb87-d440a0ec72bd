---
creation_date: 2025-04-05
modification_date: 2025-04-05
type: note
aliases: []
tags: []
area: 
project: 
resource: 
archive: 
status: active
priority: 
links: []
related: []
---
#warning #researchq
Mullvad:
Beware - privacy danger ahead!
netfilter allows for a very expressive language to be used for defining firewall rules, which is great. However, the language has no guard-rails, and having syntactically correct rules that netfilter will load without emitting any error messages doesn't ensure that the rules will achieve your expected behavior, and they can in fact be detrimental to your privacy. As such, exercise extreme caution and preferably test your
rules in a safe environment if you must ensure your privacy at all times.

Example
Applying these rules will also allow the excluded IP to be reached when our app is in its blocked state. Also, excluding some IP addresses and not others can lead to de-anonymization attacks. Consider the following scenario: you exclude IP addresses that example.com points to from the tunnel and then attempt to visit a website on https://example.com in a web browser, and as the page is being loaded, the HTML document requests various scripts and images from https://assets.example.com. Since assets.example.com and example.com can resolve to 2 different addresses, the traffic to assets.example.com can still be routed through our tunnel. The end result is that whoever controls both assets.example.com and example.com can observe that seemingly the same user is sending requests from 2 different IP addresses simultaneously, so the tunnel exit IP address can be linked to user's real IP address.
# Untitled

## Content


## References


## Tasks
- [ ] 

## Metadata
- **Original Creation**: 2025-04-05
- **Source**: 
- **Context**: