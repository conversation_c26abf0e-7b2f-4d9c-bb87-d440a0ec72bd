---
creation_date: 2025-04-07
modification_date: 2025-04-07
type: note
aliases: 
tags:
  - files
  - guides
  - linux
  - computer
area: 
project: 
resource: 
archive: 
status: active
priority: 
links: 
related:
---

# new directories and setting file permissions 

[[Media Streaming Server]] media streaming server for example:

```bash
    sudo chmod -R 775 /srv/media
    sudo chmod g+s /srv/media/{movies,tv,downloads} # Apply setgid bit
    # Optional: Ensure existing files are 664 if needed
    # sudo find /srv/media -type f -exec chmod 664 {} \;
```

```bash
sudo chmod -R 775 /srv/media && sudo chmod g+s /srv/media/{movies,tv,downloads}
```

# Adding a user to a group to share folder permissions

adding a user to a group will help multiple users access the same files, whilst also segregating permissions amongst system files:

```bash
sudo chmod -R 775 /srv/media && sudo chmod g+s /srv/media/{movies,tv,downloads}
```

**Note** you can create and iterate over multiple files/directories or variables using {} curly braces separated by commas. see [[#Adding a user to a group to share folder permissions]] in the first section
## Content


## References


## Tasks
- [ ] 

## Metadata
- **Original Creation**: 2025-04-07
- **Source**: 
- **Context**: