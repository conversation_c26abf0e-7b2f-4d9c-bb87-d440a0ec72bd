---
creation_date: 2025-07-14
modification_date: 2025-07-14
type: index
aliases: [Resources Index, Resource MOC]
tags: [para/resources, index, moc, resources]
resource: Index
---

# Resource Index

> A comprehensive index of all resources in the vault, organized by category for easier reference.

## Resource Categories

### Documentation & Guides
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "guide") OR contains(tags, "documentation")
SORT file.name ASC
```

### Technical Resources
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "technical") OR contains(tags, "code") OR contains(tags, "software")
SORT file.name ASC
```

### Cheatsheets & Quick References
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources/Cheatsheets"
SORT file.name ASC
```

### Network & System Administration
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "network") OR contains(tags, "system") OR contains(tags, "administration")
SORT file.name ASC
```

### AI & Prompt Engineering
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources/Prompt Engineering"
SORT file.name ASC
```

### Software Development
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "development") OR contains(tags, "programming") OR contains(tags, "coding")
SORT file.name ASC
```

### Tools & Configuration
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "tools") OR contains(tags, "configuration") OR contains(tags, "setup")
SORT file.name ASC
```

### YendorCats Project Resources
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(file.name, "YendorCats") OR contains(tags, "yendorcats")
SORT file.name ASC
```

### Recent Additions
```dataview
TABLE 
  type as "Type",
  file.ctime as "Created"
FROM "3-Resources"
SORT file.ctime DESC
LIMIT 10
```

### All Resources
```dataview
TABLE 
  type as "Type",
  file.mtime as "Last Modified",
  tags as "Tags"
FROM "3-Resources"
WHERE file.name != "Resource Index" 
AND file.name != "Resources TOC"
SORT file.name ASC
```

## Resource Navigation

- [[3-Resources/Guides TOC|Guides & Documentation]]
- [[3-Resources/Software Development Resources|Software Development]]
- [[3-Resources/People MOC|People & Contacts]]
- [[3-Resources/Para Notes Guide|PARA Organization]]
- [[3-Resources/Google Keep Notes|Google Keep Imports]]

## Related
- [[3-Resources]]
- [[1-Projects]]
- [[2-Areas]]
- [[4-Archive]]