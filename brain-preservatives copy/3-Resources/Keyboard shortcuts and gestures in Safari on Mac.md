---
title: "Keyboard shortcuts and gestures in Safari on Mac"
source: "https://support.apple.com/en-ie/guide/safari/cpsh003/mac"
author:
  - "[[Apple Support]]"
published:
created: 2025-08-30
description: "In Safari on your Mac, quickly accomplish tasks using keyboard and other shortcuts."
tags:
  - "clippings"
---
Modifying this control will update this page automatically

[Table of Contents](https://support.apple.com/en-ie/guide/safari/toc)

In the Safari app on your Mac, you can quickly accomplish many tasks using keyboard shortcuts and gestures. See the shortcuts and gestures below, as well as keyboard shortcuts in Safari menus in the [menu bar](https://support.apple.com/en-ie/guide/safari/aside/glos33eb8abd/18.0/mac/15.0). In app menus, keyboard shortcuts are [represented by symbols](https://support.apple.com/en-ie/guide/mac-help/cpmh0011).

*Note:* Keyboard shortcuts in apps may vary depending on the language and keyboard layout you’re using on your Mac. If the shortcuts below don’t work as you expect, look in the app menus in the menu bar to see the correct shortcuts. You can also [use the Keyboard Viewer](https://support.apple.com/en-ie/guide/mac-help/mchlp1015) to see your current keyboard layout, known as an *input source*.

[Open Safari for me](https://openApp?bundleId=com.apple.Safari)

## Scroll

| Action | Shortcut or gesture |
| --- | --- |
| Scroll up, down, left or right | Press the arrow keys. |
| Scroll in larger increments | Press the Option key while you press an arrow key. |
| Scroll down a screen | Page Down  Space bar |
| Scroll up a screen | Page Up  Shift–Space bar |
| Scroll to the top-left or bottom-left corner of the web page | Command–Up Arrow  Command–Down Arrow |

## Current web page

| Action | Shortcut or gesture |
| --- | --- |
| Search the current web page | Command-F |
| Highlight the next field or pop-up menu on a web page | Tab  Tab also highlights buttons and other controls if Keyboard Navigation is turned on in Keyboard settings.  [Open Keyboard settings for me](https://openPrefPane?bundleId=com.apple.preference.keyboard) |
| Highlight the next field, pop-up menu or clickable item (such as a link) on a web page | Option-Tab  Option-Tab also highlights buttons and other controls if Keyboard Navigation is turned on in Keyboard settings.  [Open Keyboard settings for me](https://openPrefPane?bundleId=com.apple.preference.keyboard)  To swap the behaviour of Tab and Option-Tab, turn on “Press Tab to highlight each item on a web page” in the Advanced pane of Safari settings. |
| While typing in the [Smart Search field](https://support.apple.com/en-ie/guide/safari/aside/glos5a7c4a65/18.0/mac/15.0), restore the current web page address | Esc |
| Select the Smart Search field | Command-L |
| Print the current web page | Command-P |
| Copy the selected item | Command-C |
| Paste the most recently copied item | Command-V |

## Tabs

| Action | Shortcut or gesture |
| --- | --- |
| Show tab overview | Shift-Command-\\ |
| Open a web page in a new tab | Command-click a link.  Command-click a bookmark.  Command-Return after typing in the [Smart Search field](https://support.apple.com/en-ie/guide/safari/aside/glos5a7c4a65/18.0/mac/15.0). |
| Open a web page in a new tab, and make that tab the active tab | Shift-Command-click a link.  Shift-Command-click a bookmark.  Shift-Command-Return after typing in the Smart Search field. |
| Go to the next tab | Control-Tab or Shift-Command-\] |
| Go to the previous tab | Control-Shift-Tab or Shift-Command -\[ |
| Select one of your first nine tabs | Command-1 to Command-9 |
| Close the active tab | Command-W |
| Close all tabs except for one | Option-click on the tab you want to leave open. |
| Reopen the last tab you closed | Shift-Command-T |

## History

| Action | Shortcut or gesture |
| --- | --- |
| Go back to the previous web page | Command-\[ |
| Go forward to the next web page | Command-\] |
| See a list of your recently visited web pages by name | Press and hold or until the list appears. |
| See a list of your recently visited web pages by web address (URL) | Press the Option key, then press and hold or until the list appears. |

## Zoom

| Action | Shortcut or gesture |
| --- | --- |
| Exit full-screen view | Esc |
| Zoom website content | Press Command-Plus Sign (+) or Command-Minus Sign (-). |
| Zoom website text | Press and hold the Option key, then choose View > Make Text Bigger or View > Make Text Smaller. |

## Downloads

| Action | Shortcut or gesture |
| --- | --- |
| [Download](https://support.apple.com/en-ie/guide/safari/aside/glos55d15cc0/18.0/mac/15.0) a linked file | Option-click a link to the file. |
| Open a downloaded file | Double-click the file in the downloads list.  To show the downloads list, click near the top-right corner of the Safari window. |

## Reading List

| Action | Shortcut or gesture |
| --- | --- |
| Show or Hide the Reading List sidebar | Control-Command-2 |
| Add the current web page | Shift-Command-D |
| Add a linked web page | Shift-click a link to the web page. |
| Remove a web page | Control-click the web page summary in the sidebar, then choose Remove Item.  You can also swipe left over the web page summary, then click Remove. Or swipe all the way to the left until the web page summary disappears. |
| Open Reader | Shift-Command-R |
| Close Reader | Esc |

## Bookmarks

| Action | Shortcut or gesture |
| --- | --- |
| Add a bookmark to the Favourites bar | Click the [Smart Search field](https://support.apple.com/en-ie/guide/safari/aside/glos5a7c4a65/18.0/mac/15.0) to show the website’s full address and its icon, then drag the icon to the Favourites bar. |
| Open all bookmarks from a folder in the Favourites bar | Command-click the folder in the Favourites bar. |
| Move a bookmark on the Favourites bar | Drag the bookmark left or right. |
| Remove a bookmark from the Favourites bar | Drag the bookmark off the top of the bar. |

## Bookmarks sidebar and bookmarks view

| Action | Shortcut or gesture |
| --- | --- |
| Show or Hide the Bookmarks sidebar | Control-Command-1 |
| Select bookmarks and folders in the sidebar | Command-click each bookmark and folder.  Shift-click to extend the selection. |
| Select the next bookmark or folder | Up Arrow or Down Arrow |
| Open the selected bookmark | Space bar |
| Open the selected folder | Space bar or Right Arrow |
| Close the selected folder | Space bar or Left Arrow |
| Change the name or address of a bookmark | Select the bookmark, then press Return.  You can also [force click](https://support.apple.com/en-ie/guide/safari/aside/glosb234ba05/18.0/mac/15.0) the bookmark. |
| Cancel editing a bookmark name in the sidebar | Esc |
| Finish editing a bookmark name | Return |
| Create a folder containing the selected bookmarks and folders in bookmarks view | Option-click the New Folder button near the top-right corner. |
| Delete a bookmark | Control-click the bookmark, then choose Delete. |

**See also** [Apple Support article: Mac keyboard shortcuts](https://support.apple.com/102650) [Take screenshots or screen recordings on Mac](https://support.apple.com/en-ie/guide/mac-help/mh26782) [Create keyboard shortcuts for apps on Mac](https://support.apple.com/en-ie/guide/mac-help/mchlp2271) [Change settings in Safari on Mac](https://support.apple.com/en-ie/guide/safari/change-safari-settings-ibrwcd8bc28e/18.0/mac/15.0)

Thanks for your feedback.