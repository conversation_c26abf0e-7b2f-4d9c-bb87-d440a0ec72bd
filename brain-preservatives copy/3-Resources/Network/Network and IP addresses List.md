---
creation_date: 2025-06-23
modification_date: 2025-06-23
type: resource
source: Personal research
tags:
- para/resources
- guide
- ip 
- address
- tuf
- server
- paceyspace
- asus
area: Software-Development
difficulty: easy
url: 
---

# Network and IP addresses List

## Overview
<!-- Brief description of this resource -->
**1 Adelaide drive web services:**

| Service     | Protocol | External Host  | Internal Host | External Port | Internal Port | Authorised Keys |
| ----------- | -------- | -------------- | ------------- | ------------- | ------------- | --------------- |
| enhance     | TCP&UDP  | ************** | ***********   | 443           | 2087          |                 |
| ssh sirius  | TCP      | ************** | ***********   | 2862          | 2287          |                 |
| ICMP        | ICMP     | ************** | ***********   | 2293          | *             |                 |
| dropbeara   | TCP      | ************** | ***********   | 2222          | 2222          |                 |
| Tufhost ssh | TCP      | ************** | ************  | 22            | 22            |                 |



## Key Points
<!-- Main takeaways or important information -->
##### Finding list of IP addresses from known (visited) Networks---
To use the find command on a MacBook (or any Unix-like system) to search for a file containing a portion of an IP address, you can combine the find command with grep. Here's a step-by-step guide to help you achieve this:

**Step 1: Understand the Problem**

• You want to search for a file that contains a portion of an IP address.

• The search should include the entire file system, including hidden and system files.

• You need to search recursively through directories.

**Step 2: Use the** find **and** grep **Commands**

The find command is used to locate files, and grep is used to search for patterns within those files. You can combine them to search for a specific string (or portion of an IP address) in files.

**Basic Syntax**



find /path/to/search -type f -exec grep -l "pattern" {} \;

  

• /path/to/search: The directory where you want to start the search. To search the entire file system, use /.

• -type f: Restricts the search to regular files (not directories, symlinks, etc.).

• -exec grep -l "pattern" {} \;: Executes grep on each file found by find. The -l option tells grep to only list the filenames that contain the pattern.

**Example Command**

If you're looking for a portion of an IP address, such as 192.168, you can use:

  

  

sudo find / -type f -exec grep -l "192.168" {} \;

  

• sudo: Required to access system and hidden files that are not readable by your user account.

• /: Searches the entire file system.

• "192.168": The portion of the IP address you're searching for.

**Step 3: Search the Entire File System**

To search the entire file system, including hidden and system files, you need to use sudo because some directories (e.g., /private, /var, /etc) are restricted.

**Command to Search the Entire File System**

  

  

sudo find / -type f -exec grep -l "192.168" {} \;

  

**Explanation**

• sudo: Grants superuser privileges to access all files.

• /: Starts the search from the root directory, covering the entire file system.

• -type f: Ensures only regular files are searched.

• grep -l "192.168": Searches for the pattern 192.168 in each file and lists the filenames that contain it.

**Step 4: Handle Large File Systems**

Searching the entire file system can be time-consuming, especially on large drives. To speed up the process, you can:

1. **Limit the search to specific directories**: If you have an idea of where the file might be, specify a directory instead of /. For example:

  

  

sudo find /Users -type f -exec grep -l "192.168" {} \;

  

This searches only the /Users directory.

2. **Use** grep **with** find **more efficiently**: If you know the file type (e.g., .txt, .log), you can limit the search to those files:

  

  

sudo find / -type f -name "*.txt" -exec grep -l "192.168" {} \;

  

  

**Step 5: Handle Hidden Files**

By default, the find command includes hidden files (files starting with a dot, like .bashrc or .ssh). If you want to explicitly include hidden files, you can use the -name option with a pattern that matches hidden files:

  

  

sudo find / -type f -name ".*" -exec grep -l "192.168" {} \;

  

**Step 6: Handle Case Sensitivity**

By default, grep is case-sensitive. If you want a case-insensitive search, use the -i option:

  

  

sudo find / -type f -exec grep -il "192.168" {} \;

  

**Step 7: Monitor Progress**

Searching the entire file system can take a while. To monitor the progress, you can pipe the output to less or tee:

  

  

sudo find / -type f -exec grep -l "192.168" {} \; | less

  

**Final Command**

Here’s the complete command to search the entire file system for a portion of an IP address (192.168), including hidden and system files:

  

  

sudo find / -type f -exec grep -l "192.168" {} \;

  

**Additional Tips**

1. **Be Patient**: Searching the entire file system can take time, especially if there are many files.

2. **Use** sudo **Carefully**: Be cautious when using sudo, as it grants full system access.

3. **Limit the Search**: If possible, narrow down the search to specific directories to speed up the process.

**Output**

The command will output the paths of all files that contain the specified portion of the IP address. For example:

  

  

/path/to/file1.txt

/path/to/file2.log

  

This should help you locate the file containing the IP address you're looking for. If you need further assistance, feel free to ask


## Details
<!-- Detailed information -->
**List of known IP's**:
Mum's House (IPv4)
- *************
- *************
Unkown:
- ************

| **Category**        | **IP**        |
| ------------------- | ------------- |
| #Mum’s House (IPv4) | ************* |
|                     | ************* |
| *** #Unkown         | ************  |
|                     |               |
## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[Network and IP addresses List]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[Network and IP addresses List]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "guide") AND file.name != "Network and IP addresses List"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Quick Links
- [[Network and IP addresses List Project|New Project]]
- [[Network and IP addresses List Reference|Quick Reference]]
- [[3-Resources|All Resources]]
:

Mums Network
************ (AU)
[**************](https://whatismyipaddress.com/ip/**************)

Adelaide Drive:
[]()Jordan@**************
**************
