---
id: agents-obsidian-handbook
title: "AGENTS.md — Obsidian Vault Agent Handbook"
description: "Comprehensive guide for AI agents on formatting markdown documents in Obsidian vault with proper metadata, categorization, and structure for optimal Dataview and search functionality."
author: "Jordan Pacey"
version: "1.0.0"
created: "2025-09-07"
updated: "2025-09-07"
status: "active"
type: "handbook/guide"
area: "documentation/obsidian"
agents_supported: ["Cline", "Roo Code", "Augment Code", "Cursor"]
requires_metadata: true
visual_aids: true
diagram_formats: ["Mermaid", "SVG", "PNG"]
task_management: true
obsidian:
  dataview: true
  tasks_plugin: true
  mermaid: true
  templater: true
  para_method: true
tags:
  - agents
  - obsidian
  - metadata
  - documentation
  - dataview
  - para
  - vault-structure
  - markdown
  - categorization
  - search
  - knowledge-management
aliases: ["AGEN<PERSON>", "Agent Handbook", "Obsidian Agent Guide"]
---

# AGENTS.md — Obsidian Vault Agent Handbook

---

## Purpose

This handbook provides comprehensive instructions for AI agents on how to format markdown documents in this Obsidian vault. The goal is to ensure consistent, searchable, and well-categorized content that maximizes the effectiveness of Dataview queries and vault search functionality.

## Core Principles

### 1. Metadata is Critical
- **Every document MUST have comprehensive frontmatter metadata**
- Metadata powers Dataview queries, search, and vault organization
- Missing or incomplete metadata severely impacts vault functionality
- Use verbose, descriptive metadata rather than minimal entries

### 2. PARA Method Structure
This vault follows the PARA method:
- **1-Projects**: Active projects with deadlines
- **2-Areas**: Ongoing responsibilities and interests
- **3-Resources**: Reference materials and knowledge
- **4-Archive**: Completed or inactive items

### 3. Consistent Categorization
- Use standardized tags and categories
- Follow established naming conventions
- Maintain hierarchical relationships between notes

---

## Required Metadata Structure

### Universal Fields (All Documents)
```yaml
---
id: unique-identifier
title: "Document Title"
description: "Brief description of content"
creation_date: YYYY-MM-DD
modification_date: YYYY-MM-DD
type: document-type
area: primary-area
tags: [tag1, tag2, tag3]
author: author-name
status: active|planning|on-hold|completed|cancelled
priority_score: 0-100
related:
  depends-on: []
  blocks: []
  area-overlap: []
  references: []
  supports: []
  relates-to: []
---
```

### Document Types and Specific Fields

#### Projects (type: project)
```yaml
project_owner: "Jordan|Other"
project_client: "Personal|Church|University|Client Name"
priority: "high|medium|low"
deadline: YYYY-MM-DD
start_date: YYYY-MM-DD
completion_percentage: 0-100
estimated_hours: number
```

#### Resources (type: resource)
```yaml
source: "Personal research|Website|Book|Course|Documentation|Video|Podcast|Other"
difficulty: "easy|medium|hard|expert"
resource_type: "guide|reference|tool|tutorial|template|checklist|documentation|other"
url: "URL if applicable"
usefulness_rating: 1-5
last_used: YYYY-MM-DD
keywords: [keyword1, keyword2]
```

#### Areas (type: area)
```yaml
responsibility_level: "primary|secondary|monitoring"
time_commitment: "daily|weekly|monthly|quarterly|annual"
stakeholders: [person1, person2]
```

#### Tasks (type: task)
```yaml
due: YYYY-MM-DD
priority: "high|medium|low"
project: "project-name"
estimated_time: "duration"
```

---

## Standardized Categories and Tags

### Areas
- `Software-Development`
- `Administration`
- `Personal`
- `Church`
- `University`
- `Finance`
- `Compliance`

### Priority Tags
- `critical` - Highest importance
- `urgent` - Time-sensitive
- `important` - High value
- `routine` - Regular maintenance

### Content Tags
- `guide` - How-to content
- `reference` - Quick lookup
- `tool` - Software/utility
- `tutorial` - Step-by-step learning
- `template` - Reusable format
- `checklist` - Verification list
- `documentation` - Technical docs

### Technology Tags
- `software-dev`
- `python`
- `javascript`
- `dotnet`
- `docker`
- `nginx`
- `mariadb`
- `s3`
- `ci-cd`

---

## Document Structure Template

### Standard Document Format
```markdown
---
[COMPREHENSIVE FRONTMATTER METADATA]
---

# Document Title

---

## Overview
Brief description of the document's purpose and content.

## Key Points
- Main takeaways
- Important information
- Critical details

## Details
Detailed information organized in logical sections.

### Subsection 1
Content with proper formatting.

### Subsection 2
More detailed content.

## Examples
```language
// Code examples with proper syntax highlighting
```

## Visual Aids
[Include Mermaid diagrams, SVG, or PNG as appropriate]

## Related Items
[Dataview queries to show related content]

## Notes
Additional context or considerations.

---

### Tags
#tag1 #tag2 #tag3 #area #priority

---
```

---

## Dataview Integration

### Essential Dataview Patterns

#### Project Dashboard
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority",
  completion_percentage + "%" as "Progress",
  deadline as "Due"
FROM "1-Projects"
WHERE status != "completed"
SORT priority_score DESC
```

#### Resource Library
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  resource_type as "Type",
  difficulty as "Difficulty",
  usefulness_rating as "Rating"
FROM "3-Resources"
WHERE area = "Software-Development"
SORT usefulness_rating DESC
```

#### Area Overview
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  responsibility_level as "Level",
  time_commitment as "Frequency"
FROM "2-Areas"
WHERE status = "active"
SORT responsibility_level ASC
```

---

## Visual Aids Policy

### Preferred Formats
1. **Mermaid** - Native Obsidian support, version controllable
2. **SVG** - Scalable, crisp rendering
3. **PNG** - Fallback for screenshots or raster images

### Mermaid Examples
```mermaid
flowchart TD
    A[Agent Input] --> B{Has Metadata?}
    B -->|Yes| C[Validate Structure]
    B -->|No| D[Add Required Metadata]
    C --> E[Process Content]
    D --> E
    E --> F[Save to Vault]
```

### Visual Aid Guidelines
- Include descriptive titles and captions
- Place diagrams near relevant content
- Use consistent styling and colors
- Tag visual content for easy retrieval

---

## Agent-Specific Instructions

### For All Agents
1. **Always include comprehensive metadata**
2. **Use horizontal rules (---) at document start and end**
3. **Apply consistent formatting and structure**
4. **Include relevant tags for searchability**
5. **Create relationships between related documents**

### Code Formatting
- Use proper language identifiers in code blocks
- Include syntax highlighting: ```python, ```javascript, ```yaml
- Keep code examples concise and relevant
- Provide context for code snippets

### Link Management
- Use [[WikiLinks]] for internal references
- Include descriptive link text
- Maintain bidirectional relationships
- Update related documents when creating new content

---

## Quality Checklist

Before saving any document, verify:
- [ ] Complete frontmatter metadata
- [ ] Proper document type classification
- [ ] Relevant tags and categories
- [ ] Consistent formatting
- [ ] Appropriate visual aids
- [ ] Related document links
- [ ] Dataview compatibility
- [ ] Search optimization

---

## Relationship Management

### Relationship Types
- **depends-on**: Prerequisites or dependencies
- **blocks**: Items blocked by this document
- **area-overlap**: Cross-functional areas
- **references**: External sources or citations
- **supports**: Items this document enables
- **relates-to**: General associations

### Relationship Best Practices
```yaml
related:
  depends-on: ["[[Prerequisite Document]]"]
  blocks: ["[[Blocked Item]]"]
  area-overlap: ["[[Related Area]]"]
  references: ["[[Source Material]]"]
  supports: ["[[Enabled Project]]"]
  relates-to: ["[[Similar Topic]]"]
```

---

## Search Optimization

### Keyword Strategy
- Include synonyms and alternative terms
- Use domain-specific terminology
- Add common misspellings in keywords array
- Consider search intent and user queries

### Tag Hierarchy
```
Primary: #software-dev
Secondary: #python, #javascript, #dotnet
Specific: #flask, #react, #entity-framework
Context: #tutorial, #reference, #troubleshooting
```

---

## Automated Workflows

### Priority Score Calculation
Documents automatically calculate priority scores based on:
- Deadline proximity (projects)
- Usefulness rating (resources)
- Tag importance weights
- Recent usage patterns
- Relationship dependencies

### Template Integration
- Use Templater for dynamic content generation
- Auto-populate metadata fields
- Calculate relationships and scores
- Generate contextual queries

---

## Maintenance Guidelines

### Regular Reviews
- Monthly metadata audits
- Quarterly relationship updates
- Annual tag taxonomy review
- Continuous priority score validation

### Content Lifecycle
1. **Creation**: Full metadata, proper categorization
2. **Active Use**: Regular updates, relationship maintenance
3. **Archive**: Status change, relationship cleanup
4. **Deletion**: Relationship impact assessment

---

## Error Prevention

### Common Mistakes to Avoid
- Incomplete or missing metadata
- Inconsistent tag usage
- Broken internal links
- Orphaned documents without relationships
- Duplicate or conflicting information

### Validation Checklist
- Metadata completeness
- Tag standardization
- Link integrity
- Dataview query compatibility
- Search result accuracy

---

## Advanced Features

### Custom Dataview Queries
Create specialized queries for:
- Cross-project dependencies
- Resource utilization tracking
- Area responsibility mapping
- Knowledge gap identification
- Progress monitoring

### Integration Points
- Tasks plugin for project management
- Calendar for deadline tracking
- Graph view for relationship visualization
- Search for content discovery
- Export for external reporting

---

### Tags
#agents #obsidian #metadata #documentation #dataview #para #vault-structure #markdown #categorization #search #knowledge-management

---
