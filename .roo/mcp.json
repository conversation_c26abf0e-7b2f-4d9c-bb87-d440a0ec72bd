{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "~/Library/Mobile Documents/com~apple~CloudDocs/Projects"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "~/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSA4ZlizisXgJ3gFms-FzfUCDoN2arw"}}, "time": {"command": "uvx", "args": ["mcp-server-time"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sqlite": {"command": "uvx", "args": ["mcp-server-sqlite", "--db-path", "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/identifier.sqlite"]}, "cloudflare": {"command": "npx", "args": ["mcp-remote", "https://observability.mcp.cloudflare.com/sse"]}, "jetbrains": {"command": "npx", "args": ["-y", "@jetbrains/mcp-proxy"]}}}