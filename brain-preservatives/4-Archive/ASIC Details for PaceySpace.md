---
creation_date: 2025-09-07
modification_date: 2025-09-07
type: index
aliases:
  - Archive
tags:
  - para/archive
  - index
---

# Archive

> Archive contains items that are inactive but might be referenced in the future.

![[Pasted image 20250907100815.png]]
Your welcome letter (including the ASIC key) and the record of registration for PACEYSPACE, transaction 1-W927EOP dated 22/08/2025 are available from the following links:

[Welcome letter](https://post.asic.gov.au/CustomerDropBoxR1/corresp?f=Notification_1-W927ER0.PDF&e=20251123&i=ASC0rlZiRJRJimdZh8TIzsa8hOODwmaSoBpoQsQV6q4fg%2BY%3D)

[Record of Registration](https://post.asic.gov.au/CustomerDropBoxR1/corresp?f=Notification_1-W927ER2.PDF&e=20251123&i=ASC01oq4yfpJglXuotmjBQN4JmdGXEcFTw99Nj4hqULfAsc%3D)
[[Pasted image 20250907100815.png]]
Select these links to view, save or print the information. These links will remain active for 30 days.

If you have any questions, contact us at [www.asic.gov.au/question](www.asic.gov.au/question)

Regards, 

  
Registry Officer   
Registry Operations   
Australian Securities and Investments Commission 

_Please consider the environment before printing this document._

_Information collected by ASIC may contain personal information. Please refer to our [Privacy Policy](https://asic.gov.au/privacy/)_ _for information about how we handle your personal information, your rights to seek access to and correct your personal information, and how to complain about breaches of your privacy by ASIC._

_This e-mail and any attachments are intended for the addressee(s) only and may be confidential. They may contain legally privileged, copyright material or personal and /or confidential information. You should not read, copy, use or disclose the content without authorisation. If you have received this email in error, please notify the sender as soon as possible, delete the email and destroy any copies. This notice should not be removed._

---
## ASIC KEY:
```
1-***********
```
## Completed Projects
```dataview
TABLE
  completion_date as "Completed",
  type as "Type"
FROM "4-Archive"
WHERE contains(status, "completed")
SORT completion_date DESC
```

## Software Development Archive
```dataview
TABLE
  completion_date as "Completed",
  type as "Type"
FROM "4-Archive"
WHERE contains(tags, "software-dev") OR contains(tags, "programming")
SORT completion_date DESC
```

## Administration Archive
```dataview
TABLE
  completion_date as "Completed",
  type as "Type"
FROM "4-Archive"
WHERE contains(tags, "admin") OR contains(tags, "administration")
SORT completion_date DESC
```

## All Archived Items
```dataview
TABLE
  type as "Type",
  creation_date as "Created",
  completion_date as "Completed"
FROM "4-Archive"
SORT completion_date DESC
```

## Related
- [[1-Projects]]
- [[2-Areas]]
- [[3-Resources]]
	- [[Home]]