---
creation_date: 2025-09-16
modification_date: 2025-09-16
tags: [terminal, cli, ai-agents, agent, dev, code, claude, gemini, auggie, augment]

---

> *Skip to * [[#Agents Command Usage]] *once these dependancies have been setup, to run and update the Agents!*
# Dependancies
#dependancies #packages #system #library #libraries #npm #npx #environment #nvm

### NVM Installation
	For Installing AND Updating
> with curl
```bash
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash
```
> with wget
```bash
wget -qO- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash
```
> Installing in docker
	*When invoking bash as a non-interactive shell, like in a Docker container, none of the regular profile files are sourced. In order to use `nvm`, `node`, and `npm` like normal, you can instead specify the special `BASH_ENV`variable, which bash sources when invoked non-interactively.*	
```dockerfile
# Use bash for the shell
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

# Create a script file sourced by both interactive and non-interactive bash shells
ENV BASH_ENV /home/<USER>/.bash_env
RUN touch "${BASH_ENV}"
RUN echo '. "${BASH_ENV}"' >> ~/.bashrc

# Download and install nvm
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | PROFILE="${BASH_ENV}" bash
RUN echo node > .nvmrc
RUN nvm install
```

> *Skip to * [[#Agents Command Usage]] *once these dependancies have been setup, to run and update the Agents!*
##### Installing in Docker for CICD-Jobs
[NVM_Docs](https://github.com/nvm-sh/nvm#installing-in-docker-for-cicd-jobs)
More robust, works in CI/CD-Jobs. Can be run in interactive and non-interactive containers. See [#3531](https://github.com/nvm-sh/nvm/issues/3531).
```dockerfile
FROM ubuntu:latest
ARG NODE_VERSION=20

# install curl
RUN apt update && apt install curl -y

# install nvm
RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.3/install.sh | bash

# set env
ENV NVM_DIR=/root/.nvm

# install node
RUN bash -c "source $NVM_DIR/nvm.sh && nvm install $NODE_VERSION"

# set ENTRYPOINT for reloading nvm-environment
ENTRYPOINT ["bash", "-c", "source $NVM_DIR/nvm.sh && exec \"$@\"", "--"]

# set cmd to bash
CMD ["/bin/bash"]

```
This example defaults to installation of nodejs version 20.x.y. Optionally you can easily override the version with docker build args like:
```
docker build -t nvmimage --build-arg NODE_VERSION=19 .
```
After creation of the image you can start container interactively and run commands, for example:
```
docker run --rm -it nvmimage

root@0a6b5a237c14:/# nvm -v
0.40.3

root@0a6b5a237c14:/# node -v
v19.9.0

root@0a6b5a237c14:/# npm -v
9.6.3
```

Noninteractive example:
```
user@host:/tmp/test $ docker run --rm -it nvmimage node -v
v19.9.0
user@host:/tmp/test $ docker run --rm -it nvmimage npm -v
9.6.3
```

# Agents Command Usage
*Skip to * [[#MCP]] *For the Agentic special sauce*
>ordered by my *current* preferences

CLI Agent Rankings
1. ## Claude Code (Anthropic)
	```bash
	?
	```
2. ## Auggie CLI Agent (Augment Code)
	``` bash
	npm install -g @augmentcode/auggie
	```
3. ## Qwen Coder (Alibaba)
	```bash
	npm install -g @qwen-code/qwen-code@latest
	qwen –version
	qwen
	```
4. ## Gemini CLI (Google)
```bash

```




# MCP

## Vector Storage for Embedding
### `Qdrant`
> 	[[Qdrant info]]
