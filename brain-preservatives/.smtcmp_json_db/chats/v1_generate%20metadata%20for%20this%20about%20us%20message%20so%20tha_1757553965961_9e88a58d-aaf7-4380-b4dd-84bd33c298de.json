{"id": "9e88a58d-aaf7-4380-b4dd-84bd33c298de", "title": "generate metadata for this about us message so tha", "messages": [{"role": "user", "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "generate metadata for this about us message so that i can filter it effectively in views and categorise", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "## Potentially Relevant Snippets from the current vault\n```2-Areas/Task Management/Todoist/CRUCA Todoist Tasks to 21-04-2025.md\n3|| TYPE | CONTENT                                                                                                                                                                                                                                                                                                                             | DESCRIPTION\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n19|## Markdown Documentation Generator from provided Agent Actions (Obsidian Markdown Format wit richer Metadata for search and dataview)\n20|```\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n19|## Markdown Documentation Generator from provided Agent Actions (Obsidian Markdown Format wit richer Metadata for search and dataview)\n20|```\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n19|## Markdown Documentation Generator from provided Agent Actions (Obsidian Markdown Format wit richer Metadata for search and dataview)\n20|```\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n19|## Markdown Documentation Generator from provided Agent Actions (Obsidian Markdown Format wit richer Metadata for search and dataview)\n20|```\n```\n```1-Projects/Yendorcats/Project Development Journal and Notes.md\n1018|### Search and Filtering\n1019|\n1020|  \n1021|\n1022|The system provides advanced search and filtering capabilities based on metadata:\n1023|\n1024|  \n1025|\n1026|- Filter by category (studs, queens, kittens, gallery)\n1027|\n1028|- Filter by gender (M/F)\n1029|\n1030|- Filter by breed\n1031|\n1032|- Filter by bloodline\n1033|\n1034|- Filter by hair color\n1035|\n1036|- Filter by age range\n1037|\n1038|- Filter by tags\n1039|\n1040|- Sort by date, name, or age\n1041|\n1042|- Sort in ascending or descending order\n```\n```3-Resources/PaceySpace Wiki/Page-Templates/README.md\n1|---\n2|creation_date: 2025-04-16\n3|modification_date: 2025-04-16\n4|type: resource\n5|source: Internal documentation\n6|tags: [para/resources, templates, guide, obsidian, metadata]\n7|---\n8|\n9|# Enhanced Templates Guide\n10|\n11|This guide explains how to use the enhanced templates and scripts to create powerful, interconnected notes with rich metadata.\n```\n```Templates/README.md\n1|---\n2|creation_date: 2025-04-16\n3|modification_date: 2025-04-16\n4|type: resource\n5|source: Internal documentation\n6|tags: [para/resources, templates, guide, obsidian, metadata]\n7|---\n8|\n9|# Enhanced Templates Guide\n10|\n11|This guide explains how to use the enhanced templates and scripts to create powerful, interconnected notes with rich metadata.\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n21|Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. Refer to the AGENTS.md file for further guidelines. This obsidian markdown wiki will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  ```csharp code blocks ```) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) Pay very close attention to adding appropriate markdown document metadata for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n21|Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. Refer to the AGENTS.md file for further guidelines. This obsidian markdown wiki will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  ```csharp code blocks ```) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) Pay very close attention to adding appropriate markdown document metadata for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n21|Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. Refer to the AGENTS.md file for further guidelines. This obsidian markdown wiki will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  ```csharp code blocks ```) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) Pay very close attention to adding appropriate markdown document metadata for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n21|Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. Refer to the AGENTS.md file for further guidelines. This obsidian markdown wiki will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  ```csharp code blocks ```) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) Pay very close attention to adding appropriate markdown document metadata for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it\n```\n```3-Resources/Software Guides/Second Brain Notetaking.md\n12|I need assistance with cleaning out my obsidian vault. use obsidian features. I want to make use of obsidians graph (with jugl global graph plugin) and dataview plugin. I primarily search my notes and make queries rather than following a folder structure, finding folders is a waste of time for me. filters are great. i need you to add plenty of metadata for queries and searches like everyone else does for using these plugins. I have a backup of this vault so we can to some cleaning. go ahead and make changes, we may have to prompt several times to get through everything. I have a rough template file in the Template/ folder, however you may nave a better template than i do. Just make sure the metadata can be automated (like date for example, could be set to todays date) and other examples. You can even see that the creations date of google keep notes are preserved and date back many years ago. Can you please add the note files date metadata to the obsidian notes property field too?\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n21|for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it can be easily indexed and searched. Searching will be the method of information retrieval within obsidian (omnisearch) aswell as notion and fuzzyfinder searches in neovim and programs like cling (macos).  make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. cmaintain lear, neat anf efficient and formatting so that the documents/responses you generate provide easy and useful information retrieval. Keep this resource material concise and useful. I will only copy the reference material, not conversational material.\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n21|for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it can be easily indexed and searched. Searching will be the method of information retrieval within obsidian (omnisearch) aswell as notion and fuzzyfinder searches in neovim and programs like cling (macos).  make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. cmaintain lear, neat anf efficient and formatting so that the documents/responses you generate provide easy and useful information retrieval. Keep this resource material concise and useful. I will only copy the reference material, not conversational material.\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n21|for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it can be easily indexed and searched. Searching will be the method of information retrieval within obsidian (omnisearch) aswell as notion and fuzzyfinder searches in neovim and programs like cling (macos).  make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. cmaintain lear, neat anf efficient and formatting so that the documents/responses you generate provide easy and useful information retrieval. Keep this resource material concise and useful. I will only copy the reference material, not conversational material.\n```\n```3-Resources/Prompt Engineering/3System Prompt Templates.md\n21|for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it can be easily indexed and searched. Searching will be the method of information retrieval within obsidian (omnisearch) aswell as notion and fuzzyfinder searches in neovim and programs like cling (macos).  make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. cmaintain lear, neat anf efficient and formatting so that the documents/responses you generate provide easy and useful information retrieval. Keep this resource material concise and useful. I will only copy the reference material, not conversational material.\n```\n```0.2-Tasks/Tasks TOC.md\n1|---\n2|# CORE METADATA\n3|creation_date: 2025-08-07\n4|modification_date: 2025-08-07\n5|type: index\n6|status: active\n7|priority: high\n8|area_category: Administration\n9|owner: Jordan\n10|tags: [tasks, index, toc, navigation]\n11|\n12|# RELATIONSHIPS (Enhanced System)\n13|related_areas: [\"[[2-Areas/Task Management]]\", \"[[2-Areas/Website-Maintenance]]\"]\n14|related_resources: [\"[[3-Resources/Vault Organization Guide]]\", \"[[3-Resources/Software Development Resources]]\"]\n15|related_people: [\"[[Jordan]]\"]\n16|\n17|# TASK MANAGEMENT\n18|task_priority: high\n19|task_context: admin\n20|---\n21|\n22|# 📋 Tasks - Table of Contents\n23|\n24|> **Central navigation hub for the task management system** - Your guide to all task-related resources and workflows.\n25|\n26|---\n```\n```0.2-Tasks/Daily Task Sync.md\n151|### Task File Naming Convention\n152|```\n153|YYYY-MM-DD-TaskNumber-SourceNote.md\n154|Example: 2025-07-14-001-2025-06-25.md\n155|```\n156|\n157|### Automated Metadata Generation\n158|```yaml\n159|---\n160|# CORE METADATA\n161|creation_date: 2025-07-14\n162|modification_date: 2025-07-14\n163|type: task\n164|status: not-started\n165|priority: high\n166|area_category: [Auto-detected from tags]\n167|owner: Jordan\n168|tags: [task, daily-carryover, [context-tags]]\n169|\n170|# TASK MANAGEMENT\n171|task_priority: [Extracted from daily note]\n172|task_context: [Extracted from hashtags]\n173|estimated_time: [Auto-estimated based on content]\n174|energy_required: [Auto-assigned based on context]\n175|due_date: [Extracted if present]\n176|\n177|# RELATIONSHIPS (Enhanced System)\n178|related_projects: [[\"[[Auto-detected from tags]]\"]]\n179|related_areas: [[\"[[Auto-detected from context]]\"]]\n180|source_note: \"[[Original Daily Note]]\"\n181|source_date: 2025-06-25\n182|task_number: 001\n183|\n184|# TASK LIFECYCLE\n185|created_date: 2025-07-14\n186|migrated_from: \"0-Daily Notes/2025-06-25.md\"\n187|original_task_line: 30\n188|---\n189|```\n190|\n191|---\n```\n```Scripts/Vault Maintenance Scripts.md\n28|## 🔧 Standardize Metadata Script\n```\n```2-Areas/Task Management/Tasks.md\n1|---\n2|# CORE METADATA\n3|creation_date: 2025-06-08\n4|modification_date: 2025-07-14\n5|type: index\n6|status: active\n7|priority: high\n8|area_category: Administration\n9|owner: Jordan\n10|aliases: [Tasks, Todo]\n11|tags: [tasks, index, productivity, para/areas, task-management]\n12|\n13|# RELATIONSHIPS (Enhanced System)\n14|related_areas: [\"[[Task Management]]\", \"[[Administration]]\"]\n15|related_resources: [\"[[Task Management System]]\", \"[[Productivity Tools]]\"]\n16|related_people: [\"[[Jordan]]\"]\n17|area: Task Management\n18|---\n19|\n20|# 🎯 Task Management Dashboard\n21|\n22|> **Central command for all tasks across the vault** - Prioritized, contextualized, and actionable.\n23|\n24|## 🔥 URGENT - Do First\n25|```dataview\n26|TABLE WITHOUT ID\n27|  \"🔥\" as \"\",\n28|  file.link as \"URGENT TASKS\",\n29|  choice(estimated_time, estimated_time, \"?\") as \"Time\",\n30|  choice(due, due, \"ASAP\") as \"Due\"\n31|FROM \"\"\n32|WHERE !completed\n33|  AND task_priority = \"urgent\"\n34|  AND (due <= date(today) OR !due)\n35|SORT file.path ASC\n36|LIMIT 8\n37|```\n```\n```3-Resources/Vault Organization System Guide.md\n1|---\n2|# CORE METADATA\n3|creation_date: 2025-07-14\n4|modification_date: 2025-07-14\n5|type: resource\n6|status: active\n7|priority: critical\n8|area_category: Administration\n9|owner: Jordan\n10|tags: [para/resources, guide, obsidian, vault-organization, metadata, productivity]\n11|\n12|# RESOURCE CLASSIFICATION\n13|source: Personal research\n14|difficulty: intermediate\n15|resource_type: guide\n16|url: \"\"\n17|last_verified: 2025-07-14\n18|\n19|# RELATIONSHIPS (Enhanced System)\n20|related_projects: [\"[[Vault Restructuring Project]]\"]\n21|related_areas: [\"[[Task Management]]\", \"[[Administration]]\"]\n22|related_people: [\"[[Jordan]]\"]\n23|references: [\"[[Dataview Documentation]]\", \"[[Obsidian Best Practices]]\"]\n24|inspired_by: [\"[[PARA Method]]\", \"[[Getting Things Done]]\"]\n25|---\n26|\n27|# 🏗️ Vault Organization System Guide\n28|\n29|## Overview\n30|This guide documents the enhanced metadata schema, relationship system, and task management approach implemented to create a highly connected, efficient knowledge management system in Obsidian.\n```\n```1-Projects/Yendorcats/Project Development Journal and Notes.md\n628|## S3 Metadata Implementation\n629|\n630|  \n631|\n632|The project uses Backblaze B2 S3-compatible storage with rich metadata for cat gallery images. The metadata includes:\n633|\n634|  \n635|\n636|### Required Fields\n637|\n638|- Name of the cat\n639|\n640|- Gender (M/F)\n641|\n642|- Date uploaded (set automatically)\n643|\n644|- File format\n645|\n646|- Content type\n647|\n648|  \n649|\n650|### Optional User-Inputted Fields\n651|\n652|- Description\n653|\n654|- Age\n655|\n656|- Bloodline\n657|\n658|- Breed\n659|\n660|- Hair color\n661|\n662|- Date taken (photo)\n663|\n664|- Personality\n665|\n666|- Category\n667|\n668|- Tags (for filtering and search)\n669|\n670|- Mother/Father information\n671|\n672|  \n673|\n674|### System-Generated Fields\n675|\n676|- File size\n677|\n678|- Image dimensions\n679|\n680|- Upload IP and user agent\n681|\n682|- Upload session ID\n683|\n684|  \n685|\n686|This rich metadata enables advanced filtering and search capabilities, allowing users to find cats based on various criteria.\n687|\n688|  \n689|\n690|For detailed information about the metadata implementation, see the [S3 Metadata Implementation Guide](1-Projects/Yendorcats/YendorCats%20S3%20Metadata%20Implementation.md).\n```\n```Templates/Enhanced Task.md\n1|---\n2|# CORE METADATA\n3|creation_date: <% tp.date.now(\"YYYY-MM-DD\") %>\n4|modification_date: <% tp.date.now(\"YYYY-MM-DD\") %>\n5|type: task\n6|status: not-started\n7|priority: <% await tp.system.suggester([\"critical\", \"high\", \"medium\", \"low\"], [\"critical\", \"high\", \"medium\", \"low\"], false, \"Task Priority\") %>\n8|area_category: <% await tp.system.suggester([\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], [\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], false, \"Area Category\") %>\n9|owner: Jordan\n10|tags: [task, <% await tp.system.suggester([\"urgent\", \"project\", \"admin\", \"maintenance\", \"creative\"], [\"urgent\", \"project\", \"admin\", \"maintenance\", \"creative\"], false, \"Primary Tag\") %>]\n```\n```3-Resources/YendorCats S3 Metadata Implementation.md\n1|---\n2|# CORE METADATA\n3|creation_date: 2025-04-27\n4|modification_date: 2025-07-14\n5|type: resource\n6|status: active\n7|priority: high\n8|area_category: Software-Development\n9|owner: Jordan\n10|tags: [para/resources, guide, software-dev, s3, metadata, backblaze]\n11|\n12|# RESOURCE CLASSIFICATION\n13|source: Project documentation\n14|difficulty: intermediate\n15|resource_type: guide\n16|url: \"\"\n17|last_verified: 2025-07-14\n18|\n19|# RELATIONSHIPS (Enhanced System)\n20|related_projects: [\"[[YendorCats Project Documentation]]\"]\n21|related_areas: [\"[[Software-Development]]\"]\n22|related_people: [\"[[Jordan]]\"]\n23|references: [\"[[AWS S3 Documentation]]\", \"[[.NET 8 Documentation]]\"]\n24|inspired_by: [\"[[S3 Best Practices Guide]]\"]\n25|---\n26|\n27|# YendorCats S3 Metadata Implementation\n```\n```Family/split tunelling with mullvad.md\n1|---\n2|creation_date: 2025-04-05\n3|modification_date: 2025-04-05\n4|type: note\n5|aliases: []\n6|tags: []\n7|area: \n8|project: \n9|resource: \n10|archive: \n11|status: active\n12|priority: \n13|links: []\n14|related: []\n15|---\n16|\n17|# Untitled\n18|\n19|## Content\n20|\n21|\n22|## References\n23|\n24|\n25|## Tasks\n26|- [ ] \n27|\n28|## Metadata\n29|- **Original Creation**: 2025-04-05\n30|- **Source**: \n31|- **Context**:\n```\n```1-Projects/Yendorcats/Project Development Journal and Notes.md\n938|### Metadata Management\n939|\n940|  \n941|\n942|The system supports two methods for managing cat image metadata:\n943|\n944|  \n945|\n946|1. **S3 Object Metadata (Primary Method)**:\n947|\n948|   - All metadata is stored as S3 object metadata\n949|\n950|   - Metadata is retrieved directly from S3 when displaying images\n951|\n952|   - Supports rich metadata with many fields\n953|\n954|   - Enables advanced filtering and search capabilities\n955|\n956|  \n957|\n958|2. **Filename Parsing (Fallback Method)**:\n959|\n960|   - For backward compatibility with existing images\n961|\n962|   - Extracts basic metadata from filenames\n963|\n964|   - Filename format: `[cat's name]-[age]-[date(DDMMYY)]-[order].jpg`\n965|\n966|   - Example: `Georgia-2.6-230325-1.jpg` (Name: Georgia, Age: 2.6 years, Date: March 23, 2025, Order: 1)\n```\n```3-Resources/PaceySpace Wiki/Page-Templates/README.md\n80|## Utility Scripts\n81|\n82|### Template Selector\n83|- **File**: `Template Selector.md`\n84|- **Purpose**: Helps you select the appropriate template based on what you're trying to create\n85|- **Usage**: Run this script when you want to create a new note and it will guide you through the process\n86|\n87|### Create Related Note\n88|- **File**: `Create Related Note.md`\n89|- **Purpose**: Creates a new note that's automatically linked to the current note\n90|- **Usage**: Run this script from any note to create a related note that will be automatically linked in both directions\n91|\n92|### Enhance Metadata\n93|- **File**: `Enhance Metadata.md`\n94|- **Purpose**: Helps you enhance the metadata of existing notes\n95|- **Usage**: Run this script from any note to add or update metadata fields based on the note type\n```\n```Templates/README.md\n80|## Utility Scripts\n81|\n82|### Template Selector\n83|- **File**: `Template Selector.md`\n84|- **Purpose**: Helps you select the appropriate template based on what you're trying to create\n85|- **Usage**: Run this script when you want to create a new note and it will guide you through the process\n86|\n87|### Create Related Note\n88|- **File**: `Create Related Note.md`\n89|- **Purpose**: Creates a new note that's automatically linked to the current note\n90|- **Usage**: Run this script from any note to create a related note that will be automatically linked in both directions\n91|\n92|### Enhance Metadata\n93|- **File**: `Enhance Metadata.md`\n94|- **Purpose**: Helps you enhance the metadata of existing notes\n95|- **Usage**: Run this script from any note to add or update metadata fields based on the note type\n```\n```Family/Bridge network to access zero tier services from internet.md\n52|## Content\n53|\n54|\n55|## References\n56|\n57|\n58|## Tasks\n59|- [ ] \n60|\n61|## Metadata\n62|- **Original Creation**: 2025-04-06\n63|- **Source**: \n64|- **Context**:\n```\n```3-Resources/Software Guides/Flaresolverr and Prowlarr.md\n247|## Content\n248|\n249|\n250|## References\n251|\n252|\n253|## Tasks\n254|- [ ] \n255|\n256|## Metadata\n257|- **Original Creation**: 2025-04-06\n258|- **Source**: \n259|- **Context**:\n```\n```3-Resources/Software Guides/Unix Filesystem guides.md\n45|## Content\n46|\n47|\n48|## References\n49|\n50|\n51|## Tasks\n52|- [ ] \n53|\n54|## Metadata\n55|- **Original Creation**: 2025-04-07\n56|- **Source**: \n57|- **Context**:\n```\n```Templates/README.md\n61|### Meeting Template\n62|- **File**: `Meeting.md`\n63|- **Purpose**: Create detailed meeting notes that link to related projects and areas\n64|- **Key Features**:\n65|  - Rich metadata including participants, location, project, area\n66|  - Action items with assignees and due dates\n67|  - Follow-up section\n68|  - Related projects and areas views\n69|\n70|### Person Template\n71|- **File**: `Person.md`\n72|- **Purpose**: Create detailed contact notes with comprehensive metadata\n73|- **Key Features**:\n74|  - Rich metadata including organization, role, relationship\n75|  - Interactions log\n76|  - Related projects and areas views\n77|  - Meetings view\n78|  - Quick links to create related notes\n```\n```3-Resources/PaceySpace Wiki/Page-Templates/README.md\n61|### Meeting Template\n62|- **File**: `Meeting.md`\n63|- **Purpose**: Create detailed meeting notes that link to related projects and areas\n64|- **Key Features**:\n65|  - Rich metadata including participants, location, project, area\n66|  - Action items with assignees and due dates\n67|  - Follow-up section\n68|  - Related projects and areas views\n69|\n70|### Person Template\n71|- **File**: `Person.md`\n72|- **Purpose**: Create detailed contact notes with comprehensive metadata\n73|- **Key Features**:\n74|  - Rich metadata including organization, role, relationship\n75|  - Interactions log\n76|  - Related projects and areas views\n77|  - Meetings view\n78|  - Quick links to create related notes\n```\n```3-Resources/Software Guides/VPN.md\n26|## Content\n27|\n28|\n29|## References\n30|\n31|\n32|## Tasks\n33|- [ ] \n34|\n35|## Metadata\n36|- **Original Creation**: 2025-04-05\n37|- **Source**: \n38|- **Context**:\n```\n```3-Resources/Prompt Engineering/Prompt Rules for Technical Markdown Documentation  Documents.md\n37|Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. These will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  `csharp code blocks using 3 backticks and csharp in header of block`) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) I make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. I want it to have neat and pretty format for clear and satisfying viewing, and efficient and easy information retrieval.\n```\n```Templates/Enhanced Area.md\n1|---\n2|# CORE METADATA\n3|creation_date: <% tp.date.now(\"YYYY-MM-DD\") %>\n4|modification_date: <% tp.date.now(\"YYYY-MM-DD\") %>\n5|type: area\n6|status: <% await tp.system.suggester([\"active\", \"inactive\", \"archived\"], [\"active\", \"inactive\", \"archived\"], false, \"Area Status\") %>\n7|priority: <% await tp.system.suggester([\"critical\", \"high\", \"medium\", \"low\"], [\"critical\", \"high\", \"medium\", \"low\"], false, \"Area Priority\") %>\n8|area_category: <% await tp.system.suggester([\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], [\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], false, \"Area Category\") %>\n9|owner: Jordan\n10|tags: [para/areas, <% await tp.system.suggester([\"software-dev\", \"administration\", \"church\", \"personal\", \"education\", \"finance\"], [\"software-dev\", \"administration\", \"church\", \"personal\", \"education\", \"finance\"], false, \"Primary Tag\") %>]\n```\n```3-Resources/PaceySpace Wiki/2-Areas/Legal/Templates/Client_Presentation_Template.md\n63|### 🔧 **Key Features**\n64|\n65|**For Your Customers:**\n66|- ✅ Beautiful, responsive [CONTENT] gallery that works on all devices\n67|- ✅ Advanced filtering ([FILTER OPTIONS])\n68|- ✅ Detailed [CONTENT] profiles with rich information\n69|- ✅ Easy contact forms for inquiries\n70|- ✅ Fast loading times (under 3 seconds)\n71|\n72|**For You (Admin Interface):**\n73|- ✅ Easy [CONTENT] upload with drag-and-drop\n74|- ✅ Rich metadata management for each [CONTENT ITEM]\n75|- ✅ User-friendly content management\n76|- ✅ Inquiry management and tracking\n77|- ✅ Analytics and performance reports\n78|\n79|---\n```\n```Tasks.md\n1|---\n2|# CORE METADATA\n3|creation_date: 2025-06-08\n4|modification_date: 2025-07-14\n5|type: index\n6|status: active\n7|priority: high\n8|area_category: Administration\n9|owner: Jordan\n10|aliases: [Tasks, Todo]\n11|tags: [tasks, index, productivity]\n12|\n13|# RELATIONSHIPS (Enhanced System)\n14|related_areas: [\"[[Task Management]]\", \"[[Administration]]\"]\n15|related_resources: [\"[[Task Management System]]\", \"[[Productivity Tools]]\"]\n16|related_people: [\"[[Jordan]]\"]\n17|---\n18|\n19|# 🎯 Task Management Dashboard\n20|\n21|> **Central command for all tasks across the vault** - Prioritized, contextualized, and actionable.\n```\n```Templates/Enhanced Resource.md\n1|---\n2|# CORE METADATA\n3|creation_date: <% tp.date.now(\"YYYY-MM-DD\") %>\n4|modification_date: <% tp.date.now(\"YYYY-MM-DD\") %>\n5|type: resource\n6|status: <% await tp.system.suggester([\"active\", \"archived\", \"outdated\"], [\"active\", \"archived\", \"outdated\"], false, \"Resource Status\") %>\n7|priority: <% await tp.system.suggester([\"critical\", \"high\", \"medium\", \"low\"], [\"critical\", \"high\", \"medium\", \"low\"], false, \"Resource Priority\") %>\n8|area_category: <% await tp.system.suggester([\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], [\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], false, \"Area Category\") %>\n9|owner: Jordan\n10|tags: [para/resources, <% await tp.system.suggester([\"guide\", \"reference\", \"tool\", \"tutorial\", \"cheatsheet\"], [\"guide\", \"reference\", \"tool\", \"tutorial\", \"cheatsheet\"], false, \"Resource Type\") %>]\n```\n```3-Resources/Vault Organization System Guide.md\n211|## 🚀 Quick Reference\n212|\n213|### Creating New Notes\n214|- Use **Enhanced templates** for consistent metadata\n215|- **Set relationships immediately** during creation\n216|- **Choose appropriate priority** and context\n217|- **Link to existing notes** where relevant\n218|\n219|### Updating Existing Notes\n220|- **Migrate metadata** to new schema gradually\n221|- **Convert 'related' field** to typed relationships\n222|- **Add task management fields** where applicable\n223|- **Update dataview queries** to use new fields\n224|\n225|### Best Practices\n226|- **Be specific** with relationships - quality over quantity\n227|- **Update modification_date** when making significant changes\n228|- **Use consistent naming** for areas and projects\n229|- **Regular maintenance** prevents system decay\n230|\n231|---\n232|\n233|**Tags**: #vault-organization #metadata #relationships #task-management #productivity #obsidian #knowledge-management\n234|\n235|---\n```\n```0.2-Tasks/Task Linking Guide.md\n1|---\n2|# CORE METADATA\n3|creation_date: 2025-07-14\n4|modification_date: 2025-07-14\n5|type: guide\n6|status: active\n7|priority: high\n8|area_category: Administration\n9|owner: Jordan\n10|tags: [tasks, linking, guide, connectivity, vault-organization]\n11|\n12|# RELATIONSHIPS (Enhanced System)\n13|related_areas: [\"[[Task Management]]\", \"[[Administration]]\"]\n14|related_resources: [\"[[Vault Organization System Guide]]\", \"[[Task Management System]]\"]\n15|related_people: [\"[[Jordan]]\"]\n16|\n17|# TASK MANAGEMENT\n18|task_priority: high\n19|task_context: admin\n20|---\n21|\n22|# 🔗 Task Linking Guide - Vault Connectivity\n23|\n24|> **Complete guide for linking tasks to vault resources** - Create a highly connected knowledge web.\n25|\n26|---\n```\n```3-Resources/PaceySpace Wiki/Page-Templates/Enhance Metadata.md\n28|{ name: \"review_frequency\", prompt: \"Review Frequency (daily, weekly, monthly, quarterly)\", default: \"monthly\" },\n29|    { name: \"organization\", prompt: \"Organization (if applicable)\", default: \"\" },\n30|    { name: \"key_contacts\", prompt: \"Key Contacts (comma-separated)\", default: \"\" },\n31|    { name: \"last_review_date\", prompt: \"Last Review Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n32|    { name: \"next_review_date\", prompt: \"Next Review Date\", default: tp.date.now(\"YYYY-MM-DD\", 30) }\n33|  ];\n34|} else if (currentType === \"resource\") {\n35|  metadataFields = [\n36|    { name: \"source\", prompt: \"Source\", default: \"Personal research\" },\n37|    { name: \"area\", prompt: \"Related Area\", default: \"Software-Development\" },\n38|    { name: \"difficulty\", prompt: \"Difficulty (easy, medium, hard)\", default: \"medium\" },\n39|    { name: \"keywords\", prompt: \"Keywords (comma-separated)\", default: \"guide, reference\" },\n40|    { name: \"last_used\", prompt: \"Last Used Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n```\n```Templates/Enhance Metadata.md\n28|{ name: \"review_frequency\", prompt: \"Review Frequency (daily, weekly, monthly, quarterly)\", default: \"monthly\" },\n29|    { name: \"organization\", prompt: \"Organization (if applicable)\", default: \"\" },\n30|    { name: \"key_contacts\", prompt: \"Key Contacts (comma-separated)\", default: \"\" },\n31|    { name: \"last_review_date\", prompt: \"Last Review Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n32|    { name: \"next_review_date\", prompt: \"Next Review Date\", default: tp.date.now(\"YYYY-MM-DD\", 30) }\n33|  ];\n34|} else if (currentType === \"resource\") {\n35|  metadataFields = [\n36|    { name: \"source\", prompt: \"Source\", default: \"Personal research\" },\n37|    { name: \"area\", prompt: \"Related Area\", default: \"Software-Development\" },\n38|    { name: \"difficulty\", prompt: \"Difficulty (easy, medium, hard)\", default: \"medium\" },\n39|    { name: \"keywords\", prompt: \"Keywords (comma-separated)\", default: \"guide, reference\" },\n40|    { name: \"last_used\", prompt: \"Last Used Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n```\n```Templates/Enhanced Project.md\n1|---\n2|# CORE METADATA\n3|creation_date: <% tp.date.now(\"YYYY-MM-DD\") %>\n4|modification_date: <% tp.date.now(\"YYYY-MM-DD\") %>\n5|type: project\n6|status: <% await tp.system.suggester([\"active\", \"on-hold\", \"completed\", \"cancelled\"], [\"active\", \"on-hold\", \"completed\", \"cancelled\"], false, \"Project Status\") %>\n7|priority: <% await tp.system.suggester([\"critical\", \"high\", \"medium\", \"low\"], [\"critical\", \"high\", \"medium\", \"low\"], false, \"Project Priority\") %>\n8|area_category: <% await tp.system.suggester([\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], [\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], false, \"Area Category\") %>\n9|owner: Jordan\n10|tags: [para/projects, <% await tp.system.suggester([\"software-dev\", \"church\", \"personal\", \"university\", \"admin\", \"finance\"], [\"software-dev\", \"church\", \"personal\", \"university\", \"admin\", \"finance\"], false, \"Primary Tag\") %>]\n```\n```0.2-Tasks/Tasks Dashboard.md\n1|---\n2|# CORE METADATA\n3|creation_date: 2025-08-07\n4|modification_date: 2025-08-07\n5|type: task-dashboard\n6|status: active\n7|priority: critical\n8|area_category: Administration\n9|owner: Jordan\n10|tags: [tasks, dashboard, productivity, central-hub]\n11|\n12|# RELATIONSHIPS (Enhanced System)\n13|related_areas: [\"[[2-Areas/Task Management]]\", \"[[2-Areas/Website-Maintenance]]\", \"[[2-Areas/Church Administration]]\"]\n14|related_resources: [\"[[3-Resources/Vault Organization Guide]]\", \"[[3-Resources/Software Development Resources]]\"]\n15|related_people: [\"[[Jordan]]\"]\n16|\n17|# TASK MANAGEMENT\n18|task_priority: critical\n19|task_context: admin\n20|---\n21|\n22|# 🎯 Tasks Dashboard - Central Command\n23|\n24|> **The central hub for all task management across the vault** - Your single source of truth for what needs to be done.\n```\n```Templates/Enhanced Resource V2.md\n100|## Overview\n101|<!-- Brief description of this resource -->\n102|\n103|## Key Points\n104|<!-- Main takeaways or important information -->\n105|- \n106|\n107|## Details\n108|<!-- Detailed information -->\n```\n```Templates/Enhance Metadata.md\n39|{ name: \"keywords\", prompt: \"Keywords (comma-separated)\", default: \"guide, reference\" },\n40|    { name: \"last_used\", prompt: \"Last Used Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n41|    { name: \"url\", prompt: \"URL (if applicable)\", default: \"\" },\n42|    { name: \"author\", prompt: \"Author (if applicable)\", default: \"\" }\n43|  ];\n44|} else if (currentType === \"person\") {\n45|  metadataFields = [\n46|    { name: \"first_name\", prompt: \"First Name\", default: \"\" },\n47|    { name: \"last_name\", prompt: \"Last Name\", default: \"\" },\n48|    { name: \"organization\", prompt: \"Organization\", default: \"\" },\n49|    { name: \"role\", prompt: \"Role\", default: \"\" },\n50|    { name: \"email\", prompt: \"Email\", default: \"\" },\n51|    { name: \"phone\", prompt: \"Phone\", default: \"\" },\n52|    { name: \"relationship\", prompt: \"Relationship (colleague, client, friend, family, service provider)\", default: \"\" }\n53|  ];\n54|} else if (currentType === \"meeting\") {\n55|  metadataFields = [\n56|    { name: \"time\", prompt: \"Meeting Time\", default: tp.date.now(\"HH:mm\") },\n```\n```3-Resources/PaceySpace Wiki/Page-Templates/Enhance Metadata.md\n39|{ name: \"keywords\", prompt: \"Keywords (comma-separated)\", default: \"guide, reference\" },\n40|    { name: \"last_used\", prompt: \"Last Used Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n41|    { name: \"url\", prompt: \"URL (if applicable)\", default: \"\" },\n42|    { name: \"author\", prompt: \"Author (if applicable)\", default: \"\" }\n43|  ];\n44|} else if (currentType === \"person\") {\n45|  metadataFields = [\n46|    { name: \"first_name\", prompt: \"First Name\", default: \"\" },\n47|    { name: \"last_name\", prompt: \"Last Name\", default: \"\" },\n48|    { name: \"organization\", prompt: \"Organization\", default: \"\" },\n49|    { name: \"role\", prompt: \"Role\", default: \"\" },\n50|    { name: \"email\", prompt: \"Email\", default: \"\" },\n51|    { name: \"phone\", prompt: \"Phone\", default: \"\" },\n52|    { name: \"relationship\", prompt: \"Relationship (colleague, client, friend, family, service provider)\", default: \"\" }\n53|  ];\n54|} else if (currentType === \"meeting\") {\n55|  metadataFields = [\n56|    { name: \"time\", prompt: \"Meeting Time\", default: tp.date.now(\"HH:mm\") },\n```\n```0.2-Tasks/Tasks TOC.md\n103|## 📚 Documentation & Guides\n104|\n105|### User Guides\n106|- **[[0.2-Tasks/Guides/Getting Started|🚀 Getting Started]]** - New user introduction\n107|- **[[0.2-Tasks/Guides/Best Practices|⭐ Best Practices]]** - Effective task management tips\n108|- **[[0.2-Tasks/Guides/Troubleshooting|🔧 Troubleshooting]]** - Common issues and solutions\n109|\n110|### Reference\n111|- **[[0.2-Tasks/Reference/Task Schema|📋 Task Schema]]** - Metadata field definitions\n112|- **[[0.2-Tasks/Reference/Query Library|🔍 Query Library]]** - Useful dataview queries\n113|- **[[0.2-Tasks/Reference/Keyboard Shortcuts|⌨️ Keyboard Shortcuts]]** - Quick actions and hotkeys\n114|\n115|---\n```\n\n\n\ngenerate metadata for this about us message so that i can filter it effectively in views and categorise\n\n"}], "id": "0f3c9a2d-dc2e-4392-aed0-76982c9f50aa", "mentionables": [{"type": "current-file", "file": "2-Areas/PaceySpace About Us Message for advertisement and External networking.md"}], "similaritySearchResults": [{"id": 9614, "path": "2-Areas/Task Management/Todoist/CRUCA Todoist Tasks to 21-04-2025.md", "mtime": 1747611992204, "content": "| TYPE | CONTENT                                                                                                                                                                                                                                                                                                                             | DESCRIPTION", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 3, "startLine": 3}, "similarity": 0.5933717495512585}, {"id": 12949, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "## Markdown Documentation Generator from provided Agent Actions (Obsidian Markdown Format wit richer Metadata for search and dataview)\n```", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 20, "startLine": 19}, "similarity": 0.5862080763130472}, {"id": 13149, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "## Markdown Documentation Generator from provided Agent Actions (Obsidian Markdown Format wit richer Metadata for search and dataview)\n```", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 20, "startLine": 19}, "similarity": 0.5862080763130472}, {"id": 13249, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "## Markdown Documentation Generator from provided Agent Actions (Obsidian Markdown Format wit richer Metadata for search and dataview)\n```", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 20, "startLine": 19}, "similarity": 0.5862080763130472}, {"id": 13049, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "## Markdown Documentation Generator from provided Agent Actions (Obsidian Markdown Format wit richer Metadata for search and dataview)\n```", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 20, "startLine": 19}, "similarity": 0.5862080763130472}, {"id": 11192, "path": "1-Projects/Yendorcats/Project Development Journal and Notes.md", "mtime": 1754037467000, "content": "### Search and Filtering\n\n  \n\nThe system provides advanced search and filtering capabilities based on metadata:\n\n  \n\n- Filter by category (studs, queens, kittens, gallery)\n\n- Filter by gender (M/F)\n\n- Filter by breed\n\n- Filter by bloodline\n\n- Filter by hair color\n\n- Filter by age range\n\n- Filter by tags\n\n- Sort by date, name, or age\n\n- Sort in ascending or descending order", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 1042, "startLine": 1018}, "similarity": 0.5853770462548256}, {"id": 8953, "path": "3-Resources/PaceySpace Wiki/Page-Templates/README.md", "mtime": 1748968575372, "content": "---\ncreation_date: 2025-04-16\nmodification_date: 2025-04-16\ntype: resource\nsource: Internal documentation\ntags: [para/resources, templates, guide, obsidian, metadata]\n---\n\n# Enhanced Templates Guide\n\nThis guide explains how to use the enhanced templates and scripts to create powerful, interconnected notes with rich metadata.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 11, "startLine": 1}, "similarity": 0.5824376240000961}, {"id": 7648, "path": "Templates/README.md", "mtime": 1747611992224, "content": "---\ncreation_date: 2025-04-16\nmodification_date: 2025-04-16\ntype: resource\nsource: Internal documentation\ntags: [para/resources, templates, guide, obsidian, metadata]\n---\n\n# Enhanced Templates Guide\n\nThis guide explains how to use the enhanced templates and scripts to create powerful, interconnected notes with rich metadata.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 11, "startLine": 1}, "similarity": 0.5824376240000961}, {"id": 13150, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. Refer to the AGENTS.md file for further guidelines. This obsidian markdown wiki will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  ```csharp code blocks ```) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) Pay very close attention to adding appropriate markdown document metadata for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 21}, "similarity": 0.5766314381172069}, {"id": 13050, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. Refer to the AGENTS.md file for further guidelines. This obsidian markdown wiki will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  ```csharp code blocks ```) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) Pay very close attention to adding appropriate markdown document metadata for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 21}, "similarity": 0.5766314381172069}, {"id": 12950, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. Refer to the AGENTS.md file for further guidelines. This obsidian markdown wiki will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  ```csharp code blocks ```) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) Pay very close attention to adding appropriate markdown document metadata for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 21}, "similarity": 0.5766314381172069}, {"id": 13250, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. Refer to the AGENTS.md file for further guidelines. This obsidian markdown wiki will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  ```csharp code blocks ```) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) Pay very close attention to adding appropriate markdown document metadata for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 21}, "similarity": 0.5766314381172069}, {"id": 8543, "path": "3-Resources/Software Guides/Second Brain Notetaking.md", "mtime": 1747611992217, "content": "I need assistance with cleaning out my obsidian vault. use obsidian features. I want to make use of obsidians graph (with jugl global graph plugin) and dataview plugin. I primarily search my notes and make queries rather than following a folder structure, finding folders is a waste of time for me. filters are great. i need you to add plenty of metadata for queries and searches like everyone else does for using these plugins. I have a backup of this vault so we can to some cleaning. go ahead and make changes, we may have to prompt several times to get through everything. I have a rough template file in the Template/ folder, however you may nave a better template than i do. Just make sure the metadata can be automated (like date for example, could be set to todays date) and other examples. You can even see that the creations date of google keep notes are preserved and date back many years ago. Can you please add the note files date metadata to the obsidian notes property field too?", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 12, "startLine": 12}, "similarity": 0.570749503820848}, {"id": 13251, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it can be easily indexed and searched. Searching will be the method of information retrieval within obsidian (omnisearch) aswell as notion and fuzzyfinder searches in neovim and programs like cling (macos).  make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. cmaintain lear, neat anf efficient and formatting so that the documents/responses you generate provide easy and useful information retrieval. Keep this resource material concise and useful. I will only copy the reference material, not conversational material.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 21}, "similarity": 0.5642455160282879}, {"id": 13051, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it can be easily indexed and searched. Searching will be the method of information retrieval within obsidian (omnisearch) aswell as notion and fuzzyfinder searches in neovim and programs like cling (macos).  make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. cmaintain lear, neat anf efficient and formatting so that the documents/responses you generate provide easy and useful information retrieval. Keep this resource material concise and useful. I will only copy the reference material, not conversational material.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 21}, "similarity": 0.5642455160282879}, {"id": 12951, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it can be easily indexed and searched. Searching will be the method of information retrieval within obsidian (omnisearch) aswell as notion and fuzzyfinder searches in neovim and programs like cling (macos).  make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. cmaintain lear, neat anf efficient and formatting so that the documents/responses you generate provide easy and useful information retrieval. Keep this resource material concise and useful. I will only copy the reference material, not conversational material.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 21}, "similarity": 0.5642455160282879}, {"id": 13151, "path": "3-Resources/Prompt Engineering/3System Prompt Templates.md", "mtime": 1757269883096, "content": "for a PARA structure document database. It is very important that you add markdown document metadata to every documentaiton output you generate along with the information you provide, so that it can be easily indexed and searched. Searching will be the method of information retrieval within obsidian (omnisearch) aswell as notion and fuzzyfinder searches in neovim and programs like cling (macos).  make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. cmaintain lear, neat anf efficient and formatting so that the documents/responses you generate provide easy and useful information retrieval. Keep this resource material concise and useful. I will only copy the reference material, not conversational material.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 21}, "similarity": 0.5642455160282879}, {"id": 11318, "path": "0.2-Tasks/Tasks TOC.md", "mtime": 1754576545824, "content": "---\n# CORE METADATA\ncreation_date: 2025-08-07\nmodification_date: 2025-08-07\ntype: index\nstatus: active\npriority: high\narea_category: Administration\nowner: Jordan\ntags: [tasks, index, toc, navigation]\n\n# RELATIONSHIPS (Enhanced System)\nrelated_areas: [\"[[2-Areas/Task Management]]\", \"[[2-Areas/Website-Maintenance]]\"]\nrelated_resources: [\"[[3-Resources/Vault Organization Guide]]\", \"[[3-Resources/Software Development Resources]]\"]\nrelated_people: [\"[[Jordan]]\"]\n\n# TASK MANAGEMENT\ntask_priority: high\ntask_context: admin\n---\n\n# 📋 Tasks - Table of Contents\n\n> **Central navigation hub for the task management system** - Your guide to all task-related resources and workflows.\n\n---", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 26, "startLine": 1}, "similarity": 0.5621988344848231}, {"id": 11414, "path": "0.2-Tasks/Daily Task Sync.md", "mtime": 1752454218361, "content": "### Task File Naming Convention\n```\nYYYY-MM-DD-TaskNumber-SourceNote.md\nExample: 2025-07-14-001-2025-06-25.md\n```\n\n### Automated Metadata Generation\n```yaml\n---\n# CORE METADATA\ncreation_date: 2025-07-14\nmodification_date: 2025-07-14\ntype: task\nstatus: not-started\npriority: high\narea_category: [Auto-detected from tags]\nowner: Jordan\ntags: [task, daily-carryover, [context-tags]]\n\n# TASK MANAGEMENT\ntask_priority: [Extracted from daily note]\ntask_context: [Extracted from hashtags]\nestimated_time: [Auto-estimated based on content]\nenergy_required: [Auto-assigned based on context]\ndue_date: [Extracted if present]\n\n# RELATIONSHIPS (Enhanced System)\nrelated_projects: [[\"[[Auto-detected from tags]]\"]]\nrelated_areas: [[\"[[Auto-detected from context]]\"]]\nsource_note: \"[[Original Daily Note]]\"\nsource_date: 2025-06-25\ntask_number: 001\n\n# TASK LIFECYCLE\ncreated_date: 2025-07-14\nmigrated_from: \"0-Daily Notes/2025-06-25.md\"\noriginal_task_line: 30\n---\n```\n\n---", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 191, "startLine": 151}, "similarity": 0.559176296966877}, {"id": 7587, "path": "Scripts/Vault Maintenance Scripts.md", "mtime": 1752453035768, "content": "## 🔧 Standardize Metada<PERSON>", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 28, "startLine": 28}, "similarity": 0.5587182587997801}, {"id": 9602, "path": "2-Areas/Task Management/Tasks.md", "mtime": 1752453355454, "content": "---\n# CORE METADATA\ncreation_date: 2025-06-08\nmodification_date: 2025-07-14\ntype: index\nstatus: active\npriority: high\narea_category: Administration\nowner: Jordan\naliases: [Tasks, Todo]\ntags: [tasks, index, productivity, para/areas, task-management]\n\n# RELATIONSHIPS (Enhanced System)\nrelated_areas: [\"[[Task Management]]\", \"[[Administration]]\"]\nrelated_resources: [\"[[Task Management System]]\", \"[[Productivity Tools]]\"]\nrelated_people: [\"[[Jordan]]\"]\narea: Task Management\n---\n\n# 🎯 Task Management Dashboard\n\n> **Central command for all tasks across the vault** - Prioritized, contextualized, and actionable.\n\n## 🔥 URGENT - Do First\n```dataview\nTABLE WITHOUT ID\n  \"🔥\" as \"\",\n  file.link as \"URGENT TASKS\",\n  choice(estimated_time, estimated_time, \"?\") as \"Time\",\n  choice(due, due, \"ASAP\") as \"Due\"\nFROM \"\"\nWHERE !completed\n  AND task_priority = \"urgent\"\n  AND (due <= date(today) OR !due)\nSORT file.path ASC\nLIMIT 8\n```", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 37, "startLine": 1}, "similarity": 0.5584681672796582}, {"id": 8142, "path": "3-Resources/Vault Organization System Guide.md", "mtime": 1752452722348, "content": "---\n# CORE METADATA\ncreation_date: 2025-07-14\nmodification_date: 2025-07-14\ntype: resource\nstatus: active\npriority: critical\narea_category: Administration\nowner: Jordan\ntags: [para/resources, guide, obsidian, vault-organization, metadata, productivity]\n\n# RESOURCE CLASSIFICATION\nsource: Personal research\ndifficulty: intermediate\nresource_type: guide\nurl: \"\"\nlast_verified: 2025-07-14\n\n# RELATIONSHIPS (Enhanced System)\nrelated_projects: [\"[[Vault Restructuring Project]]\"]\nrelated_areas: [\"[[Task Management]]\", \"[[Administration]]\"]\nrelated_people: [\"[[Jordan]]\"]\nreferences: [\"[[Dataview Documentation]]\", \"[[Obsidian Best Practices]]\"]\ninspired_by: [\"[[PARA Method]]\", \"[[Getting Things Done]]\"]\n---\n\n# 🏗️ Vault Organization System Guide\n\n## Overview\nThis guide documents the enhanced metadata schema, relationship system, and task management approach implemented to create a highly connected, efficient knowledge management system in Obsidian.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 30, "startLine": 1}, "similarity": 0.5577306248787203}, {"id": 11182, "path": "1-Projects/Yendorcats/Project Development Journal and Notes.md", "mtime": 1754037467000, "content": "## S3 Metadata Implementation\n\n  \n\nThe project uses Backblaze B2 S3-compatible storage with rich metadata for cat gallery images. The metadata includes:\n\n  \n\n### Required Fields\n\n- Name of the cat\n\n- Gender (M/F)\n\n- Date uploaded (set automatically)\n\n- File format\n\n- Content type\n\n  \n\n### Optional User-Inputted Fields\n\n- Description\n\n- Age\n\n- Bloodline\n\n- Breed\n\n- Hair color\n\n- Date taken (photo)\n\n- Personality\n\n- Category\n\n- Tags (for filtering and search)\n\n- Mother/Father information\n\n  \n\n### System-Generated Fields\n\n- File size\n\n- Image dimensions\n\n- Upload IP and user agent\n\n- Upload session ID\n\n  \n\nThis rich metadata enables advanced filtering and search capabilities, allowing users to find cats based on various criteria.\n\n  \n\nFor detailed information about the metadata implementation, see the [S3 Metadata Implementation Guide](1-Projects/Yendorcats/YendorCats%20S3%20Metadata%20Implementation.md).", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 690, "startLine": 628}, "similarity": 0.55757669246553}, {"id": 7661, "path": "Templates/Enhanced Task.md", "mtime": 1752452553563, "content": "---\n# CORE METADATA\ncreation_date: <% tp.date.now(\"YYYY-MM-DD\") %>\nmodification_date: <% tp.date.now(\"YYYY-MM-DD\") %>\ntype: task\nstatus: not-started\npriority: <% await tp.system.suggester([\"critical\", \"high\", \"medium\", \"low\"], [\"critical\", \"high\", \"medium\", \"low\"], false, \"Task Priority\") %>\narea_category: <% await tp.system.suggester([\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], [\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], false, \"Area Category\") %>\nowner: Jordan\ntags: [task, <% await tp.system.suggester([\"urgent\", \"project\", \"admin\", \"maintenance\", \"creative\"], [\"urgent\", \"project\", \"admin\", \"maintenance\", \"creative\"], false, \"Primary Tag\") %>]", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 10, "startLine": 1}, "similarity": 0.5552761686506762}, {"id": 8120, "path": "3-Resources/YendorCats S3 Metadata Implementation.md", "mtime": 1752452658838, "content": "---\n# CORE METADATA\ncreation_date: 2025-04-27\nmodification_date: 2025-07-14\ntype: resource\nstatus: active\npriority: high\narea_category: Software-Development\nowner: Jordan\ntags: [para/resources, guide, software-dev, s3, metadata, backblaze]\n\n# RESOURCE CLASSIFICATION\nsource: Project documentation\ndifficulty: intermediate\nresource_type: guide\nurl: \"\"\nlast_verified: 2025-07-14\n\n# RELATIONSHIPS (Enhanced System)\nrelated_projects: [\"[[YendorCats Project Documentation]]\"]\nrelated_areas: [\"[[Software-Development]]\"]\nrelated_people: [\"[[Jordan]]\"]\nreferences: [\"[[AWS S3 Documentation]]\", \"[[.NET 8 Documentation]]\"]\ninspired_by: [\"[[S3 Best Practices Guide]]\"]\n---\n\n# YendorCats S3 Metadata Implementation", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 27, "startLine": 1}, "similarity": 0.5547995001910501}, {"id": 7844, "path": "Family/split tunelling with mullvad.md", "mtime": 1747611992222, "content": "---\ncreation_date: 2025-04-05\nmodification_date: 2025-04-05\ntype: note\naliases: []\ntags: []\narea: \nproject: \nresource: \narchive: \nstatus: active\npriority: \nlinks: []\nrelated: []\n---\n\n# Untitled\n\n## Content\n\n\n## References\n\n\n## Tasks\n- [ ] \n\n## Metadata\n- **Original Creation**: 2025-04-05\n- **Source**: \n- **Context**:", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 31, "startLine": 1}, "similarity": 0.5536406423859669}, {"id": 11190, "path": "1-Projects/Yendorcats/Project Development Journal and Notes.md", "mtime": 1754037467000, "content": "### Metadata Management\n\n  \n\nThe system supports two methods for managing cat image metadata:\n\n  \n\n1. **S3 Object Metadata (Primary Method)**:\n\n   - All metadata is stored as S3 object metadata\n\n   - Metadata is retrieved directly from S3 when displaying images\n\n   - Supports rich metadata with many fields\n\n   - Enables advanced filtering and search capabilities\n\n  \n\n2. **Filename Parsing (Fallback Method)**:\n\n   - For backward compatibility with existing images\n\n   - Extracts basic metadata from filenames\n\n   - Filename format: `[cat's name]-[age]-[date(DDMMYY)]-[order].jpg`\n\n   - Example: `Georgia-2.6-230325-1.jpg` (Name: Georgia, Age: 2.6 years, Date: March 23, 2025, Order: 1)", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 966, "startLine": 938}, "similarity": 0.5536331433326134}, {"id": 8957, "path": "3-Resources/PaceySpace Wiki/Page-Templates/README.md", "mtime": 1748968575372, "content": "## Utility Scripts\n\n### Template Selector\n- **File**: `Template Selector.md`\n- **Purpose**: Helps you select the appropriate template based on what you're trying to create\n- **Usage**: Run this script when you want to create a new note and it will guide you through the process\n\n### Create Related Note\n- **File**: `Create Related Note.md`\n- **Purpose**: Creates a new note that's automatically linked to the current note\n- **Usage**: Run this script from any note to create a related note that will be automatically linked in both directions\n\n### Enhance Metadata\n- **File**: `Enhance Metadata.md`\n- **Purpose**: Helps you enhance the metadata of existing notes\n- **Usage**: Run this script from any note to add or update metadata fields based on the note type", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 95, "startLine": 80}, "similarity": 0.552091193697746}, {"id": 7652, "path": "Templates/README.md", "mtime": 1747611992224, "content": "## Utility Scripts\n\n### Template Selector\n- **File**: `Template Selector.md`\n- **Purpose**: Helps you select the appropriate template based on what you're trying to create\n- **Usage**: Run this script when you want to create a new note and it will guide you through the process\n\n### Create Related Note\n- **File**: `Create Related Note.md`\n- **Purpose**: Creates a new note that's automatically linked to the current note\n- **Usage**: Run this script from any note to create a related note that will be automatically linked in both directions\n\n### Enhance Metadata\n- **File**: `Enhance Metadata.md`\n- **Purpose**: Helps you enhance the metadata of existing notes\n- **Usage**: Run this script from any note to add or update metadata fields based on the note type", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 95, "startLine": 80}, "similarity": 0.552091193697746}, {"id": 8057, "path": "Family/Bridge network to access zero tier services from internet.md", "mtime": 1753689783808, "content": "## Content\n\n\n## References\n\n\n## Tasks\n- [ ] \n\n## Metadata\n- **Original Creation**: 2025-04-06\n- **Source**: \n- **Context**:", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 64, "startLine": 52}, "similarity": 0.5508001153469861}, {"id": 8894, "path": "3-Resources/Software Guides/Flaresolverr and Prowlarr.md", "mtime": 1747611992216, "content": "## Content\n\n\n## References\n\n\n## Tasks\n- [ ] \n\n## Metadata\n- **Original Creation**: 2025-04-06\n- **Source**: \n- **Context**:", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 259, "startLine": 247}, "similarity": 0.5508001153469861}, {"id": 8540, "path": "3-Resources/Software Guides/Unix Filesystem guides.md", "mtime": 1747611992217, "content": "## Content\n\n\n## References\n\n\n## Tasks\n- [ ] \n\n## Metadata\n- **Original Creation**: 2025-04-07\n- **Source**: \n- **Context**:", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 57, "startLine": 45}, "similarity": 0.5494369314296571}, {"id": 7651, "path": "Templates/README.md", "mtime": 1747611992224, "content": "### Meeting Template\n- **File**: `Meeting.md`\n- **Purpose**: Create detailed meeting notes that link to related projects and areas\n- **Key Features**:\n  - Rich metadata including participants, location, project, area\n  - Action items with assignees and due dates\n  - Follow-up section\n  - Related projects and areas views\n\n### Person Template\n- **File**: `Person.md`\n- **Purpose**: Create detailed contact notes with comprehensive metadata\n- **Key Features**:\n  - Rich metadata including organization, role, relationship\n  - Interactions log\n  - Related projects and areas views\n  - Meetings view\n  - Quick links to create related notes", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 78, "startLine": 61}, "similarity": 0.5489075768596461}, {"id": 8956, "path": "3-Resources/PaceySpace Wiki/Page-Templates/README.md", "mtime": 1748968575372, "content": "### Meeting Template\n- **File**: `Meeting.md`\n- **Purpose**: Create detailed meeting notes that link to related projects and areas\n- **Key Features**:\n  - Rich metadata including participants, location, project, area\n  - Action items with assignees and due dates\n  - Follow-up section\n  - Related projects and areas views\n\n### Person Template\n- **File**: `Person.md`\n- **Purpose**: Create detailed contact notes with comprehensive metadata\n- **Key Features**:\n  - Rich metadata including organization, role, relationship\n  - Interactions log\n  - Related projects and areas views\n  - Meetings view\n  - Quick links to create related notes", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 78, "startLine": 61}, "similarity": 0.5489075768596461}, {"id": 8537, "path": "3-Resources/Software Guides/VPN.md", "mtime": 1747611992217, "content": "## Content\n\n\n## References\n\n\n## Tasks\n- [ ] \n\n## Metadata\n- **Original Creation**: 2025-04-05\n- **Source**: \n- **Context**:", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 38, "startLine": 26}, "similarity": 0.5483251204618449}, {"id": 8262, "path": "3-Resources/Prompt Engineering/Prompt Rules for Technical Markdown Documentation  Documents.md", "mtime": 1751767974295, "content": "Your corresponding answers for the information and resources you provide must be outputted to me in markdown format for an obsidian wiki. These will serve as my documentation for the project and help me learn coding easier as an inexperienced coder, by saving my time in editing the ouput format. mitigate newline issues and ensure good use of markdown formatting is used (such as  `csharp code blocks using 3 backticks and csharp in header of block`) markdown tables and add horizontal lines (---) at the beginning and end of the resource you provide me to work on and reference. This is so i can segregate the information and see clearly defined sections (make sure to use #, ##, ### .... titles.) I make extensive use of tagging in order to find information i need from my vault (for example: #s3, #storage, #functionName, optimisations etc.) whenever there are keywords. I want it to have neat and pretty format for clear and satisfying viewing, and efficient and easy information retrieval.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 37, "startLine": 37}, "similarity": 0.5482653552050678}, {"id": 7762, "path": "Templates/Enhanced Area.md", "mtime": 1752452415856, "content": "---\n# CORE METADATA\ncreation_date: <% tp.date.now(\"YYYY-MM-DD\") %>\nmodification_date: <% tp.date.now(\"YYYY-MM-DD\") %>\ntype: area\nstatus: <% await tp.system.suggester([\"active\", \"inactive\", \"archived\"], [\"active\", \"inactive\", \"archived\"], false, \"Area Status\") %>\npriority: <% await tp.system.suggester([\"critical\", \"high\", \"medium\", \"low\"], [\"critical\", \"high\", \"medium\", \"low\"], false, \"Area Priority\") %>\narea_category: <% await tp.system.suggester([\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], [\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], false, \"Area Category\") %>\nowner: Jordan\ntags: [para/areas, <% await tp.system.suggester([\"software-dev\", \"administration\", \"church\", \"personal\", \"education\", \"finance\"], [\"software-dev\", \"administration\", \"church\", \"personal\", \"education\", \"finance\"], false, \"Primary Tag\") %>]", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 10, "startLine": 1}, "similarity": 0.5481153081896173}, {"id": 9188, "path": "3-Resources/PaceySpace Wiki/2-Areas/Legal/Templates/Client_Presentation_Template.md", "mtime": 1748996887471, "content": "### 🔧 **Key Features**\n\n**For Your Customers:**\n- ✅ Beautiful, responsive [CONTENT] gallery that works on all devices\n- ✅ Advanced filtering ([FILTER OPTIONS])\n- ✅ Detailed [CONTENT] profiles with rich information\n- ✅ Easy contact forms for inquiries\n- ✅ Fast loading times (under 3 seconds)\n\n**For You (Admin Interface):**\n- ✅ Easy [CONTENT] upload with drag-and-drop\n- ✅ Rich metadata management for each [CONTENT ITEM]\n- ✅ User-friendly content management\n- ✅ Inquiry management and tracking\n- ✅ Analytics and performance reports\n\n---", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 79, "startLine": 63}, "similarity": 0.5464668873290967}, {"id": 7577, "path": "Tasks.md", "mtime": 1752454514237, "content": "---\n# CORE METADATA\ncreation_date: 2025-06-08\nmodification_date: 2025-07-14\ntype: index\nstatus: active\npriority: high\narea_category: Administration\nowner: Jordan\naliases: [Tasks, Todo]\ntags: [tasks, index, productivity]\n\n# RELATIONSHIPS (Enhanced System)\nrelated_areas: [\"[[Task Management]]\", \"[[Administration]]\"]\nrelated_resources: [\"[[Task Management System]]\", \"[[Productivity Tools]]\"]\nrelated_people: [\"[[Jordan]]\"]\n---\n\n# 🎯 Task Management Dashboard\n\n> **Central command for all tasks across the vault** - Prioritized, contextualized, and actionable.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 21, "startLine": 1}, "similarity": 0.5463621241388923}, {"id": 7666, "path": "Templates/Enhanced Resource.md", "mtime": 1752452457020, "content": "---\n# CORE METADATA\ncreation_date: <% tp.date.now(\"YYYY-MM-DD\") %>\nmodification_date: <% tp.date.now(\"YYYY-MM-DD\") %>\ntype: resource\nstatus: <% await tp.system.suggester([\"active\", \"archived\", \"outdated\"], [\"active\", \"archived\", \"outdated\"], false, \"Resource Status\") %>\npriority: <% await tp.system.suggester([\"critical\", \"high\", \"medium\", \"low\"], [\"critical\", \"high\", \"medium\", \"low\"], false, \"Resource Priority\") %>\narea_category: <% await tp.system.suggester([\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], [\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], false, \"Area Category\") %>\nowner: Jordan\ntags: [para/resources, <% await tp.system.suggester([\"guide\", \"reference\", \"tool\", \"tutorial\", \"cheatsheet\"], [\"guide\", \"reference\", \"tool\", \"tutorial\", \"cheatsheet\"], false, \"Resource Type\") %>]", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 10, "startLine": 1}, "similarity": 0.546282898222019}, {"id": 8151, "path": "3-Resources/Vault Organization System Guide.md", "mtime": 1752452722348, "content": "## 🚀 Quick Reference\n\n### Creating New Notes\n- Use **Enhanced templates** for consistent metadata\n- **Set relationships immediately** during creation\n- **Choose appropriate priority** and context\n- **Link to existing notes** where relevant\n\n### Updating Existing Notes\n- **Migrate metadata** to new schema gradually\n- **Convert 'related' field** to typed relationships\n- **Add task management fields** where applicable\n- **Update dataview queries** to use new fields\n\n### Best Practices\n- **Be specific** with relationships - quality over quantity\n- **Update modification_date** when making significant changes\n- **Use consistent naming** for areas and projects\n- **Regular maintenance** prevents system decay\n\n---\n\n**Tags**: #vault-organization #metadata #relationships #task-management #productivity #obsidian #knowledge-management\n\n---", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 235, "startLine": 211}, "similarity": 0.5452848184231446}, {"id": 11378, "path": "0.2-Tasks/Task Linking Guide.md", "mtime": 1752454480369, "content": "---\n# CORE METADATA\ncreation_date: 2025-07-14\nmodification_date: 2025-07-14\ntype: guide\nstatus: active\npriority: high\narea_category: Administration\nowner: Jordan\ntags: [tasks, linking, guide, connectivity, vault-organization]\n\n# RELATIONSHIPS (Enhanced System)\nrelated_areas: [\"[[Task Management]]\", \"[[Administration]]\"]\nrelated_resources: [\"[[Vault Organization System Guide]]\", \"[[Task Management System]]\"]\nrelated_people: [\"[[Jordan]]\"]\n\n# TASK MANAGEMENT\ntask_priority: high\ntask_context: admin\n---\n\n# 🔗 Task Linking Guide - Vault Connectivity\n\n> **Complete guide for linking tasks to vault resources** - Create a highly connected knowledge web.\n\n---", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 26, "startLine": 1}, "similarity": 0.5447988076448494}, {"id": 8992, "path": "3-Resources/PaceySpace Wiki/Page-Templates/Enhance Metadata.md", "mtime": 1748968575368, "content": "{ name: \"review_frequency\", prompt: \"Review Frequency (daily, weekly, monthly, quarterly)\", default: \"monthly\" },\n    { name: \"organization\", prompt: \"Organization (if applicable)\", default: \"\" },\n    { name: \"key_contacts\", prompt: \"Key Contacts (comma-separated)\", default: \"\" },\n    { name: \"last_review_date\", prompt: \"Last Review Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n    { name: \"next_review_date\", prompt: \"Next Review Date\", default: tp.date.now(\"YYYY-MM-DD\", 30) }\n  ];\n} else if (currentType === \"resource\") {\n  metadataFields = [\n    { name: \"source\", prompt: \"Source\", default: \"Personal research\" },\n    { name: \"area\", prompt: \"Related Area\", default: \"Software-Development\" },\n    { name: \"difficulty\", prompt: \"Difficulty (easy, medium, hard)\", default: \"medium\" },\n    { name: \"keywords\", prompt: \"Keywords (comma-separated)\", default: \"guide, reference\" },\n    { name: \"last_used\", prompt: \"Last Used Date\", default: tp.date.now(\"YYYY-MM-DD\") },", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 40, "startLine": 28}, "similarity": 0.5443378690567169}, {"id": 7772, "path": "Templates/Enhance <PERSON>.md", "mtime": 1747611992224, "content": "{ name: \"review_frequency\", prompt: \"Review Frequency (daily, weekly, monthly, quarterly)\", default: \"monthly\" },\n    { name: \"organization\", prompt: \"Organization (if applicable)\", default: \"\" },\n    { name: \"key_contacts\", prompt: \"Key Contacts (comma-separated)\", default: \"\" },\n    { name: \"last_review_date\", prompt: \"Last Review Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n    { name: \"next_review_date\", prompt: \"Next Review Date\", default: tp.date.now(\"YYYY-MM-DD\", 30) }\n  ];\n} else if (currentType === \"resource\") {\n  metadataFields = [\n    { name: \"source\", prompt: \"Source\", default: \"Personal research\" },\n    { name: \"area\", prompt: \"Related Area\", default: \"Software-Development\" },\n    { name: \"difficulty\", prompt: \"Difficulty (easy, medium, hard)\", default: \"medium\" },\n    { name: \"keywords\", prompt: \"Keywords (comma-separated)\", default: \"guide, reference\" },\n    { name: \"last_used\", prompt: \"Last Used Date\", default: tp.date.now(\"YYYY-MM-DD\") },", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 40, "startLine": 28}, "similarity": 0.5443378690567169}, {"id": 7685, "path": "Templates/Enhanced Project.md", "mtime": 1752452357284, "content": "---\n# CORE METADATA\ncreation_date: <% tp.date.now(\"YYYY-MM-DD\") %>\nmodification_date: <% tp.date.now(\"YYYY-MM-DD\") %>\ntype: project\nstatus: <% await tp.system.suggester([\"active\", \"on-hold\", \"completed\", \"cancelled\"], [\"active\", \"on-hold\", \"completed\", \"cancelled\"], false, \"Project Status\") %>\npriority: <% await tp.system.suggester([\"critical\", \"high\", \"medium\", \"low\"], [\"critical\", \"high\", \"medium\", \"low\"], false, \"Project Priority\") %>\narea_category: <% await tp.system.suggester([\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], [\"Software-Development\", \"Administration\", \"Personal\", \"Church\", \"Education\", \"Finance\"], false, \"Area Category\") %>\nowner: Jordan\ntags: [para/projects, <% await tp.system.suggester([\"software-dev\", \"church\", \"personal\", \"university\", \"admin\", \"finance\"], [\"software-dev\", \"church\", \"personal\", \"university\", \"admin\", \"finance\"], false, \"Primary Tag\") %>]", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 10, "startLine": 1}, "similarity": 0.5443376246203115}, {"id": 11327, "path": "0.2-Tasks/Tasks Dashboard.md", "mtime": 1754576304776, "content": "---\n# CORE METADATA\ncreation_date: 2025-08-07\nmodification_date: 2025-08-07\ntype: task-dashboard\nstatus: active\npriority: critical\narea_category: Administration\nowner: Jordan\ntags: [tasks, dashboard, productivity, central-hub]\n\n# RELATIONSHIPS (Enhanced System)\nrelated_areas: [\"[[2-Areas/Task Management]]\", \"[[2-Areas/Website-Maintenance]]\", \"[[2-Areas/Church Administration]]\"]\nrelated_resources: [\"[[3-Resources/Vault Organization Guide]]\", \"[[3-Resources/Software Development Resources]]\"]\nrelated_people: [\"[[Jordan]]\"]\n\n# TASK MANAGEMENT\ntask_priority: critical\ntask_context: admin\n---\n\n# 🎯 Tasks Dashboard - Central Command\n\n> **The central hub for all task management across the vault** - Your single source of truth for what needs to be done.", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 24, "startLine": 1}, "similarity": 0.5437304863689076}, {"id": 7700, "path": "Templates/Enhanced Resource V2.md", "mtime": 1752452765683, "content": "## Overview\n<!-- Brief description of this resource -->\n\n## Key Points\n<!-- Main takeaways or important information -->\n- \n\n## Details\n<!-- Detailed information -->", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 108, "startLine": 100}, "similarity": 0.5435759036902932}, {"id": 7773, "path": "Templates/Enhance <PERSON>.md", "mtime": 1747611992224, "content": "{ name: \"keywords\", prompt: \"Keywords (comma-separated)\", default: \"guide, reference\" },\n    { name: \"last_used\", prompt: \"Last Used Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n    { name: \"url\", prompt: \"URL (if applicable)\", default: \"\" },\n    { name: \"author\", prompt: \"Author (if applicable)\", default: \"\" }\n  ];\n} else if (currentType === \"person\") {\n  metadataFields = [\n    { name: \"first_name\", prompt: \"First Name\", default: \"\" },\n    { name: \"last_name\", prompt: \"Last Name\", default: \"\" },\n    { name: \"organization\", prompt: \"Organization\", default: \"\" },\n    { name: \"role\", prompt: \"Role\", default: \"\" },\n    { name: \"email\", prompt: \"Email\", default: \"\" },\n    { name: \"phone\", prompt: \"Phone\", default: \"\" },\n    { name: \"relationship\", prompt: \"Relationship (colleague, client, friend, family, service provider)\", default: \"\" }\n  ];\n} else if (currentType === \"meeting\") {\n  metadataFields = [\n    { name: \"time\", prompt: \"Meeting Time\", default: tp.date.now(\"HH:mm\") },", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 56, "startLine": 39}, "similarity": 0.5425129149128565}, {"id": 8993, "path": "3-Resources/PaceySpace Wiki/Page-Templates/Enhance Metadata.md", "mtime": 1748968575368, "content": "{ name: \"keywords\", prompt: \"Keywords (comma-separated)\", default: \"guide, reference\" },\n    { name: \"last_used\", prompt: \"Last Used Date\", default: tp.date.now(\"YYYY-MM-DD\") },\n    { name: \"url\", prompt: \"URL (if applicable)\", default: \"\" },\n    { name: \"author\", prompt: \"Author (if applicable)\", default: \"\" }\n  ];\n} else if (currentType === \"person\") {\n  metadataFields = [\n    { name: \"first_name\", prompt: \"First Name\", default: \"\" },\n    { name: \"last_name\", prompt: \"Last Name\", default: \"\" },\n    { name: \"organization\", prompt: \"Organization\", default: \"\" },\n    { name: \"role\", prompt: \"Role\", default: \"\" },\n    { name: \"email\", prompt: \"Email\", default: \"\" },\n    { name: \"phone\", prompt: \"Phone\", default: \"\" },\n    { name: \"relationship\", prompt: \"Relationship (colleague, client, friend, family, service provider)\", default: \"\" }\n  ];\n} else if (currentType === \"meeting\") {\n  metadataFields = [\n    { name: \"time\", prompt: \"Meeting Time\", default: tp.date.now(\"HH:mm\") },", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 56, "startLine": 39}, "similarity": 0.5425129149128565}, {"id": 11324, "path": "0.2-Tasks/Tasks TOC.md", "mtime": 1754576545824, "content": "## 📚 Documentation & Guides\n\n### User Guides\n- **[[0.2-Tasks/Guides/Getting Started|🚀 Getting Started]]** - New user introduction\n- **[[0.2-Tasks/Guides/Best Practices|⭐ Best Practices]]** - Effective task management tips\n- **[[0.2-Tasks/Guides/Troubleshooting|🔧 Troubleshooting]]** - Common issues and solutions\n\n### Reference\n- **[[0.2-Tasks/Reference/Task Schema|📋 Task Schema]]** - Metadata field definitions\n- **[[0.2-Tasks/Reference/Query Library|🔍 Query Library]]** - Useful dataview queries\n- **[[0.2-Tasks/Reference/Keyboard Shortcuts|⌨️ Keyboard Shortcuts]]** - Quick actions and hotkeys\n\n---", "model": "ollama/bge-m3", "dimension": 1024, "metadata": {"endLine": 115, "startLine": 103}, "similarity": 0.542412895242545}]}], "createdAt": 1757553483928, "updatedAt": 1757553965961, "schemaVersion": 1}