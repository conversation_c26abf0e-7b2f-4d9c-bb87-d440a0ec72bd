## Connect

[Check the docs](https://github.com/upstash/context7#readme) for installation
MCP URL

mcp.context7.com/mcp
API URL

context7.com/api/v1

API KEY
[ REDACTED]

Make sure to copy your new API KEY now. You won't be able to see it again.

Use this API key to authenticate requests to the Context7 API while using the MCP server.

## API

Use the Context7 API to search libraries and fetch documentation programmatically

```bash
curl -X GET "https://context7.com/api/v1/search?query=react+hook+form" \
  -H "Authorization: Bearer CONTEXT7_API_KEY"
```

#### Parameters

`query` - Search term for finding libraries

#### Response

```json
{
  "results": [
    {
      "id": "/react-hook-form/documentation",
      "title": "React Hook Form",
      "description": "📋 Official documentation", 
      "totalTokens": 50275,
      "totalSnippets": 274,
      "stars": 741,
      "trustScore": 9.1,
      "versions": []
    },
    ...
  ]
}
```
