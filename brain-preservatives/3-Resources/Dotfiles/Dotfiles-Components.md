---
creation_date: 2024-06-12
modification_date: 2024-06-12
type: note
aliases:
  - Dotfiles Components
  - Configuration Components
tags:
  - para/resources
  - linux
  - dotfiles
  - configuration
  - endeavouros
area: System
project: Dotfiles Management
resource: Linux
archive: 
status: active
priority: 1
links:
  - "[[Brain/3-Resources/Dotfiles/Dotfiles-Management]]"
related:
  - "[[Computer Education 1]]"
---

# Dotfiles Components

This document details the various components of my EndeavourOS dotfiles configuration.

## Table of Contents
- [[#Shell Configuration (Fish)]]
- [[#Window Manager (i3)]]
- [[#Desktop Environment (XFCE4)]]
- [[#Development Tools]]
- [[#System Configuration]]
- [[#Application Configuration]]

## Shell Configuration (Fish)

Fish is a user-friendly command line shell with features like syntax highlighting, autosuggestions, and tab completions.

### Key Files

| File | Purpose |
| ---- | ------- |
| `~/.config/fish/config.fish` | Main configuration file |
| `~/.config/fish/fish_variables` | Environment variables |
| `~/.config/fish/functions/*.fish` | Custom functions |
| `~/.config/fish/completions/*.fish` | Custom completions |
| `~/.config/fish/conf.d/*.fish` | Configuration modules |

### Notable Configurations

- NVM (Node Version Manager) integration
- Custom prompt configuration
- Aliases and functions for common tasks
- Environment variable settings

## Window Manager (i3)

i3 is a tiling window manager designed for efficiency and customization.

### Key Files

| File | Purpose |
| ---- | ------- |
| `~/.config/i3/config` | Main configuration file |
| `~/.config/i3/i3status.conf` | Status bar configuration |
| `~/.config/i3/scripts/` | Custom scripts for i3 |

### Notable Configurations

- Keybindings for window management
- Workspace configuration
- Autostart applications
- Status bar customization

## Desktop Environment (XFCE4)

XFCE4 is a lightweight desktop environment that provides a complete graphical user interface.

### Key Files

| File | Purpose |
| ---- | ------- |
| `~/.config/xfce4/xfconf/` | XFCE4 configuration database |
| `~/.config/xfce4/panel/` | Panel configuration |
| `~/.config/xfce4/terminal/` | Terminal configuration |

### Notable Configurations

- Panel layout and plugins
- Desktop appearance settings
- Session management
- Application menu configuration

## Development Tools

Configuration for various development tools used in my workflow.

### Git Configuration

| File | Purpose |
| ---- | ------- |
| `~/.gitconfig` | Global Git configuration |
| `~/.gitignore_global` | Global Git ignore patterns |

### SSH Configuration

| File | Purpose |
| ---- | ------- |
| `~/.ssh/config` | SSH client configuration |
| `~/.ssh/authorized_keys` | Authorized public keys |

### Editor Configuration

| File | Purpose |
| ---- | ------- |
| `~/.config/Code/User/settings.json` | VS Code settings |
| `~/.vimrc` | Vim configuration |

## System Configuration

Configuration for system-level components.

### X11 Configuration

| File | Purpose |
| ---- | ------- |
| `~/.Xresources` | X resources configuration |
| `~/.xinitrc` | X initialization script |

### Input Device Configuration

| File | Purpose |
| ---- | ------- |
| `~/.config/libinput/` | Input device settings |

## Application Configuration

Configuration for various applications used in my workflow.

### Terminal Emulators
#kitty 

| File                                | Purpose                                     |
| ----------------------------------- | ------------------------------------------- |
| `~/.config/alacritty/alacritty.yml` | Alacritty terminal configuration            |
| `~/.config/kitty/kitty.conf`        | [[Fjord-an's Kitty terminal configuration]] |

### Web Browsers

| File | Purpose |
| ---- | ------- |
| `~/.mozilla/firefox/profiles.ini` | Firefox profile configuration |
| `~/.config/chromium/` | Chromium configuration |

## Tasks
- [ ] Document all fish shell functions
- [ ] Document i3 keybindings
- [ ] Document XFCE4 panel configuration
- [ ] Document application-specific configurations

## Metadata
- **Original Creation**: 2024-06-12
- **Source**: Personal configuration
- **Context**: System management and portability
