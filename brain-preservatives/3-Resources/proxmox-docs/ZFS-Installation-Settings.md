# ZFS Installation Settings for Proxmox

## ZFS Advanced Options Explained

### 1. **ashift: 12** ✅ KEEP THIS
- **What it is**: Sector size alignment (2^12 = 4KB sectors)
- **Why 12**: Modern SSDs use 4KB sectors internally
- **Recommendation**: **KEEP 12** (correct for modern drives)
- **Don't change unless**: You have very old drives with 512-byte sectors (then use 9)

### 2. **compress: on** ✅ RECOMMENDED
- **What it is**: Real-time compression of data
- **Benefits**: 
  - Saves disk space (typically 20-40% reduction)
  - Can actually IMPROVE performance (less disk I/O)
  - Transparent to applications
- **Recommendation**: **KEEP ON** (lz4 compression is very fast)
- **CPU impact**: Minimal with modern CPUs

### 3. **checksum: on** ✅ ALWAYS KEEP ON
- **What it is**: Data integrity verification
- **Benefits**: 
  - Detects data corruption
  - Prevents silent data corruption
  - Self-healing with redundancy
- **Recommendation**: **NEVER TURN OFF**
- **This is a core ZFS feature**: Don't disable

### 4. **copies: 1** ✅ CORRECT FOR SINGLE DISK
- **What it is**: Number of data copies stored
- **For single disk**: Keep at 1
- **For RAID**: Can be 2 or 3 for extra redundancy
- **Recommendation**: **KEEP 1** (unless you have multiple disks and want extra redundancy)

### 5. **ARC max size: 1593** 📝 NEEDS ADJUSTMENT
- **What it is**: ZFS cache size in MB
- **Current**: 1593 MB (~1.6 GB)
- **Rule of thumb**: 50-75% of total RAM for ZFS-only systems
- **Recommendation depends on your RAM**:
  - **8 GB RAM**: Set to 2048-3072 MB (2-3 GB)
  - **16 GB RAM**: Set to 4096-6144 MB (4-6 GB)
  - **32 GB RAM**: Set to 8192-12288 MB (8-12 GB)

### 6. **hdsize** 📝 CRITICAL - DISK PARTITIONING
- **What it is**: How much of the disk to use for ZFS
- **Options**:
  - **Leave blank**: Uses entire disk
  - **Specify size**: Leaves remainder unallocated
- **Recommendations**:

#### For SSD (Recommended):
```
hdsize: Leave 10-20% unallocated
Example for 1TB SSD: hdsize = 800-900 GB
```

#### For HDD:
```
hdsize: Can use 90-95% of disk
Example for 2TB HDD: hdsize = 1800-1900 GB
```

## Recommended Settings by System

### Small System (8GB RAM, 256GB SSD):
```
ashift: 12
compress: on
checksum: on
copies: 1
ARC max size: 2048
hdsize: 200 (leaves 56GB unallocated)
```

### Medium System (16GB RAM, 512GB SSD):
```
ashift: 12
compress: on
checksum: on
copies: 1
ARC max size: 4096
hdsize: 400 (leaves 112GB unallocated)
```

### Large System (32GB RAM, 1TB SSD):
```
ashift: 12
compress: on
checksum: on
copies: 1
ARC max size: 8192
hdsize: 800 (leaves 224GB unallocated)
```

## Why Leave Space Unallocated?

### For SSDs (Very Important):
1. **Over-provisioning**: Improves performance and lifespan
2. **Wear leveling**: SSD controller has extra cells to work with
3. **Performance**: Maintains speed as disk fills up
4. **Replacement**: Ensures new SSD will fit even if slightly smaller

### For HDDs:
1. **Future expansion**: Can add to ZFS pool later
2. **Partition flexibility**: Space for other uses
3. **Safety margin**: Room for filesystem overhead

## Storage Layout After Installation

With your settings, you'll get:

```
/dev/sda1    512M    EFI System Partition
/dev/sda2    1G      Boot partition
/dev/sda3    [rest]  ZFS pool (rpool)
  ├── ROOT/pve-1     (root filesystem)
  ├── data           (VM storage)
  └── [free space]   (for snapshots, etc.)
```

## Quick Decision Guide

**If you're unsure about your hardware specs:**

### Conservative/Safe Settings:
```
ashift: 12
compress: on
checksum: on
copies: 1
ARC max size: 2048
hdsize: [total disk size] - 50GB
```

### Performance Settings (if you know your system):
```
ashift: 12
compress: on
checksum: on
copies: 1
ARC max size: [RAM in MB] * 0.5
hdsize: [total disk size] * 0.8
```

## What Happens If You Get It Wrong?

### Can be changed later:
- compress (per dataset)
- ARC max size (in /etc/modprobe.d/)
- Can add more disks to pool

### Cannot be changed later:
- ashift (set at pool creation)
- hdsize (would require reinstall)

## My Recommendation for You

**Without knowing your exact specs, use these safe defaults:**

```
ashift: 12          ← KEEP
compress: on        ← KEEP  
checksum: on        ← KEEP
copies: 1          ← KEEP
ARC max size: 3072  ← CHANGE (assuming 8-16GB RAM)
hdsize: [leave ~20% unallocated]
```

**Example**: If you have a 500GB drive, set hdsize to 400.

## Need to Know Your System?

If you want specific recommendations, we need:
1. Total RAM amount
2. Disk size and type (SSD/HDD)
3. Intended use (how many VMs planned)

The settings above are safe defaults that will work well for most home lab setups!
