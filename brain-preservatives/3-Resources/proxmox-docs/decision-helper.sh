#!/bin/bash

echo "========================================="
echo "     Proxmox Installation Decision Helper"
echo "========================================="
echo ""

echo "This script will help you decide whether to install Proxmox."
echo ""

score=0

echo "Question 1: Do you need the TUF laptop for daily use?"
read -p "(yes/no): " daily_use
if [ "$daily_use" = "no" ]; then
    ((score++))
fi

echo ""
echo "Question 2: Are you comfortable managing a Linux server?"
read -p "(yes/no): " server_comfort
if [ "$server_comfort" = "yes" ]; then
    ((score++))
fi

echo ""
echo "Question 3: Do you have another computer to access the Proxmox web interface?"
read -p "(yes/no): " other_computer
if [ "$other_computer" = "yes" ]; then
    ((score++))
fi

echo ""
echo "Question 4: Do you want to run multiple VMs/containers 24/7?"
read -p "(yes/no): " vms_247
if [ "$vms_247" = "yes" ]; then
    ((score++))
fi

echo ""
echo "Question 5: Have you backed up all important data from the TUF laptop?"
read -p "(yes/no): " backed_up
if [ "$backed_up" = "yes" ]; then
    ((score++))
fi

echo ""
echo "Question 6: Are you willing to learn a new platform?"
read -p "(yes/no): " willing_learn
if [ "$willing_learn" = "yes" ]; then
    ((score++))
fi

echo ""
echo "========================================="
echo "RESULTS:"
echo "========================================="
echo "Your score: $score/6"
echo ""

if [ $score -eq 6 ]; then
    echo "✅ RECOMMENDATION: You're ready to install Proxmox!"
    echo "All conditions are favorable for installation."
elif [ $score -ge 4 ]; then
    echo "⚠️  RECOMMENDATION: Consider testing first."
    echo "You meet most requirements, but test in a VM first to be sure."
elif [ $score -ge 2 ]; then
    echo "🤔 RECOMMENDATION: Probably not ready yet."
    echo "Consider using KVM/QEMU on Ubuntu instead, or test extensively in a VM."
else
    echo "❌ RECOMMENDATION: Don't install Proxmox on the laptop."
    echo "Keep Ubuntu and use VirtualBox or KVM for virtualization needs."
fi

echo ""
echo "========================================="
echo "NEXT STEPS:"
echo "========================================="

if [ $score -ge 4 ]; then
    echo "1. Read 00-IMPORTANT-README.md first"
    echo "2. Test Proxmox in a VirtualBox VM"
    echo "3. Review all documentation"
    echo "4. Make final decision after testing"
else
    echo "1. Install KVM on Ubuntu: sudo apt install qemu-kvm virt-manager"
    echo "2. Use virt-manager for GUI VM management"
    echo "3. Consider Proxmox for future when you have dedicated hardware"
fi

echo ""
echo "Remember: There's no rush! Test first, decide later."
