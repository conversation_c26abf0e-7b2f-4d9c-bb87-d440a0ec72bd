# Creating Your First VM and Network Setup in Proxmox

## Quick Start: First VM in 5 Minutes

### 1. Upload ISO
1. Click on your node (e.g., "pve")
2. Click "local" storage
3. Click "ISO Images"
4. Click "Upload"
5. Select your ISO file (e.g., Ubuntu Server)

### 2. Create VM
1. Click "Create VM" (top-right blue button)
2. **General**: Name your VM (e.g., "ubuntu-server")
3. **OS**: Select your uploaded ISO
4. **System**: Leave defaults (or enable QEMU Agent)
5. **Disks**: Set size (e.g., 20GB)
6. **CPU**: Set cores (e.g., 2)
7. **Memory**: Set RAM (e.g., 2048 MB)
8. **Network**: Leave default (vmbr0)
9. Click "Finish"

### 3. Start and Install
1. Select your new VM
2. Click "Start"
3. Click "Console"
4. Follow OS installation

## Detailed VM Creation Guide

### Before You Begin
- Have ISO files ready
- Know your network settings
- Plan resource allocation

### Step-by-Step VM Creation

#### 1. General Settings
```
Node: pve (your node name)
VM ID: 100 (auto-assigned)
Name: my-ubuntu-vm
Resource Pool: None
Start at boot: [ ] (unchecked initially)
Start/Shutdown order: Default
```

#### 2. OS Selection
```
ISO Image: ubuntu-22.04-server.iso
Guest OS Type: Linux
Version: 5.x - 2.6 Kernel
```

#### 3. System Settings
```
Graphics: Default (VGA)
Machine: q35 (newer) or i440fx (older)
BIOS: SeaBIOS (legacy) or OVMF (UEFI)
Add EFI Disk: Yes (if using OVMF)
QEMU Agent: ✓ Enable (important!)
```

#### 4. Hard Disk
```
Bus/Device: SCSI 0
Storage: local-lvm
Disk Size: 20 GB
Format: QEMU image format (qcow2)
Cache: Default (no cache)
Discard: ✓ (if SSD)
```

#### 5. CPU Configuration
```
Sockets: 1
Cores: 2 (adjust based on needs)
Type: kvm64 or host
Enable NUMA: [ ] (for single socket)
```

#### 6. Memory
```
Memory (MiB): 2048
Minimum memory: 512 (for ballooning)
Ballooning: ✓ (dynamic memory)
```

#### 7. Network
```
Bridge: vmbr0
Model: VirtIO (best) or Intel E1000
MAC Address: auto
Firewall: ✓
```

### Post-Installation Tasks

#### Install QEMU Guest Agent (Linux)
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install qemu-guest-agent
sudo systemctl enable --now qemu-guest-agent

# RHEL/CentOS
sudo yum install qemu-guest-agent
sudo systemctl enable --now qemu-guest-agent
```

#### Install VirtIO Drivers (Windows)
1. Download: https://fedorapeople.org/groups/virt/virtio-win/
2. Mount ISO in VM
3. Run installer
4. Install guest agent

## Container (LXC) Creation

### Quick Container Setup
1. Download template: local → CT Templates → Templates
2. Click "Create CT"
3. Configure:
   - CT ID: 200
   - Hostname: my-container
   - Password: set strong password
   - Template: select downloaded
   - Root Disk: 8GB
   - CPU: 1 core
   - Memory: 512MB
   - Network: DHCP or static
4. Start container

### Container vs VM

| Feature | Container (LXC) | Virtual Machine (KVM) |
|---------|----------------|----------------------|
| Performance | Very Fast | Good |
| Resource Usage | Minimal | Higher |
| Isolation | Kernel-level | Full |
| OS Support | Linux only | Any OS |
| Boot Time | Seconds | Minutes |
| Use Case | Services | Full systems |

## Network Configuration

### Understanding Proxmox Networks

#### Default Bridge (vmbr0)
- Connected to physical network
- VMs get IPs from your router
- Can access internet and local network

### Creating Internal Networks

#### Add New Bridge
```bash
# Edit network config
nano /etc/network/interfaces

# Add internal bridge
auto vmbr1
iface vmbr1 inet static
    address **********
    netmask *************
    bridge_ports none
    bridge_stp off
    bridge_fd 0

# Apply changes
systemctl restart networking
```

### VLAN Setup
1. Node → System → Network
2. Create → Linux VLAN
3. Configure:
   - Name: vlan10
   - VLAN Tag: 10
   - Parent: vmbr0

### Common Network Configurations

#### NAT Network
```bash
# Enable IP forwarding
echo 1 > /proc/sys/net/ipv4/ip_forward

# Add NAT rules
iptables -t nat -A POSTROUTING -s **********/24 -o vmbr0 -j MASQUERADE
```

#### Port Forwarding
```bash
# Forward port 80 to VM
iptables -t nat -A PREROUTING -i vmbr0 -p tcp --dport 80 -j DNAT --to ************:80
```

## VM Management

### Basic Operations
- **Start**: Green play button
- **Shutdown**: Red stop (graceful)
- **Stop**: Red square (force)
- **Reset**: Circular arrow
- **Pause**: Pause button
- **Console**: Monitor icon

### Snapshots
1. Select VM → Snapshots
2. Click "Take Snapshot"
3. Name: "before-updates"
4. Include RAM: Optional
5. Description: Add notes

### Cloning
1. Right-click VM → Clone
2. Choose:
   - Full Clone: Independent copy
   - Linked Clone: Shares base disk
3. Set new VM ID and name

### Backup
1. Select VM → Backup
2. Click "Backup now"
3. Choose:
   - Storage: backup location
   - Mode: snapshot/suspend/stop
   - Compression: lzo/gzip/zstd

## Troubleshooting

### VM Won't Start
```bash
# Check status
qm status 100

# View config
qm config 100

# Check logs
cat /var/log/pve/qemu-server/100.log

# Remove lock
qm unlock 100
```

### No Network in VM
1. Check bridge exists: `brctl show`
2. Check VM network: `qm config 100 | grep net`
3. Inside VM: Check IP configuration

### Performance Issues
- Enable VirtIO drivers
- Set CPU type to "host"
- Disable unnecessary devices
- Check resource allocation

## Best Practices

### Resource Planning
- Don't over-allocate RAM
- CPU can be overcommitted 2:1
- Use thin provisioning for storage
- Monitor actual usage

### Security
- Install guest agents
- Enable firewall
- Regular snapshots
- Backup before changes
- Use strong passwords

### Optimization
- VirtIO for network and disk
- QEMU Agent for better integration
- Appropriate CPU type
- Memory ballooning for flexibility

## Common VM Templates

### Ubuntu Server Template
```bash
# After installation:
apt update && apt upgrade -y
apt install qemu-guest-agent cloud-init
systemctl enable qemu-guest-agent
# Clean for template
apt clean
cloud-init clean
> /etc/machine-id
```

### Windows Template
1. Install Windows
2. Install VirtIO drivers
3. Install guest agent
4. Run sysprep
5. Convert to template

## Next Steps

Now you can:
- ✓ Create and manage VMs
- ✓ Configure networking
- ✓ Take snapshots
- ✓ Make backups

Continue learning:
- Advanced networking (VLANs, bonding)
- Storage management
- High availability
- Clustering
- Automation with API
