/* ---------------------------- */
/*   Main Metadata Menu style   */
/* ---------------------------- */
.metadata-menu .chip {
  border-radius: 0.8em;
  padding: 0.1em 0.75em 0.25em 0.75em;
  color: white;
  margin-left: 0.5em;
  margin-right: 0.5em;
  height: 1.6em;
  white-space: nowrap;
}
.metadata-menu .chip.field-type {
  font-size: x-small;
}
.metadata-menu .chip.media-type-container {
  background-color: var(--text-accent);
}
.metadata-menu .chip.cycle {
  background-color: var(--color-blue);
}
.metadata-menu .chip.multi {
  background-color: var(--color-green);
}
.metadata-menu .chip.boolean {
  background-color: var(--color-yellow);
}
.metadata-menu .chip.single {
  background-color: var(--color-green);
}
.metadata-menu .chip.select {
  background-color: var(--color-orange);
}
.metadata-menu .chip.number {
  background-color: var(--color-purple);
}
.metadata-menu .chip.file {
  background-color: var(--color-blue);
}
.metadata-menu .chip.date {
  background-color: var(--color-pink);
}
.metadata-menu .chip.datetime {
  background-color: var(--color-pink);
}
.metadata-menu .chip.time {
  background-color: var(--color-pink);
}
.metadata-menu .chip.lookup {
  background-color: var(--color-red);
}
.metadata-menu .chip.formula {
  background-color: var(--color-red);
}
.metadata-menu .chip.canvas-links {
  background-color: var(--color-blue);
}
.metadata-menu .chip.yaml {
  background-color: var(--color-blue);
}
.metadata-menu .chip.json {
  background-color: var(--color-blue);
}
.metadata-menu .chip.object {
  background-color: var(--color-red);
}
.metadata-menu .chip.object-list {
  background-color: var(--color-red);
}

.metadata-menu-dv-field-container {
  display: flex;
  align-items: center;
  min-height: var(--font-adaptive-normal);
}
.metadata-menu-dv-field-container .value-container {
  margin-right: 0.375em;
}
.metadata-menu-dv-field-container .values-container {
  display: flex;
}
.metadata-menu-dv-field-container .values-container .item-container {
  display: flex;
}
.metadata-menu-dv-field-container .values-container .item-container .label {
  padding-left: 0.5em;
  padding-right: 0.5em;
  background-color: var(--color-base-30);
  margin-right: 0.375em;
  border-radius: var(--button-radius);
  font-size: small;
  height: 1.5em;
}
.metadata-menu-dv-field-container .values-container .item-container .label.hovered {
  padding-left: 0.875em;
  border-top-left-radius: 0em;
  border-bottom-left-radius: 0em;
}
.metadata-menu-dv-field-container .values-container .item-container button {
  border-radius: var(--button-radius) 0 0 var(--button-radius);
  margin-right: 0;
  height: 1.5em;
  background-color: var(--color-accent);
  color: var(--text-on-accent);
}
.metadata-menu-dv-field-container input {
  margin-right: 0.375em;
}
.metadata-menu-dv-field-container input.is-invalid {
  color: var(--color-red);
}
.metadata-menu-dv-field-container button {
  min-width: calc(var(--font-text-size) * 1.4);
  padding: 0.6em 0em 0.9em 0em;
  margin-right: 0.375em;
  height: 1em;
}
.metadata-menu-dv-field-container button svg {
  width: 12px;
  height: 12px;
  margin: 0.4em 0.2em 0.1em 0.2em;
}
.metadata-menu-dv-field-container button.active {
  background-color: #556b77;
  border-radius: 0.375em;
}
.metadata-menu-dv-field-container button.multi {
  margin-right: 0em;
}
.metadata-menu-dv-field-container button.disabled {
  color: var(--text-faint);
}
.metadata-menu-dv-field-container .spacer-1 {
  width: calc(var(--font-text-size) * 1.7);
}
.metadata-menu-dv-field-container .spacer-2 {
  width: calc(var(--font-text-size) * 3.4);
}
.metadata-menu-dv-field-container .spacer-3 {
  width: calc(var(--font-text-size) * 5.1);
}

.metadata-menu.fileclass-icon {
  color: var(--link-color);
  margin-left: 0.2em;
  cursor: context-menu;
  align-items: center;
  display: inline-flex;
  margin-left: 0.375em;
}
.metadata-menu.fileclass-icon.dataview-fileclass-icon {
  grid-area: 1;
  margin-right: 0.375em;
}
.metadata-menu.fileclass-icon.field-status-may-have-changed {
  color: var(--color-yellow);
}
.metadata-menu.fileclass-icon.field-status-changed {
  color: var(--text-warning);
}
.metadata-menu.fileclass-icon.field-status-error {
  color: var(--text-error);
}
.metadata-menu.fileclass-icon svg {
  height: calc(var(--icon-size) * 0.935);
  width: calc(var(--icon-size) * 0.935);
}

.workspace-tab-header-inner .metadata-menu.fileclass-icon svg {
  vertical-align: calc(var(--icon-size) * 0.15);
}

.plugin-metadata-menu {
  padding: 0 var(--size-2-2);
}
.plugin-metadata-menu .status-item-btn {
  box-shadow: none;
  padding: 0;
  height: inherit;
}
.plugin-metadata-menu .status-item-btn .sync-status-icon .svg-icon {
  stroke-width: 1.5;
  stroke: var(--text-faint);
}
.plugin-metadata-menu .status-item-btn .sync-status-icon.indexing .rotating {
  stroke: var(--color-blue);
}
.plugin-metadata-menu .status-item-btn .sync-status-icon.indexed .check-mark {
  stroke: var(--color-green);
}

.metadata-menu.modal-container.narrow .modal {
  width: initial;
}
.metadata-menu.modal-container .cm-gutter {
  color: var(--text-code);
  background-color: var(--background-primary);
}
.metadata-menu.modal-container .cm-activeLineGutter, .metadata-menu.modal-container .cm-activeLine {
  background-color: var(--text-accent);
  color: var(--text-on-accent);
}
.metadata-menu.modal-container .vstacked {
  display: grid;
  margin: 1em 0;
}
.metadata-menu.modal-container .vstacked code {
  color: var(--text-code);
  font-size: smaller;
}
.metadata-menu.modal-container .vstacked .field-container {
  margin: 0;
}
.metadata-menu.modal-container .sub-text {
  display: flex;
  font-size: smaller;
  color: var(--text-muted);
}
.metadata-menu.modal-container .info-code {
  font-size: x-small;
}
.metadata-menu.modal-container .suggestion-item .field-type-container {
  min-width: 160px;
}
.metadata-menu.modal-container .suggestion-item .field-type-tooltip {
  color: var(--text-faint);
}
.metadata-menu.modal-container .field-container {
  display: flex;
  align-items: center;
  margin: 1em 0;
}
.metadata-menu.modal-container .field-container .field-type-label, .metadata-menu.modal-container .field-container .parent-label {
  margin: 0 1em;
  white-space: nowrap;
}
.metadata-menu.modal-container .field-container .field-type-label.empty, .metadata-menu.modal-container .field-container .parent-label.empty {
  color: var(--text-faint);
}
.metadata-menu.modal-container .field-container .tooltip-btn .tooltip-button {
  background: none;
  box-shadow: none;
  border: none;
  vertical-align: middle;
  color: var(--text-warning);
  padding-right: 1em;
}
.metadata-menu.modal-container .field-container .info {
  white-space: nowrap;
  margin-right: 1em;
  font-size: x-small;
  font-style: italic;
  color: var(--color-accent);
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container .field-container .info {
    display: none;
  }
}
.metadata-menu.modal-container .field-container .label {
  white-space: nowrap;
}
.metadata-menu.modal-container .field-container .label.bold {
  font-weight: var(--bold-weight);
  color: var(--bold-color);
}
.metadata-menu.modal-container .field-container .label.code {
  font-family: monospace;
  color: var(--code-normal);
  font-size: var(--code-size);
  background-color: var(--code-background);
  white-space: pre;
}
.metadata-menu.modal-container .field-container .label.italic {
  font-style: italic;
  color: var(--italic-color);
}
.metadata-menu.modal-container .field-container .label.strikethrough {
  text-decoration: line-through;
}
.metadata-menu.modal-container .field-container .style-buttons-container {
  display: inline-flex;
}
.metadata-menu.modal-container .field-container .style-buttons-container .style-button-container {
  display: block;
  margin-right: 1em;
  margin-top: 0;
  margin-bottom: 0;
}
.metadata-menu.modal-container .field-container .style-buttons-container .style-button-container:last-child {
  margin-right: 0;
}
.metadata-menu.modal-container .field-container .style-buttons-container .style-button-container .style-btn-label {
  margin-right: 0.5em;
}
.metadata-menu.modal-container .field-container input, .metadata-menu.modal-container .field-container textarea {
  margin-right: 1em;
}
.metadata-menu.modal-container .field-container input.with-label, .metadata-menu.modal-container .field-container textarea.with-label {
  margin-left: 1em;
}
.metadata-menu.modal-container .field-container input.full-width, .metadata-menu.modal-container .field-container textarea.full-width {
  width: 100%;
}
.metadata-menu.modal-container .field-container input:last-child, .metadata-menu.modal-container .field-container textarea:last-child {
  margin-right: 0;
}
.metadata-menu.modal-container .field-container .date-input-wrapper {
  display: flex;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  color: var(--text-normal);
  font-family: inherit;
  font-size: var(--font-ui-small);
  border-radius: var(--input-radius);
  outline: none;
  margin-right: 1em;
  padding: 0;
}
.metadata-menu.modal-container .field-container .date-input-wrapper:focus {
  box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
}
.metadata-menu.modal-container .field-container .date-input-wrapper input {
  border: none;
}
.metadata-menu.modal-container .field-container .date-input-wrapper input:focus {
  box-shadow: none;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .master-input {
  margin-right: 0;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .master-input.time {
  width: 7em;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .master-input.date {
  width: 14em;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .master-input.datetime {
  width: 14em;
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container .field-container .date-input-wrapper .master-input {
    display: none;
  }
}
.metadata-menu.modal-container .field-container .date-input-wrapper .date-picker {
  width: 0;
  margin-right: 0;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .time-picker {
  width: 2em;
  margin-right: 0.5em;
  font-size: large;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .date-picker-button {
  border: none;
  box-shadow: none;
  size: 1em;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .date-picker-button:hover {
  background-color: inherit;
}
.metadata-menu.modal-container .field-container textarea.full-width {
  width: 100%;
}
.metadata-menu.modal-container .field-container .icon-preview {
  margin: 0 0.6em;
  color: var(--text-muted);
}
.metadata-menu.modal-container .field-container .spacer {
  width: 100%;
}
.metadata-menu.modal-container .field-container .is-invalid {
  color: var(--color-red);
}
.metadata-menu.modal-container .field-container .more-info {
  color: var(--text-faint);
  white-space: nowrap;
  margin-left: 0.5em;
  font-size: small;
  font-style: italic;
}
.metadata-menu.modal-container .field-container .warning {
  color: var(--color-red);
}
.metadata-menu.modal-container .field-container .node-color {
  min-height: 2em;
  min-width: 2em;
  border-radius: 50%;
  margin: 0.2em 0.2em;
  text-align: center;
}
.metadata-menu.modal-container .field-container .node-color.color-1 {
  background-color: rgba(var(--canvas-color-1), 0.2);
  color: rgba(var(--canvas-color-1), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-1.active {
  background-color: rgba(var(--canvas-color-1), 1);
  color: rgba(var(--canvas-color-1), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-2 {
  background-color: rgba(var(--canvas-color-2), 0.2);
  color: rgba(var(--canvas-color-2), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-2.active {
  background-color: rgba(var(--canvas-color-2), 1);
  color: rgba(var(--canvas-color-2), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-3 {
  background-color: rgba(var(--canvas-color-3), 0.2);
  color: rgba(var(--canvas-color-3), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-3.active {
  background-color: rgba(var(--canvas-color-3), 1);
  color: rgba(var(--canvas-color-3), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-4 {
  background-color: rgba(var(--canvas-color-4), 0.2);
  color: rgba(var(--canvas-color-4), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-4.active {
  background-color: rgba(var(--canvas-color-4), 1);
  color: rgba(var(--canvas-color-4), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-5 {
  background-color: rgba(var(--canvas-color-5), 0.2);
  color: rgba(var(--canvas-color-5), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-5.active {
  background-color: rgba(var(--canvas-color-5), 1);
  color: rgba(var(--canvas-color-5), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-6 {
  background-color: rgba(var(--canvas-color-6), 0.2);
  color: rgba(var(--canvas-color-6), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-6.active {
  background-color: rgba(var(--canvas-color-6), 1);
  color: rgba(var(--canvas-color-6), 1);
}
.metadata-menu.modal-container .field-container .node-color svg {
  vertical-align: bottom;
  stroke-width: 3px;
}
.metadata-menu.modal-container .field-container .node-color.picker {
  background: conic-gradient(var(--color-red), var(--color-yellow), var(--color-green), var(--color-blue), var(--color-purple), var(--color-red));
}
.metadata-menu.modal-container .field-container .node-color.picker input {
  opacity: 0;
  height: 2em;
  width: 2em;
}
.metadata-menu.modal-container .field-container .edge-side {
  display: flex;
}
.metadata-menu.modal-container .field-container .edge-side .side-icon svg {
  height: 22px;
  width: 22px;
  margin: 0 0.1em 0 0.7em;
}
.metadata-menu.modal-container .field-container.edges {
  margin-bottom: 0;
}
.metadata-menu.modal-container .field-container.colors {
  display: flex;
  flex-wrap: wrap;
}
.metadata-menu.modal-container .field-container.labels {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.metadata-menu.modal-container .field-container.labels .label {
  margin-right: 1em;
}
.metadata-menu.modal-container .field-container.labels .item {
  white-space: nowrap;
}
.metadata-menu.modal-container .field-container.labels .item.chip {
  padding-top: 0.3em;
  margin-left: 0em;
  margin-right: 0.3em;
  margin-top: 0.3em;
  background-color: var(--tag-background);
  border: 1px solid var(--tag-border-color);
  width: fit-content;
  display: flex;
  color: var(--text-muted);
  align-items: center;
}
.metadata-menu.modal-container .field-container.labels .item p {
  margin-block-end: 0;
  margin-block-start: 0;
}
.metadata-menu.modal-container .field-container.labels .item.spacer {
  width: 100%;
}
.metadata-menu.modal-container .field-container.labels .item.right-align {
  padding-right: 0;
}
.metadata-menu.modal-container .field-container.labels .item button {
  border: none;
  box-shadow: none;
  background: none;
  padding-right: 0;
}
.metadata-menu.modal-container .field-container.labels .item button:hover {
  color: var(--text-accent);
}
.metadata-menu.modal-container .field-container.labels .item button.item-remove:hover {
  color: var(--text-accent);
}
.metadata-menu.modal-container .field-container button {
  margin-right: 12px;
}
.metadata-menu.modal-container .field-container button :disabled {
  opacity: 50%;
}
.metadata-menu.modal-container .field-container button .danger {
  background-color: var(--color-purple);
  color: var(--color-base-00);
}
.metadata-menu.modal-container .field-container button:last-child {
  margin-right: 0;
}
.metadata-menu.modal-container .field-error {
  height: 100%;
  color: var(--text-on-accent);
  width: 100%;
  background-color: var(--color-accent);
  padding: var(--size-4-1) var(--size-4-2);
  font-size: var(--font-ui-small);
  border-radius: var(--input-radius);
}
.metadata-menu.modal-container .field-warning {
  font-size: smaller;
  background-color: var(--color-accent);
  color: var(--text-on-accent);
  padding: 0.5em;
  border-radius: 0.5em;
}
.metadata-menu.modal-container .footer-actions {
  display: flex;
  padding: var(--size-4-3);
  align-items: center;
}
.metadata-menu.modal-container .footer-actions .spacer {
  width: 100%;
}
.metadata-menu.modal-container .footer-actions .info {
  white-space: nowrap;
  margin-right: 1em;
  font-size: x-small;
  font-style: italic;
  color: var(--color-accent);
}
.metadata-menu.modal-container .footer-actions button {
  margin-right: 12px;
}
.metadata-menu.modal-container .footer-actions button :disabled {
  opacity: 50%;
}
.metadata-menu.modal-container .footer-actions button .danger {
  background-color: var(--color-purple);
  color: var(--color-base-00);
}
.metadata-menu.modal-container .footer-actions button:last-child {
  margin-right: 0;
}
.metadata-menu.modal-container.confirm-modal .field-name {
  color: var(--text-accent);
}
.metadata-menu.modal-container.confirm-modal .field-value {
  color: var(--text-accent);
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container {
  display: flex;
  align-items: center;
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container .file-name {
  font-weight: 600;
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container .position-icon {
  display: flex;
  align-items: center;
  margin: 0 0.375em;
  color: var(--text-muted);
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container .position-icon svg {
  height: 0.8em;
  width: 0.8em;
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container .line-number {
  color: var(--text-muted);
  font-size: smaller;
}
.metadata-menu.modal-container.confirm-modal .target-container .current-value {
  color: var(--text-muted);
  font-size: smaller;
}
.metadata-menu.modal-container.confirm-modal .target-container .current-value.empty-or-missing {
  font-style: italic;
}

.metadata-menu.modal-container.note-fields-modal .navigation-separator {
  margin-top: 1em;
}
.metadata-menu.modal-container.note-fields-modal .back-button-wrapper {
  display: flex;
}
.metadata-menu.modal-container.note-fields-modal button {
  padding: 0 0 0.4em 0.4em;
  height: auto;
  box-shadow: none;
  background-color: var(--background-primary);
}
.metadata-menu.modal-container.note-fields-modal button.disabled {
  color: var(--text-faint);
}
.metadata-menu.modal-container.note-fields-modal button:disabled {
  color: var(--text-faint);
}
.metadata-menu.modal-container.note-fields-modal .modal {
  width: auto;
  min-width: var(--dialog-width);
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container.note-fields-modal .modal {
    max-width: 97vw;
    min-width: 0px;
    margin-bottom: 50px;
    margin-left: 6px;
  }
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container {
  display: grid;
  border-collapse: separate;
  grid-template-columns: auto auto 1fr auto;
  margin-top: 3px;
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container.note-fields-modal .note-fields-container {
    grid-template-columns: 270px 75px;
  }
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper {
  grid-column: 1;
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper {
  grid-column: 2;
  display: flex;
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-settings-spacer {
    width: 100%;
  }
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper {
  grid-column: 3;
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper {
    grid-column: 1;
    margin-bottom: 1em;
  }
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper {
  grid-column: 4;
  display: flex;
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper {
    grid-column: 2;
    margin-bottom: 1em;
  }
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-options-spacer {
  width: 100%;
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item {
  padding-top: 0.2em;
  padding-right: 0.5em;
  float: none;
  vertical-align: middle;
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item.field-name, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item.field-name, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item.field-name, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item.field-name {
  font-weight: bold;
  white-space: nowrap;
  background-color: none;
  color: var(--text-normal);
  padding-left: 0.6em;
  border: none;
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item.field-name, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item.field-name, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item.field-name, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item.field-name {
    padding-left: 0;
  }
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item.field-name.active, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item.field-name.active, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item.field-name.active, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item.field-name.active {
  padding-left: 0.2em;
  border-left: 0.4em var(--color-accent) solid;
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item .field-type, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item .field-type, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item .field-type, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item .field-type {
  margin-top: 0.25em;
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item .field-type.chip, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item .field-type.chip, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item .field-type.chip, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item .field-type.chip {
    margin-right: 0;
  }
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item.field-setting, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item.field-setting, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item.field-setting, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item.field-setting {
  padding-right: 0;
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item.field-setting button svg, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item.field-setting button svg, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item.field-setting button svg, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item.field-setting button svg {
  color: var(--text-faint);
  height: 1em;
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item.field-value, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item.field-value, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item.field-value, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item.field-value {
  width: 100%;
}
.metadata-menu.modal-container.note-fields-modal .note-fields-container .field-name-wrapper .field-item.field-value.emptyfield, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-value-wrapper .field-item.field-value.emptyfield, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-settings-wrapper .field-item.field-value.emptyfield, .metadata-menu.modal-container.note-fields-modal .note-fields-container .field-options-wrapper .field-item.field-value.emptyfield {
  color: var(--text-faint);
}
.metadata-menu.modal-container.note-fields-modal .insert-all-fields {
  margin-top: 0.4em;
  padding-left: 0.6em;
  display: flex;
}
.metadata-menu.modal-container.note-fields-modal .insert-all-fields button {
  padding: 0 0 0.4em 0.4em;
  height: auto;
  box-shadow: none;
  background-color: var(--background-primary);
  margin-left: 0.4em;
}
.metadata-menu.modal-container.note-fields-modal .fields-inheritance-manager-container {
  display: flex;
}
.metadata-menu.modal-container.note-fields-modal .fields-inheritance-manager-container .fileclass-manager-container {
  display: flex;
}
.metadata-menu.modal-container.note-fields-modal .fields-inheritance-manager-container .fileclass-manager-container .name {
  padding-bottom: 0.4em;
  border: none;
}
.metadata-menu.modal-container.note-fields-modal .fields-inheritance-manager-container .fileclass-manager-container .name.active {
  padding-bottom: 0em;
  border-bottom: 0.4em var(--color-accent) solid;
}
.metadata-menu.modal-container.note-fields-modal .fields-inheritance-manager-container .fileclass-manager-container .name:hover {
  cursor: context-menu;
}
.metadata-menu.modal-container.note-fields-modal .fields-inheritance-manager-container .fileclass-manager-container button svg {
  color: var(--text-faint);
  height: 1em;
}
.metadata-menu.modal-container.note-fields-modal .fields-inheritance-manager-container .separator {
  margin: 0 0.5em;
}

.metadata-menu.modal-container.narrow .modal {
  width: initial;
}
.metadata-menu.modal-container .cm-gutter {
  color: var(--text-code);
  background-color: var(--background-primary);
}
.metadata-menu.modal-container .cm-activeLineGutter, .metadata-menu.modal-container .cm-activeLine {
  background-color: var(--text-accent);
  color: var(--text-on-accent);
}
.metadata-menu.modal-container .vstacked {
  display: grid;
  margin: 1em 0;
}
.metadata-menu.modal-container .vstacked code {
  color: var(--text-code);
  font-size: smaller;
}
.metadata-menu.modal-container .vstacked .field-container {
  margin: 0;
}
.metadata-menu.modal-container .sub-text {
  display: flex;
  font-size: smaller;
  color: var(--text-muted);
}
.metadata-menu.modal-container .info-code {
  font-size: x-small;
}
.metadata-menu.modal-container .suggestion-item .field-type-container {
  min-width: 160px;
}
.metadata-menu.modal-container .suggestion-item .field-type-tooltip {
  color: var(--text-faint);
}
.metadata-menu.modal-container .field-container {
  display: flex;
  align-items: center;
  margin: 1em 0;
}
.metadata-menu.modal-container .field-container .field-type-label, .metadata-menu.modal-container .field-container .parent-label {
  margin: 0 1em;
  white-space: nowrap;
}
.metadata-menu.modal-container .field-container .field-type-label.empty, .metadata-menu.modal-container .field-container .parent-label.empty {
  color: var(--text-faint);
}
.metadata-menu.modal-container .field-container .tooltip-btn .tooltip-button {
  background: none;
  box-shadow: none;
  border: none;
  vertical-align: middle;
  color: var(--text-warning);
  padding-right: 1em;
}
.metadata-menu.modal-container .field-container .info {
  white-space: nowrap;
  margin-right: 1em;
  font-size: x-small;
  font-style: italic;
  color: var(--color-accent);
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container .field-container .info {
    display: none;
  }
}
.metadata-menu.modal-container .field-container .label {
  white-space: nowrap;
}
.metadata-menu.modal-container .field-container .label.bold {
  font-weight: var(--bold-weight);
  color: var(--bold-color);
}
.metadata-menu.modal-container .field-container .label.code {
  font-family: monospace;
  color: var(--code-normal);
  font-size: var(--code-size);
  background-color: var(--code-background);
  white-space: pre;
}
.metadata-menu.modal-container .field-container .label.italic {
  font-style: italic;
  color: var(--italic-color);
}
.metadata-menu.modal-container .field-container .label.strikethrough {
  text-decoration: line-through;
}
.metadata-menu.modal-container .field-container .style-buttons-container {
  display: inline-flex;
}
.metadata-menu.modal-container .field-container .style-buttons-container .style-button-container {
  display: block;
  margin-right: 1em;
  margin-top: 0;
  margin-bottom: 0;
}
.metadata-menu.modal-container .field-container .style-buttons-container .style-button-container:last-child {
  margin-right: 0;
}
.metadata-menu.modal-container .field-container .style-buttons-container .style-button-container .style-btn-label {
  margin-right: 0.5em;
}
.metadata-menu.modal-container .field-container input, .metadata-menu.modal-container .field-container textarea {
  margin-right: 1em;
}
.metadata-menu.modal-container .field-container input.with-label, .metadata-menu.modal-container .field-container textarea.with-label {
  margin-left: 1em;
}
.metadata-menu.modal-container .field-container input.full-width, .metadata-menu.modal-container .field-container textarea.full-width {
  width: 100%;
}
.metadata-menu.modal-container .field-container input:last-child, .metadata-menu.modal-container .field-container textarea:last-child {
  margin-right: 0;
}
.metadata-menu.modal-container .field-container .date-input-wrapper {
  display: flex;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  color: var(--text-normal);
  font-family: inherit;
  font-size: var(--font-ui-small);
  border-radius: var(--input-radius);
  outline: none;
  margin-right: 1em;
  padding: 0;
}
.metadata-menu.modal-container .field-container .date-input-wrapper:focus {
  box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
}
.metadata-menu.modal-container .field-container .date-input-wrapper input {
  border: none;
}
.metadata-menu.modal-container .field-container .date-input-wrapper input:focus {
  box-shadow: none;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .master-input {
  margin-right: 0;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .master-input.time {
  width: 7em;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .master-input.date {
  width: 14em;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .master-input.datetime {
  width: 14em;
}
@media screen and (max-width: 400px) {
  .metadata-menu.modal-container .field-container .date-input-wrapper .master-input {
    display: none;
  }
}
.metadata-menu.modal-container .field-container .date-input-wrapper .date-picker {
  width: 0;
  margin-right: 0;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .time-picker {
  width: 2em;
  margin-right: 0.5em;
  font-size: large;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .date-picker-button {
  border: none;
  box-shadow: none;
  size: 1em;
}
.metadata-menu.modal-container .field-container .date-input-wrapper .date-picker-button:hover {
  background-color: inherit;
}
.metadata-menu.modal-container .field-container textarea.full-width {
  width: 100%;
}
.metadata-menu.modal-container .field-container .icon-preview {
  margin: 0 0.6em;
  color: var(--text-muted);
}
.metadata-menu.modal-container .field-container .spacer {
  width: 100%;
}
.metadata-menu.modal-container .field-container .is-invalid {
  color: var(--color-red);
}
.metadata-menu.modal-container .field-container .more-info {
  color: var(--text-faint);
  white-space: nowrap;
  margin-left: 0.5em;
  font-size: small;
  font-style: italic;
}
.metadata-menu.modal-container .field-container .warning {
  color: var(--color-red);
}
.metadata-menu.modal-container .field-container .node-color {
  min-height: 2em;
  min-width: 2em;
  border-radius: 50%;
  margin: 0.2em 0.2em;
  text-align: center;
}
.metadata-menu.modal-container .field-container .node-color.color-1 {
  background-color: rgba(var(--canvas-color-1), 0.2);
  color: rgba(var(--canvas-color-1), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-1.active {
  background-color: rgba(var(--canvas-color-1), 1);
  color: rgba(var(--canvas-color-1), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-2 {
  background-color: rgba(var(--canvas-color-2), 0.2);
  color: rgba(var(--canvas-color-2), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-2.active {
  background-color: rgba(var(--canvas-color-2), 1);
  color: rgba(var(--canvas-color-2), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-3 {
  background-color: rgba(var(--canvas-color-3), 0.2);
  color: rgba(var(--canvas-color-3), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-3.active {
  background-color: rgba(var(--canvas-color-3), 1);
  color: rgba(var(--canvas-color-3), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-4 {
  background-color: rgba(var(--canvas-color-4), 0.2);
  color: rgba(var(--canvas-color-4), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-4.active {
  background-color: rgba(var(--canvas-color-4), 1);
  color: rgba(var(--canvas-color-4), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-5 {
  background-color: rgba(var(--canvas-color-5), 0.2);
  color: rgba(var(--canvas-color-5), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-5.active {
  background-color: rgba(var(--canvas-color-5), 1);
  color: rgba(var(--canvas-color-5), 1);
}
.metadata-menu.modal-container .field-container .node-color.color-6 {
  background-color: rgba(var(--canvas-color-6), 0.2);
  color: rgba(var(--canvas-color-6), 0.2);
}
.metadata-menu.modal-container .field-container .node-color.color-6.active {
  background-color: rgba(var(--canvas-color-6), 1);
  color: rgba(var(--canvas-color-6), 1);
}
.metadata-menu.modal-container .field-container .node-color svg {
  vertical-align: bottom;
  stroke-width: 3px;
}
.metadata-menu.modal-container .field-container .node-color.picker {
  background: conic-gradient(var(--color-red), var(--color-yellow), var(--color-green), var(--color-blue), var(--color-purple), var(--color-red));
}
.metadata-menu.modal-container .field-container .node-color.picker input {
  opacity: 0;
  height: 2em;
  width: 2em;
}
.metadata-menu.modal-container .field-container .edge-side {
  display: flex;
}
.metadata-menu.modal-container .field-container .edge-side .side-icon svg {
  height: 22px;
  width: 22px;
  margin: 0 0.1em 0 0.7em;
}
.metadata-menu.modal-container .field-container.edges {
  margin-bottom: 0;
}
.metadata-menu.modal-container .field-container.colors {
  display: flex;
  flex-wrap: wrap;
}
.metadata-menu.modal-container .field-container.labels {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.metadata-menu.modal-container .field-container.labels .label {
  margin-right: 1em;
}
.metadata-menu.modal-container .field-container.labels .item {
  white-space: nowrap;
}
.metadata-menu.modal-container .field-container.labels .item.chip {
  padding-top: 0.3em;
  margin-left: 0em;
  margin-right: 0.3em;
  margin-top: 0.3em;
  background-color: var(--tag-background);
  border: 1px solid var(--tag-border-color);
  width: fit-content;
  display: flex;
  color: var(--text-muted);
  align-items: center;
}
.metadata-menu.modal-container .field-container.labels .item p {
  margin-block-end: 0;
  margin-block-start: 0;
}
.metadata-menu.modal-container .field-container.labels .item.spacer {
  width: 100%;
}
.metadata-menu.modal-container .field-container.labels .item.right-align {
  padding-right: 0;
}
.metadata-menu.modal-container .field-container.labels .item button {
  border: none;
  box-shadow: none;
  background: none;
  padding-right: 0;
}
.metadata-menu.modal-container .field-container.labels .item button:hover {
  color: var(--text-accent);
}
.metadata-menu.modal-container .field-container.labels .item button.item-remove:hover {
  color: var(--text-accent);
}
.metadata-menu.modal-container .field-container button {
  margin-right: 12px;
}
.metadata-menu.modal-container .field-container button :disabled {
  opacity: 50%;
}
.metadata-menu.modal-container .field-container button .danger {
  background-color: var(--color-purple);
  color: var(--color-base-00);
}
.metadata-menu.modal-container .field-container button:last-child {
  margin-right: 0;
}
.metadata-menu.modal-container .field-error {
  height: 100%;
  color: var(--text-on-accent);
  width: 100%;
  background-color: var(--color-accent);
  padding: var(--size-4-1) var(--size-4-2);
  font-size: var(--font-ui-small);
  border-radius: var(--input-radius);
}
.metadata-menu.modal-container .field-warning {
  font-size: smaller;
  background-color: var(--color-accent);
  color: var(--text-on-accent);
  padding: 0.5em;
  border-radius: 0.5em;
}
.metadata-menu.modal-container .footer-actions {
  display: flex;
  padding: var(--size-4-3);
  align-items: center;
}
.metadata-menu.modal-container .footer-actions .spacer {
  width: 100%;
}
.metadata-menu.modal-container .footer-actions .info {
  white-space: nowrap;
  margin-right: 1em;
  font-size: x-small;
  font-style: italic;
  color: var(--color-accent);
}
.metadata-menu.modal-container .footer-actions button {
  margin-right: 12px;
}
.metadata-menu.modal-container .footer-actions button :disabled {
  opacity: 50%;
}
.metadata-menu.modal-container .footer-actions button .danger {
  background-color: var(--color-purple);
  color: var(--color-base-00);
}
.metadata-menu.modal-container .footer-actions button:last-child {
  margin-right: 0;
}
.metadata-menu.modal-container.confirm-modal .field-name {
  color: var(--text-accent);
}
.metadata-menu.modal-container.confirm-modal .field-value {
  color: var(--text-accent);
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container {
  display: flex;
  align-items: center;
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container .file-name {
  font-weight: 600;
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container .position-icon {
  display: flex;
  align-items: center;
  margin: 0 0.375em;
  color: var(--text-muted);
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container .position-icon svg {
  height: 0.8em;
  width: 0.8em;
}
.metadata-menu.modal-container.confirm-modal .target-container .location-container .line-number {
  color: var(--text-muted);
  font-size: smaller;
}
.metadata-menu.modal-container.confirm-modal .target-container .current-value {
  color: var(--text-muted);
  font-size: smaller;
}
.metadata-menu.modal-container.confirm-modal .target-container .current-value.empty-or-missing {
  font-style: italic;
}

.sections .item {
  padding: 0;
}
.sections .item .line {
  display: flex;
}
.sections .item .line .lineNumber {
  font-size: small;
  color: var(--text-faint);
  margin-right: 1em;
}

.value-add-notice {
  display: flex;
  background-color: var(--color-accent);
  color: var(--text-on-accent);
}
.value-add-notice .label {
  margin: 0 1em;
  padding-top: 0.3em;
}
.value-add-notice .spacer {
  width: 100%;
}
.value-add-notice button {
  margin-right: 12px;
}
.value-add-notice button :disabled {
  opacity: 50%;
}
.value-add-notice button .danger {
  background-color: var(--color-purple);
  color: var(--color-base-00);
}
.value-add-notice button:last-child {
  margin-right: 0;
}

.suggester-input {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--background-secondary-alt);
  margin-top: 0.25em;
  border-color: rgba(var(--mono-rgb-100), 0.05);
  border-bottom: 1px solid var(--background-secondary);
}
.suggester-input .suggester-title {
  margin-left: 1em;
  white-space: nowrap;
}
.suggester-input .info {
  white-space: nowrap;
  margin-right: 1em;
  font-size: x-small;
  font-style: italic;
  color: var(--color-accent);
}
.suggester-input .prompt-input {
  border: none;
}
.suggester-input button {
  margin-right: 12px;
}
.suggester-input button :disabled {
  opacity: 50%;
}
.suggester-input button .danger {
  background-color: var(--color-purple);
  color: var(--color-base-00);
}
.suggester-input button:last-child {
  margin-right: 0;
}
.suggester-input button:first-child {
  margin-left: 1em;
}
.suggester-input button:last-child {
  margin-right: 1em;
}
.suggester-input .input-as-title {
  font-weight: 500;
}

.value-container {
  display: flex;
  white-space: nowrap;
  align-items: center;
}
.value-container button {
  margin: 0 0.3em;
}
.value-container button.left {
  margin-left: 0;
}
.value-container button.right {
  margin-right: 0;
}
.value-container button.small {
  height: 1.5em;
  width: 1.5em;
  padding: 0.3em;
  border-radius: 0.75em;
  margin-right: 0.5em;
}
.value-container button.small.active {
  background-color: var(--color-accent);
  color: var(--text-on-accent);
}
.value-container button.small:focus {
  box-shadow: none;
}
.value-container button.small:hover {
  box-shadow: 0 0 0 1px var(--interactive-accent);
}
.value-container .spacer {
  width: 100%;
}
.value-container .index-container {
  margin: 0em 0.7em 0em 0em;
  display: block;
  background-color: var(--color-accent);
  color: var(--text-on-accent);
  border-radius: 0.75em;
  font-size: x-small;
  min-width: 1.5em;
  text-align: center;
}
.value-container .label-container {
  margin-right: 0.5em;
  font-weight: 600;
}
.value-container .label-with-icon-container {
  display: inline-flex;
}
.value-container .label-with-icon-container .icon {
  margin-right: 0.5em;
}
.value-container .icon-container {
  margin: 0em 0.7em 0em 0em;
  display: block;
}
.value-container .icon-container svg {
  vertical-align: text-bottom;
}
.value-container.value-checked {
  background-color: var(--background-modifier-hover);
}
.value-container.value-checked.is-selected {
  background-color: var(--interactive-accent);
}
.value-container .item-with-add-on {
  display: flex;
}
.value-container .item-with-add-on .add-on {
  font-size: x-small;
  margin-left: 1em;
  padding-top: 0.4em;
  color: var(--color-base-60);
}
.value-container .empty {
  color: var(--text-faint);
}
.value-container .media-item {
  display: flex;
  align-items: center;
}
.value-container .media-item .media-info-container {
  display: flex;
}
.value-container .media-item .thumbnail-container {
  color: var(--text-faint);
}
.value-container .media-item .thumbnail-container img.thumbnail {
  width: 100%;
}

.suggestion-item.suggest-separator {
  border-bottom: 1px solid var(--divider-color);
  border-radius: 0px;
  padding: 1px;
}

.prompt.media-as-cards {
  width: 100%;
}
.prompt.media-as-cards .prompt-results {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;
}
@media screen and (max-width: 400px) {
  .prompt.media-as-cards .prompt-results {
    grid-template-columns: auto;
  }
}
@media (max-width: 780px) {
  .prompt.media-as-cards .prompt-results {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 780px) and (max-width: 1020px) {
  .prompt.media-as-cards .prompt-results {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 1020px) and (max-width: 1280px) {
  .prompt.media-as-cards .prompt-results {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (min-width: 1280px) {
  .prompt.media-as-cards .prompt-results {
    grid-template-columns: repeat(5, 1fr);
  }
}
.prompt.media-as-cards .prompt-results .value-container {
  align-items: flex-end;
  justify-content: center;
}
.prompt.media-as-cards .prompt-results .value-container.value-checked {
  border: 1px solid var(--color-accent);
}
.prompt.media-as-cards .prompt-results .value-container .media-item {
  flex-direction: column;
}
.prompt.media-as-cards .prompt-results .value-container .media-item .file-name {
  width: 100%;
  text-align: center;
  font-size: smaller;
}

.metadata-menu.settings .settings-info-warning {
  color: var(--text-warning);
}
.metadata-menu.settings .setting-divider {
  padding: 0.75em 0;
  border-bottom: 1px solid var(--background-modifier-border);
}
.metadata-menu.settings .header-container {
  display: flex;
}
.metadata-menu.settings .header-container .text-container {
  width: 100%;
}
.metadata-menu.settings .header-container .text-container .section-header {
  margin-bottom: 0;
}
.metadata-menu.settings .header-container .text-container .setting-item-description {
  margin-bottom: 1.33em;
}
.metadata-menu.settings .fields-container {
  display: grid;
  padding-top: 0.5em;
}
.metadata-menu.settings .fields-container .setting-item .setting-item-info .name {
  font-weight: 500;
  white-space: nowrap;
  display: inline-flex;
}
.metadata-menu.settings .fields-container .setting-item .setting-item-info .name .indentation {
  width: 1em;
  color: var(--text-faint);
  margin-right: 0.3em;
}
.metadata-menu.settings .fields-container .setting-item .setting-item-info .spacer {
  width: 100%;
}
.metadata-menu.settings .setting-item .icon {
  margin-left: 1em;
}
.metadata-menu.settings .setting-item .setting-item-info.with-button {
  display: grid;
  grid-template-columns: auto 1fr auto;
  width: 100%;
}
.metadata-menu.settings .setting-item .setting-item-info.with-button .setting-item-info-text {
  grid: 1;
}
.metadata-menu.settings .setting-item .setting-item-info.with-button .spacer {
  grid: 2;
  width: 100%;
}
.metadata-menu.settings .setting-item .setting-item-info.with-button button {
  grid: 3;
  max-width: 3.25em;
}
.metadata-menu.settings .setting-item .setting-item-info .setting-item-description {
  font-weight: 500;
}
.metadata-menu.settings .setting-item .setting-item-info .setting-item-name {
  font-weight: 500;
  white-space: nowrap;
}
.metadata-menu.settings .setting-item.vstacked {
  display: grid;
  margin: 1em 0;
}
.metadata-menu.settings .setting-item .full-width textarea, .metadata-menu.settings .setting-item .full-width input, .metadata-menu.settings .setting-item .full-width .search-input-container {
  width: 100%;
}
.metadata-menu.settings .setting-item.no-border {
  border: none;
}
.metadata-menu.settings .setting-item.narrow-title .setting-item-info {
  flex: 2;
}
.metadata-menu.settings .setting-item.narrow-title .setting-item-control {
  flex: 2;
}
.metadata-menu.settings .setting-item .save {
  margin-left: 1em;
}

.metadata-menu.suggester .item-with-alias {
  display: flex;
}
.metadata-menu.suggester .item-with-alias-filepath {
  font-size: x-small;
  margin-left: 1em;
  padding-top: 0.4em;
  color: var(--color-base-60);
}

.fileClass-add-button {
  color: var(--text-accent);
  padding: 0 0.5em;
  display: flex;
}

.edit-block-button.fileclass-codeblock-icon {
  right: calc(var(--size-2-2) + 2em);
}

.metadata-menu.fileclass-view .fv-menu, .metadata-menu.fileclass-codeblock-view .fv-menu {
  display: flex;
}
.metadata-menu.fileclass-view .fv-menu .fv-menu-item, .metadata-menu.fileclass-codeblock-view .fv-menu .fv-menu-item {
  display: flex;
  margin: 0em 1em 0.5em 0em;
  color: var(--text-muted);
}
.metadata-menu.fileclass-view .fv-menu .fv-menu-item h2, .metadata-menu.fileclass-codeblock-view .fv-menu .fv-menu-item h2 {
  margin-top: 0em;
  margin-block-end: 0em;
}
.metadata-menu.fileclass-view .fv-menu .fv-menu-item.active, .metadata-menu.fileclass-codeblock-view .fv-menu .fv-menu-item.active {
  color: var(--text-accent);
  border-bottom: 3px solid var(--text-accent);
}
.metadata-menu.fileclass-view .view-container, .metadata-menu.fileclass-codeblock-view .view-container {
  display: flex;
  flex-flow: column;
  height: 95%;
}
.metadata-menu.fileclass-view .fv-fields .fields-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container {
  display: grid;
  grid-template-columns: auto auto auto 1fr;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-fields .fields-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container {
    grid-template-columns: auto auto auto;
  }
}
.metadata-menu.fileclass-view .fv-fields .fields-container .name-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .name-container {
  grid-column: 1;
  margin: 0.2em 1.3em 0.2em 0;
  white-space: nowrap;
  font-weight: 500;
  display: inline-flex;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-fields .fields-container .name-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .name-container {
    margin-top: 1em;
  }
}
.metadata-menu.fileclass-view .fv-fields .fields-container .name-container .indentation, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .name-container .indentation {
  width: 1em;
  color: var(--text-faint);
  margin-right: 0.3em;
}
.metadata-menu.fileclass-view .fv-fields .fields-container .type-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .type-container {
  grid-column: 2;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-fields .fields-container .type-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .type-container {
    margin-top: 1em;
  }
}
.metadata-menu.fileclass-view .fv-fields .fields-container .type-container .chip, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .type-container .chip {
  border-radius: 0.8em;
  padding: 0.1em 0.75em 0.25em 0.75em;
  color: white;
  margin-left: 0.5em;
  margin-right: 0.5em;
  height: 1.6em;
  white-space: nowrap;
  font-size: small;
  margin-top: 0.25em;
  opacity: 0.95;
}
.metadata-menu.fileclass-view .fv-fields .fields-container .buttons-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .buttons-container {
  grid-column: 3;
  display: flex;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-fields .fields-container .buttons-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .buttons-container {
    margin-top: 1em;
  }
}
.metadata-menu.fileclass-view .fv-fields .fields-container .options-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .options-container {
  grid-column: 4;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-fields .fields-container .options-container, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .options-container {
    grid-column: 1;
    grid-column-start: span 3;
  }
}
.metadata-menu.fileclass-view .fv-fields .fields-container .options-container .description, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .options-container .description {
  white-space: nowrap;
  margin-left: 1em;
  color: var(--text-muted);
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-fields .fields-container .options-container .description, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container .options-container .description {
    margin-left: 0em;
    white-space: normal;
  }
}
.metadata-menu.fileclass-view .fv-fields .fields-container button, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container button {
  border: none;
  box-shadow: none;
  background: none;
}
.metadata-menu.fileclass-view .fv-fields .fields-container button:hover, .metadata-menu.fileclass-codeblock-view .fv-fields .fields-container button:hover {
  color: var(--text-accent);
}
.metadata-menu.fileclass-view .fv-fields .footer, .metadata-menu.fileclass-codeblock-view .fv-fields .footer {
  display: flex;
}
.metadata-menu.fileclass-view .fv-fields .footer .cell button, .metadata-menu.fileclass-codeblock-view .fv-fields .footer .cell button {
  margin-top: 1em;
  margin-right: 0.3em;
}
.metadata-menu.fileclass-view .fv-fields .footer .cell button.favorite-button.favorite, .metadata-menu.fileclass-codeblock-view .fv-fields .footer .cell button.favorite-button.favorite {
  color: var(--color-orange);
}
.metadata-menu.fileclass-view .fv-fields .footer .cell button.favorite-button:disabled, .metadata-menu.fileclass-codeblock-view .fv-fields .footer .cell button.favorite-button:disabled {
  color: var(--text-faint);
}
.metadata-menu.fileclass-view .fv-fields .footer .cell button.remove-button, .metadata-menu.fileclass-codeblock-view .fv-fields .footer .cell button.remove-button {
  color: var(--text-error);
}
.metadata-menu.fileclass-view .fv-fields .footer .cell button.active, .metadata-menu.fileclass-codeblock-view .fv-fields .footer .cell button.active {
  background-color: var(--color-accent);
  border-bottom: 3px solid var(--text-accent);
  color: var(--text-on-accent);
}
.metadata-menu.fileclass-view .fv-fields .footer .cell select, .metadata-menu.fileclass-codeblock-view .fv-fields .footer .cell select {
  margin-top: 1em;
  margin-right: 0.3em;
}
.metadata-menu.fileclass-view .fv-settings .settings-container, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container {
  display: grid;
  grid-template-columns: auto auto auto 1fr;
  align-items: center;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-settings .settings-container, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container {
    grid-template-columns: 1fr auto;
  }
}
.metadata-menu.fileclass-view .fv-settings .settings-container .label, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .label {
  grid-column: 1;
  margin: 0.2em 1.3em 0.2em 0;
  white-space: nowrap;
  font-weight: 500;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-settings .settings-container .label, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .label {
    margin-top: 1em;
  }
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action {
  grid-column: 3;
  margin: 0.2em 0em 0.2em 0;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-settings .settings-container .action, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action {
    grid-column: 1;
    grid-column-start: span 2;
  }
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action button, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action button {
  border: none;
  box-shadow: none;
  background: none;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action button:hover, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action button:hover {
  color: var(--text-accent);
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .checkbox-container, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .checkbox-container {
  vertical-align: top;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .icon-manager, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .icon-manager {
  display: flex;
  align-items: end;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .icon-manager input, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .icon-manager input {
  margin-right: 0.5em;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .items, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .items {
  display: flex;
  align-items: center;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .items .item, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .items .item {
  white-space: nowrap;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .items .item.chip, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .items .item.chip {
  padding-top: 0.3em;
  margin-left: 0em;
  margin-right: 0.3em;
  background-color: var(--tag-background);
  border: 1px solid var(--tag-border-color);
  width: fit-content;
  display: flex;
  color: var(--text-muted);
  align-items: center;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .items .item.chip .item-remove:hover, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .items .item.chip .item-remove:hover {
  color: var(--text-accent);
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .items .item button, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .items .item button {
  padding-right: 0;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .items .item p, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .items .item p {
  margin-block-end: 0;
  margin-block-start: 0;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .items .item.spacer, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .items .item.spacer {
  width: 100%;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .action .items .item.right-align, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .action .items .item.right-align {
  padding-right: 0;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .tooltip-btn, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .tooltip-btn {
  grid-column: 2;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-settings .settings-container .tooltip-btn, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .tooltip-btn {
    margin-top: 1em;
  }
}
.metadata-menu.fileclass-view .fv-settings .settings-container .tooltip-btn .tooltip-button, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .tooltip-btn .tooltip-button {
  background: none;
  box-shadow: none;
  border: none;
  vertical-align: middle;
  color: var(--icon-color);
  padding-right: 1em;
  margin-left: 1em;
}
.metadata-menu.fileclass-view .fv-settings .settings-container .tooltip-text, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .tooltip-text {
  grid-column: 4;
  color: var(--text-muted);
  font-size: smaller;
  line-break: strict;
  line-height: normal;
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-settings .settings-container .tooltip-text, .metadata-menu.fileclass-codeblock-view .fv-settings .settings-container .tooltip-text {
    grid-column: 1;
    grid-column-start: span 2;
  }
}
.metadata-menu.fileclass-view .fv-settings .footer, .metadata-menu.fileclass-codeblock-view .fv-settings .footer {
  display: flex;
}
.metadata-menu.fileclass-view .fv-settings .footer .cell button, .metadata-menu.fileclass-codeblock-view .fv-settings .footer .cell button {
  margin-top: 1em;
  margin-right: 0.3em;
}
.metadata-menu.fileclass-view .fv-settings .footer .cell button.favorite-button.favorite, .metadata-menu.fileclass-codeblock-view .fv-settings .footer .cell button.favorite-button.favorite {
  color: var(--color-orange);
}
.metadata-menu.fileclass-view .fv-settings .footer .cell button.favorite-button:disabled, .metadata-menu.fileclass-codeblock-view .fv-settings .footer .cell button.favorite-button:disabled {
  color: var(--text-faint);
}
.metadata-menu.fileclass-view .fv-settings .footer .cell button.remove-button, .metadata-menu.fileclass-codeblock-view .fv-settings .footer .cell button.remove-button {
  color: var(--text-error);
}
.metadata-menu.fileclass-view .fv-settings .footer .cell button.active, .metadata-menu.fileclass-codeblock-view .fv-settings .footer .cell button.active {
  background-color: var(--color-accent);
  border-bottom: 3px solid var(--text-accent);
  color: var(--text-on-accent);
}
.metadata-menu.fileclass-view .fv-settings .footer .cell select, .metadata-menu.fileclass-codeblock-view .fv-settings .footer .cell select {
  margin-top: 1em;
  margin-right: 0.3em;
}
.metadata-menu.fileclass-view .fv-table, .metadata-menu.fileclass-codeblock-view .fv-table {
  display: contents;
  /*
  .table-view-table {
      @media screen and (max-width: 390px) {
          width: 300px;
      }
  }
  */
}
@media screen and (max-width: 400px) {
  .metadata-menu.fileclass-view .fv-table .options, .metadata-menu.fileclass-codeblock-view .fv-table .options {
    max-height: 50em;
    overflow: scroll;
  }
}
.metadata-menu.fileclass-view .fv-table .options .limit, .metadata-menu.fileclass-codeblock-view .fv-table .options .limit {
  display: flex;
  font-weight: 600;
  align-items: baseline;
}
.metadata-menu.fileclass-view .fv-table .options .pagination, .metadata-menu.fileclass-codeblock-view .fv-table .options .pagination {
  display: flex;
  margin: 0.5em 0;
  flex-wrap: wrap;
}
.metadata-menu.fileclass-view .fv-table .options .pagination .range, .metadata-menu.fileclass-codeblock-view .fv-table .options .pagination .range {
  padding: 0 0.75em;
  margin-right: 0.2em;
}
.metadata-menu.fileclass-view .fv-table .options .pagination .range.active, .metadata-menu.fileclass-codeblock-view .fv-table .options .pagination .range.active {
  background-color: var(--color-accent);
  color: var(--text-on-accent);
  border-radius: 0.75em;
}
.metadata-menu.fileclass-view .fv-table .options .pagination .range:hover, .metadata-menu.fileclass-codeblock-view .fv-table .options .pagination .range:hover {
  background-color: var(--color-base-35);
  color: var(---text-on-accent);
  border-radius: 0.75em;
}
.metadata-menu.fileclass-view .fv-table .options .fields, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields {
  display: flex;
  flex-wrap: wrap;
  row-gap: 0.5em;
  column-gap: 0.5em;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container {
  padding: var(--button-radius);
  background-color: var(--odd-color);
  border-radius: var(--button-radius);
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container button, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container button {
  height: 1.5em;
  width: 1.5em;
  padding: 0.3em;
  border-radius: 0.75em;
  margin-right: 0.5em;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container button.active, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container button.active {
  background-color: var(--color-accent);
  color: var(--text-on-accent);
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container button:focus, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container button:focus {
  box-shadow: none;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container button:hover, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container button:hover {
  box-shadow: 0 0 0 1px var(--interactive-accent);
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .field-header, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .field-header {
  display: flex;
  align-items: center;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .field-header .label-container, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .field-header .label-container {
  display: flex;
  align-items: center;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .field-header .label-container .field-name, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .field-header .label-container .field-name {
  font-weight: 600;
  white-space: nowrap;
  margin-right: 0.375em;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .field-header .label-container .priority, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .field-header .label-container .priority {
  font-weight: 300;
  font-size: small;
  color: var(--text-muted);
  margin-right: 0.375em;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input {
  display: flex;
  margin-top: 0.5em;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input input, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input input {
  width: 100%;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input input:focus, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input input:focus {
  outline: none;
  box-shadow: none;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input select, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input select {
  width: 100%;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown {
  flex-direction: row;
  border: var(--input-border-width) solid var(--background-modifier-border);
  border-radius: var(--input-radius);
  align-items: center;
  background-color: var(--background-modifier-form-field);
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown input, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown input {
  flex-grow: 2;
  border: none;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown input:focus-within, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown input:focus-within {
  border-color: none;
  transition: none;
}
.metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown:hover, .metadata-menu.fileclass-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown:focus-within, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown:hover, .metadata-menu.fileclass-codeblock-view .fv-table .options .fields .field-container .filter-input.filter-with-dropdown:focus-within {
  border-color: var(--background-modifier-border-hover);
  transition: box-shadow 0.15s ease-in-out, border 0.15s ease-in-out;
}
.metadata-menu.fileclass-view .fv-table .options .footer, .metadata-menu.fileclass-codeblock-view .fv-table .options .footer {
  display: flex;
}
.metadata-menu.fileclass-view .fv-table .options .footer .cell button, .metadata-menu.fileclass-codeblock-view .fv-table .options .footer .cell button {
  margin-top: 1em;
  margin-right: 0.3em;
}
.metadata-menu.fileclass-view .fv-table .options .footer .cell button.favorite-button.favorite, .metadata-menu.fileclass-codeblock-view .fv-table .options .footer .cell button.favorite-button.favorite {
  color: var(--color-orange);
}
.metadata-menu.fileclass-view .fv-table .options .footer .cell button.favorite-button:disabled, .metadata-menu.fileclass-codeblock-view .fv-table .options .footer .cell button.favorite-button:disabled {
  color: var(--text-faint);
}
.metadata-menu.fileclass-view .fv-table .options .footer .cell button.remove-button, .metadata-menu.fileclass-codeblock-view .fv-table .options .footer .cell button.remove-button {
  color: var(--text-error);
}
.metadata-menu.fileclass-view .fv-table .options .footer .cell button.active, .metadata-menu.fileclass-codeblock-view .fv-table .options .footer .cell button.active {
  background-color: var(--color-accent);
  border-bottom: 3px solid var(--text-accent);
  color: var(--text-on-accent);
}
.metadata-menu.fileclass-view .fv-table .options .footer .cell select, .metadata-menu.fileclass-codeblock-view .fv-table .options .footer .cell select {
  margin-top: 1em;
  margin-right: 0.3em;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container], .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] {
  overflow: scroll;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .first-col-header-cell, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .first-col-header-cell {
  padding-left: var(--table-cell-padding);
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .first-col-header-cell span:first-child, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .first-col-header-cell span:first-child {
  margin-left: 0;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .first-col-header-cell .first-col-header-container, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .first-col-header-cell .first-col-header-container {
  display: flex;
  width: 100%;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .first-col-header-cell .first-col-header-container .fileclass-icon, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .first-col-header-cell .first-col-header-container .fileclass-icon {
  margin: 0 0.3em;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .first-col-header-cell .first-col-header-container .fileclass-icon svg, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .first-col-header-cell .first-col-header-container .fileclass-icon svg {
  height: calc(var(--icon-size) * 0.935);
  width: calc(var(--icon-size) * 0.935);
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .field-name .field-sub-container, .metadata-menu.fileclass-view .fv-table div[id^=table-container] .value-container .field-sub-container, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .field-name .field-sub-container, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .value-container .field-sub-container {
  display: flex;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .field-name .field-sub-container p, .metadata-menu.fileclass-view .fv-table div[id^=table-container] .value-container .field-sub-container p, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .field-name .field-sub-container p, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .value-container .field-sub-container p {
  margin-block: 0em;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .field-name span, .metadata-menu.fileclass-view .fv-table div[id^=table-container] .value-container span, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .field-name span, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .value-container span {
  display: grid;
  grid-template-columns: auto auto;
  float: left;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] .field-name span a.internal-link, .metadata-menu.fileclass-view .fv-table div[id^=table-container] .value-container span a.internal-link, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .field-name span a.internal-link, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] .value-container span a.internal-link {
  grid-column: 2;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th {
  position: sticky;
  top: 0;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th.table-view-th:first-child, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th.table-view-th:first-child {
  z-index: 2;
  display: flex;
  height: var(--line-height);
  width: 100%;
  align-items: center;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th.table-view-th:first-child .checkbox-toggler, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th.table-view-th:first-child .checkbox-toggler {
  display: flex;
  align-items: center;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th.table-view-th:first-child.first-col-header-cell, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th.table-view-th:first-child.first-col-header-cell {
  left: 0;
  padding: var(--table-cell-padding);
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th.table-view-th:first-child .spacer, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th.table-view-th:first-child .spacer {
  width: 100%;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th.table-view-th:first-child span:nth-child(2), .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th.table-view-th:first-child span:nth-child(2) {
  margin-left: 0.5em;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th.table-view-th:first-child.modifier-selector, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th.table-view-th:first-child.modifier-selector {
  min-width: 0;
  margin: 0;
  padding-right: 0;
  text-align: center;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th.table-view-th:first-child.modifier-selector input, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th.table-view-th:first-child.modifier-selector input {
  vertical-align: middle;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th:first-child.table-view-th.first-col-header-cell, .metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th.table-view-th.first-col-header-cell, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th:first-child.table-view-th.first-col-header-cell, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th.table-view-th.first-col-header-cell {
  padding-left: 0;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th:first-child span.dataview.small-text, .metadata-menu.fileclass-view .fv-table div[id^=table-container] thead th span.dataview.small-text, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th:first-child span.dataview.small-text, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] thead th span.dataview.small-text {
  display: none;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] tbody > tr > td:first-child, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] tbody > tr > td:first-child {
  position: sticky;
  left: 0;
  z-index: 1;
  max-width: 100%;
  background-color: inherit;
  display: flex;
  height: var(--line-height);
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] tbody > tr > td:first-child.modifier-selector, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] tbody > tr > td:first-child.modifier-selector {
  min-width: 0;
  margin: 0;
  padding-right: 0;
  text-align: center;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] tbody > tr > td:first-child.modifier-selector input, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] tbody > tr > td:first-child.modifier-selector input {
  vertical-align: middle;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] tbody > tr > td:nth-child(2), .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] tbody > tr > td:nth-child(2) {
  padding-left: 0;
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] tbody > tr:nth-child(odd):hover > td:first-child, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] tbody > tr:nth-child(odd):hover > td:first-child {
  background-color: var(--odd-color);
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container] tbody > tr:nth-child(even):hover > td:first-child, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container] tbody > tr:nth-child(even):hover > td:first-child {
  background-color: var(--background-primary);
}
@media screen and (max-width: 390px) {
  .metadata-menu.fileclass-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child {
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: inherit;
  }
  .metadata-menu.fileclass-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child a.internal-link, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child a.internal-link {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 6em;
  }
  .metadata-menu.fileclass-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child .field-name span, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child .field-name span {
    display: grid;
    grid-template-columns: auto auto;
    float: left;
  }
  .metadata-menu.fileclass-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child .field-name span a.internal-link, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child .field-name span a.internal-link {
    grid-column: 2;
  }
  .metadata-menu.fileclass-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child .field-name span a.metadatamenu.fileclass-icon, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container].scrolled tbody > tr > td:first-child .field-name span a.metadatamenu.fileclass-icon {
    grid-area: 1;
    margin-right: 0.375em;
  }
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container].scrolled tbody > tr:nth-child(even):hover > td:first-child, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container].scrolled tbody > tr:nth-child(even):hover > td:first-child {
  background-color: var(--background-primary);
}
.metadata-menu.fileclass-view .fv-table div[id^=table-container].scrolled tbody > tr:nth-child(odd):hover > td:first-child, .metadata-menu.fileclass-codeblock-view .fv-table div[id^=table-container].scrolled tbody > tr:nth-child(odd):hover > td:first-child {
  background-color: var(--odd-color);
}

.field-btn-container {
  display: flex;
  align-items: flex-end;
}
.field-btn-container button.property-metadata-menu {
  margin: 0.15em 0;
  border: none;
  color: var(--text-accent);
  box-shadow: none;
  padding: 0 0.5em;
  height: calc(var(--input-height) * 0.875);
  width: calc(var(--input-height) * 0.875);
}
.field-btn-container button.property-metadata-menu svg {
  vertical-align: calc(var(--font-text-size) * 0.15);
  height: calc(var(--icon-size) * 0.935);
  width: calc(var(--icon-size) * 0.935);
}
.field-btn-container.with-bottom-border {
  align-self: stretch;
  align-items: flex-start;
  flex-direction: row;
  flex-shrink: 0;
  border-bottom: var(--metadata-divider-width) solid var(--metadata-divider-color);
  background-color: var(--metadata-label-background);
}

.metadata-content .action-container {
  display: flex;
  align-items: flex-end;
}
.metadata-content .action-container .fileclass-btn-container {
  display: flex;
  align-items: flex-end;
}
.metadata-content .action-container .fileclass-btn-container .add-field-button {
  border: none;
  color: var(--text-accent);
  box-shadow: none;
  padding: 0 0.5em;
  margin-left: 0.2em;
}
