/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin (https://github.com/phibr0/obsidian-commander)
*/

var Oe=Object.defineProperty,Rn=Object.defineProperties,zn=Object.getOwnPropertyDescriptor,On=Object.getOwnPropertyDescriptors,Fn=Object.getOwnPropertyNames,ze=Object.getOwnPropertySymbols;var vt=Object.prototype.hasOwnProperty,$t=Object.prototype.propertyIsEnumerable;var Yt=(t,o,e)=>o in t?Oe(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,Fe=(t,o)=>{for(var e in o||(o={}))vt.call(o,e)&&Yt(t,e,o[e]);if(ze)for(var e of ze(o))$t.call(o,e)&&Yt(t,e,o[e]);return t},Gt=(t,o)=>Rn(t,On(o));var Kt=(t,o)=>{var e={};for(var n in t)vt.call(t,n)&&o.indexOf(n)<0&&(e[n]=t[n]);if(t!=null&&ze)for(var n of ze(t))o.indexOf(n)<0&&$t.call(t,n)&&(e[n]=t[n]);return e};var Vn=(t,o)=>{for(var e in o)Oe(t,e,{get:o[e],enumerable:!0})},Wn=(t,o,e,n)=>{if(o&&typeof o=="object"||typeof o=="function")for(let a of Fn(o))!vt.call(t,a)&&a!==e&&Oe(t,a,{get:()=>o[a],enumerable:!(n=zn(o,a))||n.enumerable});return t};var jn=t=>Wn(Oe({},"__esModule",{value:!0}),t);var Va={};Vn(Va,{default:()=>lt});module.exports=jn(Va);var Ve=require("obsidian");var Ct=require("obsidian");var Qt={};var eo={"Open Commander Settings":"Otev\u0159\xEDt nastaven\xED Commandera","Open Macro Builder":"Otev\u0159\xEDt tv\u016Frce Maker","Change Icon":"Zm\u011Bnit ikonu",Rename:"P\u0159ejmenovat",Delete:"Smazat","Add command":"P\u0159idat p\u0159\xEDkaz","Add new":"P\u0159idat nov\xFD","This Command seems to have been removed. {{command_name}}":"Tento p\u0159\xEDkaz se zd\xE1 b\xFDt odstran\u011Bn. {{command_name}}","Choose a Command to add":"Vyberte p\u0159\xEDkaz k p\u0159id\xE1n\xED","to navigate":"pro navigaci","to choose an icon":"pro v\xFDb\u011Br ikony","to cancel":"pro zru\u0161en\xED","Use a custom name":"Pou\u017E\xEDt vlastn\xED jm\xE9no","Choose a custom Name for your new Command":"Vyberte vlastn\xED jm\xE9no pro v\xE1\u0161 nov\xFD p\u0159\xEDkaz","to save":"pro ulo\u017Een\xED","Choose a Icon for your new Command":"Vyberte ikonu pro v\xE1\u0161 nov\xFD p\u0159\xEDkaz","to choose a custom icon":"pro v\xFDb\u011Br vlastn\xED ikony","Remove Command":"Odstranit p\u0159\xEDkaz","Double click to rename":"Pro p\u0159ejmenov\xE1n\xED dvakr\xE1t klikn\u011Bte","This device":"Toto za\u0159\xEDzen\xED","Added by {{plugin_name}}.":"P\u0159id\xE1no pomoc\xED {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Varov\xE1n\xED: Tento p\u0159\xEDkaz je kontrolov\xE1n a nemus\xED fungovat ve v\u0161ech p\u0159\xEDpadech.","Move down":"P\u0159esunout dol\u016F","Move up":"P\u0159esunout nahoru","Change Mode (Currently: {{current_mode}})":"Zm\u011Bnit re\u017Eim (Nyn\xED: {{current_mode}})","Are you sure you want to delete the Command?":"Opravdu chcete smazat tento p\u0159\xEDkaz?","Remove and don't ask again":"Odstranit a u\u017E se neptat",Remove:"Odstranit",Cancel:"Zru\u0161it","Always ask before removing?":"V\u017Edy se pt\xE1t p\u0159ed odstran\u011Bn\xEDm?","Always show a Popup to confirm deletion of a Command.":"V\u017Edy zobrazovat vyskakovac\xED okno pro potvrzen\xED odstran\u011Bn\xED p\u0159\xEDkazu.",'Show "Add Command" Button':'Zobrazit tla\u010D\xEDtko "P\u0159idat p\u0159\xEDkaz"','Show the "Add Command" Button in every Menu. Requires restart.':'Zobrazit tla\u010D\xEDtko "P\u0159idat p\u0159\xEDkaz" ve v\u0161ech nab\xEDdk\xE1ch. Vy\u017Eaduje restart.',"Please restart Obsidian for these changes to take effect.":"Pros\xEDm restartujte Obsidian, aby se zm\u011Bny projevily.","Enable debugging":"Povolit lad\u011Bn\xED","Enable console output.":"Povolit v\xFDstup do konzole.",General:"Obecn\xE9","Editor Menu":"Kontextov\xE1 nab\xEDdka v editoru","File Menu":"Nab\xEDdka souboru","Left Ribbon":"Lev\xFD Ribbon","Right Ribbon":"Prav\xFD Ribbon",Titlebar:"Li\u0161ta aplikace",Statusbar:"Stavov\xE1 li\u0161ta","Page Header":"Hlavi\u010Dka str\xE1nky","Support development":"Podpo\u0159te v\xFDvoj","No commands here!":"Nejsou zde \u017E\xE1dn\xE9 p\u0159\xEDkazy!","Would you like to add one now?":"Chcete nyn\xED jeden p\u0159idat?","Hide Commands":"Skr\xFDt p\u0159\xEDkazy","Choose new":"Vyberte nov\xFD","Hide Commands of other Plugins":"Skr\xFDt p\u0159\xEDkazy jin\xFDch roz\u0161\xED\u0159en\xED",Icon:"Ikona",Name:"N\xE1zev","Custom Name":"Vlastn\xED n\xE1zev","Add command to all devices":"P\u0159idat p\u0159\xEDkaz na v\u0161echna za\u0159\xEDzen\xED","Add command only to mobile devices":"P\u0159idat p\u0159\xEDkaz pouze na mobiln\xED za\u0159\xEDzen\xED","Add command only to desktop devices":"P\u0159idat p\u0159\xEDkaz pouze na stoln\xED za\u0159\xEDzen\xED","Add command only to this device":"P\u0159idat p\u0159\xEDkaz pouze na toto za\u0159\xEDzen\xED",Done:"Hotovo","By Johnny\u2728 and phibr0":"Vytvo\u0159il Johnny\u2728 a phibr0","Leave feedback":"Zanechat zp\u011Btnou vazbu",Donate:"P\u0159isp\u011Bt","Share feedback, issues, and ideas with our feedback form.":"Sd\xEDlejte zp\u011Btnou vazbu, probl\xE9my a n\xE1pady pomoc\xED na\u0161eho formul\xE1\u0159e.","Consider donating to support development.":"Zva\u017Ete p\u0159\xEDsp\u011Bvek na podporu v\xFDvoje.",Save:"Ulo\u017Eit","This Command is not available on this device.":"Tento p\u0159\xEDkaz nen\xED dostupn\xFD na tomto za\u0159\xEDzen\xED.",Show:"Zobrazit",Hide:"Skr\xFDt","Hide other Commands":"Skr\xFDt ostatn\xED p\u0159\xEDkazy","Double click to enter custom value":"Dvakr\xE1t klikn\u011Bte pro zad\xE1n\xED vlastn\xED hodnoty","Choose custom spacing for Command Buttons":"Vyberte vlastn\xED odsazen\xED pro tla\u010D\xEDtka p\u0159\xEDkaz\u016F","Change the spacing between commands. You can set different values on mobile and desktop.":"Zm\u011Bna odsazen\xED mezi p\u0159\xEDkazy. M\u016F\u017Eete nastavit r\u016Fzn\xE9 hodnoty na mobiln\xEDch a stoln\xEDch za\u0159\xEDzen\xEDch.",Warning:"Varov\xE1n\xED","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"Od verze Obsidian 0.16.0 je nutn\xE9 explicitn\u011B povolit z\xE1hlav\xED zobrazen\xED. Po povolen\xED je mo\u017En\xE9, \u017Ee budete muset restartovat Obsidian.","Open Appearance Settings":"Otev\u0159\xEDt nastaven\xED vzhledu",Explorer:"Pr\u016Fzkumn\xEDk"};var to={};var oo={"Open Commander Settings":"Commander Einstellungen \xF6ffnen","Open Macro Builder":"Makro Baukasten \xF6ffnen","Change Icon":"Symbol ver\xE4ndern",Rename:"Umbenennen",Delete:"L\xF6schen","Add command":"Befehl hinzuf\xFCgen","Add new":"Neuen Befehl hinzuf\xFCgen","This Command seems to have been removed. {{command_name}}":"Dieser Befehl wurde entfernt. {{command_name}}","Choose a Command to add":"W\xE4hle einen Befehl zum hinzuf\xFCgen","to navigate":"zum navigieren","to choose an icon":"um ein symbol auszuw\xE4hlen","to cancel":"zum abbrechen","Use a custom name":"Nutze einen benutzerdefinierten Namen","Choose a custom Name for your new Command":"W\xE4hle einen benutzerdefinierten Namen f\xFCr deinen neuen Befehl","to save":"zum speichern","Choose a Icon for your new Command":"W\xE4hle ein Symbol f\xFCr deinen neuen Befehl","to choose a custom icon":"um ein benutzerdefiniertes Symbol auszuw\xE4hlen","Remove Command":"Befehl entfernen","Double click to rename":"Zum umbenennen doppelklicken","This device":"Dieses Ger\xE4t","Added by {{plugin_name}}.":"Hinzugef\xFCgt von {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Warnung: Dieser Befehl wird nur unter bestimmten Vorraussetzungen ausgef\xFChrt.","Move down":"Nach unten","Move up":"Nach oben","Change Mode (Currently: {{current_mode}})":"Modus ver\xE4ndern (Momentan: {{current_mode}})","Are you sure you want to delete the Command?":"Bist du dir sicher, dass du diesen Befehl entfernen m\xF6chtest?","Remove and don't ask again":"Entfernen und Auswahl speichern",Remove:"Entfernen",Cancel:"Abbrechen","Always ask before removing?":"Immer fragen, bevor ein Befehl gel\xF6scht wird?","Always show a Popup to confirm deletion of a Command.":"Zeige immer ein Popup um L\xF6schen zu best\xE4tigen.",'Show "Add Command" Button':'Zeige "Befehl hinzuf\xFCgen" Knopf','Show the "Add Command" Button in every Menu. Requires restart.':'Zeige den "Befehl hinzuf\xFCgen" Knopf in jedem Men\xFC. Erfordert neustart.',"Please restart Obsidian for these changes to take effect.":"Bitte starte Obsidian neu, damit diese \xC4nderungen in Kraft treten.","Enable debugging":"Aktiviere debugging","Enable console output.":"Aktiviere Konsolen-Output (F\xFCr Entwickler)",General:"Allgemein","Editor Menu":"Editor-Men\xFC","File Menu":"Datei-Men\xFC","Left Ribbon":"Band","Right Ribbon":"Rechtes Band",Titlebar:"Titelleiste",Statusbar:"Statusleiste","Page Header":"Kopfzeile","Support development":"Entwicklung unterst\xFCtzen","No commands here!":"Keine Befehle da!","Would you like to add one now?":"M\xF6chtest du jetzt einen hinzuf\xFCgen?","Hide Commands":"Befehle verstecken","Choose new":"W\xE4hle neu","Hide Commands of other Plugins":"Hide Commands of other Plugins",Icon:"Symbol",Name:"Name","Custom Name":"Benutzerdefinierter Name","Add command to all devices":"F\xFCge Befehl allen Ger\xE4ten hinzu","Add command only to mobile devices":"F\xFCge Befehl nur Mobilen Ger\xE4ten hinzu","Add command only to desktop devices":"F\xFCge Befehl nur Desktop Ger\xE4ten hinzu","Add command only to this device":"F\xFCge Befehl nur diesem Ger\xE4t hinzu",Done:"Fertig","By Johnny\u2728 and phibr0":"Von Johnny\u2728 und phibr0","Leave feedback":"Feedback geben",Donate:"Spenden","Share feedback, issues, and ideas with our feedback form.":"Teile Feedback, Probleme und Ideen mit unserem Feedback Formular!","Consider donating to support development.":"Spende um die Entwicklung zu unterst\xFCtzen.",Save:"Speichern","This Command is not available on this device.":"Dieser Befehl ist auf diesem Ger\xE4t nicht verf\xFCgbar.",Show:"Anzeigen",Hide:"Verstecken","Hide other Commands":"Andere Befehle verstecken","Double click to enter custom value":"Doppelklicken um eigenen Wert einzutragen","Choose custom spacing for Command Buttons":"W\xE4hle den Abstand zwischen Befehlen","Change the spacing between commands. You can set different values on mobile and desktop.":"Ver\xE4ndert den Abstand zwischen Befehlen.",Warning:"Achtung","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":'Ab Obsidian Version 0.16.0 m\xFCssen Sie den "View Header" explizit aktivieren. Anschlie\xDFend muss Obsidian neugestartet werden.',"Open Appearance Settings":"\xD6ffne Darstellungs-Einstellungen",Explorer:"Explorer"};var gt={"Open Commander Settings":"Open Commander Settings","Open Macro Builder":"Open Macro Builder","Change Icon":"Change Icon",Rename:"Rename",Delete:"Delete","Add command":"Add command","Add new":"Add new command","This Command seems to have been removed. {{command_name}}":"This Command seems to have been removed. {{command_name}}","Choose a Command to add":"Choose a Command to add","to navigate":"to navigate","to choose an icon":"to choose an icon","to cancel":"to cancel","Use a custom name":"Use a custom name","Choose a custom Name for your new Command":"Choose a custom Name for your new Command","to save":"to save","Choose a Icon for your new Command":"Choose a Icon for your new Command","to choose a custom icon":"to choose a custom icon","Remove Command":"Remove Command","Double click to rename":"Double click to rename","This device":"This device","Added by {{plugin_name}}.":"Added by {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Warning: This is a checked Command, meaning it might not run under every circumstance.","Move down":"Move down","Move up":"Move up","Change Mode (Currently: {{current_mode}})":"Change Mode (Currently: {{current_mode}})","Are you sure you want to delete the Command?":"Are you sure you want to delete the Command?","Remove and don't ask again":"Remove and don't ask again",Remove:"Remove",Cancel:"Cancel","Always ask before removing?":"Always ask before removing?","Always show a Popup to confirm deletion of a Command.":"Always show a Popup to confirm deletion of a Command.",'Show "Add Command" Button':'Show "Add Command" Button','Show the "Add Command" Button in every Menu. Requires restart.':'Show the "Add Command" Button in every Menu. Requires restart.',"Please restart Obsidian for these changes to take effect.":"Please restart Obsidian for these changes to take effect.","Enable debugging":"Enable debugging","Enable console output.":"Enable console output.",General:"General","Editor Menu":"Editor Menu","File Menu":"File Menu","Left Ribbon":"Ribbon","Right Ribbon":"Right Ribbon",Titlebar:"Titlebar",Statusbar:"Status Bar","Page Header":"Tab Bar","Support development":"Support development","No commands here!":"No commands here!","Would you like to add one now?":"Would you like to add one now?","Hide Commands":"Hide Commands","Choose new":"Choose new","Hide Commands of other Plugins":"Hide Commands of other Plugins",Icon:"Icon",Name:"Name","Custom Name":"Custom Name","Add command to all devices":"Add command to all devices","Add command only to mobile devices":"Add command only to mobile devices","Add command only to desktop devices":"Add command only to desktop devices","Add command only to this device":"Add command only to this device",Done:"Done","By Johnny\u2728 and phibr0":"By Johnny\u2728 and phibr0","Leave feedback":"Leave feedback",Donate:"Donate","Share feedback, issues, and ideas with our feedback form.":"Share feedback, issues, and ideas with our feedback form.","Consider donating to support development.":"Consider donating to support development.",Save:"Save","This Command is not available on this device.":"This Command is not available on this device.",Show:"Show",Hide:"Hide","Hide other Commands":"Hide other Commands","Double click to enter custom value":"Double click to enter custom value","Choose custom spacing for Command Buttons":"Choose custom spacing for Command Buttons","Change the spacing between commands. You can set different values on mobile and desktop.":"Change the spacing between commands.",Warning:"Warning","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"As of Obsidian 0.16.0 you need to explicitly enable the Tab Title Bar. Once enabled, you might need to restart Obsidian.","Open Appearance Settings":"Open Appearance Settings",Explorer:"Explorer"};var no={};var ao={};var io={"Open Commander Settings":"Ouvrir les param\xE8tres de Commander","Open Macro Builder":"Ouvrir le constructeur de Macro","Change Icon":"Changer l'ic\xF4ne",Rename:"Renommer",Delete:"Supprimer","Add command":"Ajouter une commande","Add new":"Ajouter une nouvelle commande","This Command seems to have been removed. {{command_name}}":"Cette commande semble avoir \xE9t\xE9 supprim\xE9e. {{command_name}}","Choose a Command to add":"Choisissez une commande \xE0 ajouter","to navigate":"pour naviguer","to choose an icon":"pour choisir une ic\xF4ne","to cancel":"pour annuler","Use a custom name":"Utiliser un nom personnalis\xE9","Choose a custom Name for your new Command":"Choisissez un nom personnalis\xE9 pour votre nouvelle commande","to save":"pour enregistrer","Choose a Icon for your new Command":"Choisissez une ic\xF4ne pour votre nouvelle commande","to choose a custom icon":"pour choisir une ic\xF4ne personnalis\xE9e","Remove Command":"Supprimer la commande","Double click to rename":"Double-cliquez pour renommer","This device":"Cet appareil","Added by {{plugin_name}}.":"Ajout\xE9 par {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Attention : Cette commande est coch\xE9e, ce qui signifie qu'elle pourrait ne pas fonctionner dans toutes les situations.","Move down":"Descendre","Move up":"Monter","Change Mode (Currently: {{current_mode}})":"Changer de mode (Actuellement : {{current_mode}})","Are you sure you want to delete the Command?":"\xCAtes-vous s\xFBr de vouloir supprimer la commande ?","Remove and don't ask again":"Supprimer et ne plus demander",Remove:"Supprimer",Cancel:"Annuler","Always ask before removing?":"Toujours demander avant de supprimer ?","Always show a Popup to confirm deletion of a Command.":"Toujours afficher une fen\xEAtre contextuelle pour confirmer la suppression d'une commande.",'Show "Add Command" Button':'Afficher le bouton "Ajouter une commande"','Show the "Add Command" Button in every Menu. Requires restart.':'Afficher le bouton "Ajouter une commande" dans chaque menu. N\xE9cessite un red\xE9marrage.',"Please restart Obsidian for these changes to take effect.":"Veuillez red\xE9marrer Obsidian pour que ces modifications prennent effet.","Enable debugging":"Activer le d\xE9bogage","Enable console output.":"Activer la sortie console.",General:"G\xE9n\xE9ral","Editor Menu":"Menu \xE9diteur","File Menu":"Menu fichier","Left Ribbon":"Ruban gauche","Right Ribbon":"Ruban droit",Titlebar:"Barre de titre",Statusbar:"Barre d'\xE9tat","Page Header":"En-t\xEAte de page","Support development":"Soutenir le d\xE9veloppement","No commands here!":"Aucune commande ici !","Would you like to add one now?":"Voulez-vous en ajouter une maintenant ?","Hide Commands":"Masquer les commandes","Choose new":"Choisir nouveau","Hide Commands of other Plugins":"Masquer les commandes d'autres plugins",Icon:"Ic\xF4ne",Name:"Nom","Custom Name":"Nom personnalis\xE9","Add command to all devices":"Ajouter la commande \xE0 tous les appareils","Add command only to mobile devices":"Ajouter la commande uniquement sur les appareils mobiles","Add command only to desktop devices":"Ajouter la commande uniquement sur les ordinateurs de bureau","Add command only to this device":"Ajouter la commande uniquement sur cet appareil",Done:"Termin\xE9","By Johnny\u2728 and phibr0":"Par Johnny\u2728 et phibr0","Leave feedback":"Laisser un commentaire",Donate:"Faire un don","Share feedback, issues, and ideas with our feedback form.":"Partagez vos commentaires, probl\xE8mes et id\xE9es avec notre formulaire de retour d'information.","Consider donating to support development.":"Envisagez de faire un don pour soutenir le d\xE9veloppement.",Save:"Enregistrer","This Command is not available on this device.":"Cette commande n'est pas disponible sur cet appareil.",Show:"Afficher",Hide:"Masquer","Hide other Commands":"Masquer les autres commandes","Double click to enter custom value":"Double-cliquez pour entrer une valeur personnalis\xE9e","Choose custom spacing for Command Buttons":"Choisissez un espacement personnalis\xE9 pour les boutons de commande","Change the spacing between commands.":"Modifier l'espacement entre les commandes.",Warning:"Avertissement","As of Obsidian 0.16.0 you need to explicitly enable the Tab Title Bar. Once enabled, you might need to restart Obsidian.":"\xC0 partir d'Obsidian 0.16.0, vous devez activer explicitement la barre de titre des onglets. Une fois activ\xE9e, il se peut que vous deviez red\xE9marrer Obsidian.","Open Appearance Settings":"Ouvrir les param\xE8tres d'apparence",Explorer:"Explorateur"};var ro={};var so={};var co={};var mo={};var lo={};var uo={"Open Commander Settings":"Open Commander Instellingen","Open Macro Builder":"Open Macro Bouwer","Change Icon":"Verander Icoon",Rename:"Hernoem",Delete:"Verwijder","Add command":"Voeg commando toe","Add new":"Voeg nieuw commando toe","This Command seems to have been removed. {{command_name}}":"Het lijkt er op dat dit commando is verwijderd. {{command_name}}","Choose a Command to add":"Kies een commando om toe te voegen","to navigate":"naar navigatie","to choose an icon":"naar kies een icoon","to cancel":"naar annuleren","Use a custom name":"Gebruik een aangepaste naam","Choose a custom Name for your new Command":"Kies een aangepaste naam voor je nieuwe commando","to save":"naar opslaan","Choose a Icon for your new Command":"Kies een icoon voor je nieuwe commando","to choose a custom icon":"to choose a custom icon","Remove Command":"Verwijder commando","Double click to rename":"Dubbel klik om te hernoemen","This device":"Dit apparaat","Added by {{plugin_name}}.":"Toegevoegd door {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Waarschuwing: Dit is een aangevinkte opdracht, wat betekent dat deze mogelijk niet onder alle omstandigheden wordt uitgevoerd.","Move down":"Naar beneden","Move up":"Naar boven","Change Mode (Currently: {{current_mode}})":"Verander modus (Currently: {{current_mode}})","Are you sure you want to delete the Command?":"Weet je zeker dat je dit commando wilt verwijderen??","Remove and don't ask again":"Verwijder en vraag niet opnieuw",Remove:"Verwijder",Cancel:"Annuleer","Always ask before removing?":"Altijd vragen voor verwijderen?","Always show a Popup to confirm deletion of a Command.":"Laat altijd een venster zien om het verwijderen van een commando te bevestigen.",'Show "Add Command" Button':'Laat "Voeg Commando toe" knop zien','Show the "Add Command" Button in every Menu. Requires restart.':'Laat de "Voeg Commenado toe" knop zien in elk menu. Vereist herstart.',"Please restart Obsidian for these changes to take effect.":"Start Obsidian a.u.b. opnieuw op om deze wijzigingen toe te passen.","Enable debugging":"Activeer debugging","Enable console output.":"Activeer console output.",General:"Algemeen","Editor Menu":"Editor Menu","File Menu":"Bestand Menu","Left Ribbon":"Linkse Lint","Right Ribbon":"Rechtse Lint",Titlebar:"Titelbalk",Statusbar:"Statusbalk","Page Header":"Pagina Kop","Support development":"Steun ontwikkeling","No commands here!":"Geen commando's hier!","Would you like to add one now?":"Zou je er \xE9\xE9n willen toevoegen?","Hide Commands":"Verberg Commando's","Choose new":"Kies nieuw","Hide Commands of other Plugins":"Verberg Commando's van andere Plugins",Icon:"Icoon",Name:"Naam","Custom Name":"Aangepaste naam","Add command to all devices":"Voeg commando toe aan alle apparaten","Add command only to mobile devices":"Voeg commando toe aan alleen mobiele apparaten","Add command only to desktop devices":"Voeg commando toe aan alleen dekstop apparaten","Add command only to this device":"Voed commando toe aan alleen dit apparaat",Done:"Klaar","By Johnny\u2728 and phibr0":"Door Johnny\u2728 en phibr0","Leave feedback":"Laat feedback achter",Donate:"Doneer","Share feedback, issues, and ideas with our feedback form.":"Deel feedback, problemen en idee\xEBn met ons feedback formulier.","Consider donating to support development.":"Overweeg te doneren om ontwikkeling te steunen.",Save:"Opslaan","This Command is not available on this device.":"Dit Commando is niet beschikbaar op dit apparaat.",Show:"Laat zien",Hide:"Verberg","Hide other Commands":"Verberg andere Commando's","Double click to enter custom value":"Dubbel klik om een aangepaste waarde in te vullen","Choose custom spacing for Command Buttons":"Kies aangepaste regelafstand voor Commando Knoppen","Change the spacing between commands. You can set different values on mobile and desktop.":"Verander regelafstand tussen Commando's. Dit kan verschillen tussen mobiel en dekstop.",Warning:"Waarschuwing","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"Sinds Obsidian 0.16.0 moet je de kop expliciet inschakelen. Wanneer ingeschakeld moet je mogelijk Obsidian herstarten.","Open Appearance Settings":"Open Weergave Instellingen",Explorer:"Verkenner"};var po={};var fo={};var ho={};var vo={};var go={};var bo={"Open Commander Settings":'\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 "Commander"',"Open Macro Builder":"\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043D\u0441\u0442\u0440\u0443\u043A\u0442\u043E\u0440 \u043C\u0430\u043A\u0440\u043E\u0441\u043E\u0432","Change Icon":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u043E\u043A",Rename:"\u041F\u0435\u0440\u0435\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u0442\u044C",Delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C","Add command":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443","Add new":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043A\u043E\u043C\u0430\u043D\u0434\u0443","This Command seems to have been removed. {{command_name}}":"\u042D\u0442\u0430 \u043A\u043E\u043C\u0430\u043D\u0434\u0430, \u043A\u0430\u0436\u0435\u0442\u0441\u044F, \u0431\u044B\u043B\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0430. {{command_name}}","Choose a Command to add":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0434\u043B\u044F \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u044F","to navigate":"\u0434\u043B\u044F \u043D\u0430\u0432\u0438\u0433\u0430\u0446\u0438\u0438","to choose an icon":"\u0432\u044B\u0431\u0440\u0430\u0442\u044C \u0437\u043D\u0430\u0447\u043E\u043A","to cancel":"\u043E\u0442\u043C\u0435\u043D\u0438\u0442\u044C","Use a custom name":"\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0441\u043E\u0431\u0441\u0442\u0432\u0435\u043D\u043D\u043E\u0435 \u0438\u043C\u044F","Choose a custom Name for your new Command":"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0438\u043C\u044F \u0434\u043B\u044F \u0432\u0430\u0448\u0435\u0439 \u043D\u043E\u0432\u043E\u0439 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","to save":"\u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C","Choose a Icon for your new Command":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u043E\u043A \u0434\u043B\u044F \u0432\u0430\u0448\u0435\u0439 \u043D\u043E\u0432\u043E\u0439 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","to choose a custom icon":"\u0432\u044B\u0431\u0440\u0430\u0442\u044C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0439 \u0437\u043D\u0430\u0447\u043E\u043A","Remove Command":"\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443","Double click to rename":"\u0414\u0432\u0430\u0436\u0434\u044B \u0449\u0435\u043B\u043A\u043D\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043F\u0435\u0440\u0435\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u0442\u044C","This device":"\u042D\u0442\u043E \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u043E","Added by {{plugin_name}}.":"\u0414\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u043C {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0435: \u044D\u0442\u043E \u043F\u0440\u043E\u0432\u0435\u0440\u0435\u043D\u043D\u0430\u044F \u043A\u043E\u043C\u0430\u043D\u0434\u0430, \u0442\u043E \u0435\u0441\u0442\u044C \u043E\u043D\u0430 \u043C\u043E\u0436\u0435\u0442 \u043D\u0435 \u0432\u044B\u043F\u043E\u043B\u043D\u044F\u0442\u044C\u0441\u044F \u043F\u0440\u0438 \u043B\u044E\u0431\u044B\u0445 \u043E\u0431\u0441\u0442\u043E\u044F\u0442\u0435\u043B\u044C\u0441\u0442\u0432\u0430\u0445.","Move down":"\u0412\u043D\u0438\u0437","Move up":"\u0412\u0432\u0435\u0440\u0445","Change Mode (Currently: {{current_mode}})":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0440\u0435\u0436\u0438\u043C (\u0421\u0435\u0439\u0447\u0430\u0441: {{current_mode}})","Are you sure you want to delete the Command?":"\u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B, \u0447\u0442\u043E \u0445\u043E\u0442\u0438\u0442\u0435 \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443?","Remove and don't ask again":"\u0423\u0434\u0430\u043B\u0438\u0442\u0435 \u0438 \u0431\u043E\u043B\u044C\u0448\u0435 \u043D\u0435 \u0441\u043F\u0440\u0430\u0448\u0438\u0432\u0430\u0439\u0442\u0435",Remove:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",Cancel:"\u041E\u0442\u043C\u0435\u043D\u0430","Always ask before removing?":"\u0412\u0441\u0435\u0433\u0434\u0430 \u0441\u043F\u0440\u0430\u0448\u0438\u0432\u0430\u0442\u044C \u043F\u0435\u0440\u0435\u0434 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u0435\u043C?","Always show a Popup to confirm deletion of a Command.":"\u0412\u0441\u0435\u0433\u0434\u0430 \u043F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0432\u0441\u043F\u043B\u044B\u0432\u0430\u044E\u0449\u0435\u0435 \u043E\u043A\u043D\u043E \u0434\u043B\u044F \u043F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u044F \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u043A\u043E\u043C\u0430\u043D\u0434\u044B.",'Show "Add Command" Button':"\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \xAB\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443\xBB",'Show the "Add Command" Button in every Menu. Requires restart.':"\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \xAB\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443\xBB \u0432 \u043A\u0430\u0436\u0434\u043E\u043C \u043C\u0435\u043D\u044E. \u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043F\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0430.","Please restart Obsidian for these changes to take effect.":"\u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0443\u0441\u0442\u0438\u0442\u0435 Obsidian, \u0447\u0442\u043E\u0431\u044B \u044D\u0442\u0438 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F \u0432\u0441\u0442\u0443\u043F\u0438\u043B\u0438 \u0432 \u0441\u0438\u043B\u0443.","Enable debugging":"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043E\u0442\u043B\u0430\u0434\u043A\u0443","Enable console output.":"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0432\u044B\u0432\u043E\u0434 \u043A\u043E\u043D\u0441\u043E\u043B\u0438.",General:"\u041E\u0431\u0449\u0435\u0435","Editor Menu":"\u041C\u0435\u043D\u044E \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440\u0430","File Menu":"\u041C\u0435\u043D\u044E \xAB\u0424\u0430\u0439\u043B\xBB","Left Ribbon":"\u041B\u0435\u0432\u0430\u044F \u041B\u0435\u043D\u0442\u0430","Right Ribbon":"\u041F\u0440\u0430\u0432\u0430\u044F \u041B\u0435\u043D\u0442\u0430",Titlebar:"\u0417\u0430\u0433\u043E\u043B\u043E\u0432\u043E\u043A",Statusbar:"\u0421\u0442\u0430\u0442\u0443\u0441 \u0431\u0430\u0440","Page Header":"\u041F\u0430\u043D\u0435\u043B\u044C \u0432\u043A\u043B\u0430\u0434\u043E\u043A","Support development":"\u041F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u0430 \u0440\u0430\u0437\u0440\u0430\u0431\u043E\u0442\u043A\u0438","No commands here!":"\u0417\u0434\u0435\u0441\u044C \u043D\u0435\u0442 \u043A\u043E\u043C\u0430\u043D\u0434!","Would you like to add one now?":"\u0425\u043E\u0442\u0438\u0442\u0435 \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0441\u0435\u0439\u0447\u0430\u0441?","Hide Commands":"\u0421\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u044B","Choose new":"\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E","Hide Commands of other Plugins":"\u0421\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u044B \u0434\u0440\u0443\u0433\u0438\u0445 \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u0432",Icon:"\u0418\u043A\u043E\u043D\u043A\u0430",Name:"\u0418\u043C\u044F","Custom Name":"\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0438\u043C\u044F","Add command to all devices":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u043D\u0430 \u0432\u0441\u0435 \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u0430","Add command only to mobile devices":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F \u043C\u043E\u0431\u0438\u043B\u044C\u043D\u044B\u0445 \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432","Add command only to desktop devices":'\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F "Desktop" \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432',"Add command only to this device":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u043D\u0430 \u044D\u0442\u043E \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u043E",Done:"\u0413\u043E\u0442\u043E\u0432\u043E","By Johnny\u2728 and phibr0":'\u0421\u0434\u0435\u043B\u0430\u043B\u0438: "Johnny\u2728" \u0438 "phibr0"',"Leave feedback":"\u041E\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u043E\u0442\u0437\u044B\u0432",Donate:"\u041F\u043E\u0436\u0435\u0440\u0442\u0432\u043E\u0432\u0430\u0442\u044C","Share feedback, issues, and ideas with our feedback form.":"\u041F\u043E\u0434\u0435\u043B\u0438\u0442\u0435\u0441\u044C \u043E\u0442\u0437\u044B\u0432\u0430\u043C\u0438, \u043F\u0440\u043E\u0431\u043B\u0435\u043C\u0430\u043C\u0438 \u0438 \u0438\u0434\u0435\u044F\u043C\u0438 \u0441 \u043F\u043E\u043C\u043E\u0449\u044C\u044E \u043D\u0430\u0448\u0435\u0439 \u0444\u043E\u0440\u043C\u044B \u043E\u0431\u0440\u0430\u0442\u043D\u043E\u0439 \u0441\u0432\u044F\u0437\u0438.","Consider donating to support development.":"\u041F\u043E\u0434\u0443\u043C\u0430\u0439\u0442\u0435 \u043E \u043F\u043E\u0436\u0435\u0440\u0442\u0432\u043E\u0432\u0430\u043D\u0438\u0438 \u0434\u043B\u044F \u043F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u0438 \u0440\u0430\u0437\u0432\u0438\u0442\u0438\u044F.",Save:"\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C","This Command is not available on this device.":"This Command is not available on this device.",Show:"\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C",Hide:"\u0421\u043F\u0440\u044F\u0442\u0430\u0442\u044C","Hide other Commands":"\u0421\u043F\u0440\u044F\u0442\u0430\u0442\u044C \u0434\u0440\u0443\u0433\u0438\u0435 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","Double click to enter custom value":"\u0414\u0432\u0430\u0436\u0434\u044B \u0449\u0435\u043B\u043A\u043D\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0432\u0432\u0435\u0441\u0442\u0438 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435","Choose custom spacing for Command Buttons":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0439 \u0438\u043D\u0442\u0435\u0440\u0432\u0430\u043B \u043C\u0435\u0436\u0434\u0443 \u043A\u043E\u043C\u0430\u043D\u0434\u043D\u044B\u043C\u0438 \u043A\u043D\u043E\u043F\u043A\u0430\u043C\u0438","Change the spacing between commands. You can set different values on mobile and desktop.":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u0435 \u0440\u0430\u0441\u0441\u0442\u043E\u044F\u043D\u0438\u0435 \u043C\u0435\u0436\u0434\u0443 \u043A\u043E\u043C\u0430\u043D\u0434\u0430\u043C\u0438.",Warning:"\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0435","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"\u041D\u0430\u0447\u0438\u043D\u0430\u044F \u0441 Obsidian 0.16.0 \u0432\u0430\u043C \u043D\u0435\u043E\u0431\u0445\u043E\u0434\u0438\u043C\u043E \u044F\u0432\u043D\u043E \u0432\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0441\u0442\u0440\u043E\u043A\u0443 \u0437\u0430\u0433\u043E\u043B\u043E\u0432\u043A\u0430 \u0432\u043A\u043B\u0430\u0434\u043A\u0438. \u041F\u043E\u0441\u043B\u0435 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F \u0432\u0430\u043C \u043C\u043E\u0436\u0435\u0442 \u043F\u043E\u0442\u0440\u0435\u0431\u043E\u0432\u0430\u0442\u044C\u0441\u044F \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0443\u0441\u0442\u0438\u0442\u044C Obsidian.","Open Appearance Settings":"\u041E\u0442\u043A\u0440\u043E\u0439\u0442\u0435 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0432\u043D\u0435\u0448\u043D\u0435\u0433\u043E \u0432\u0438\u0434\u0430",Explorer:"\u0424\u0430\u0439\u043B\u043E\u0432\u044B\u0439 \u043C\u0435\u043D\u0435\u0434\u0436\u0435\u0440"};var Co={};var _o={"Open Commander Settings":"\u6253\u5F00 Commander \u8BBE\u7F6E","Open Macro Builder":"\u6253\u5F00\u5B8F\u6307\u4EE4\u751F\u6210\u5668","Change Icon":"\u66F4\u6362\u56FE\u6807",Rename:"\u91CD\u547D\u540D",Delete:"\u5220\u9664","Add command":"\u6DFB\u52A0\u547D\u4EE4","Add new":"\u6DFB\u52A0\u65B0\u547D\u4EE4","This Command seems to have been removed. {{command_name}}":"\u8BE5\u547D\u4EE4\u4F3C\u4E4E\u5DF2\u88AB\u79FB\u9664\u3002{{command_name}}","Choose a Command to add":"\u9009\u62E9\u4E00\u4E2A\u547D\u4EE4\u5E76\u6DFB\u52A0","to navigate":"\u5BFC\u822A","to choose an icon":"\u9009\u4E2D\u4E00\u4E2A\u56FE\u6807","to cancel":"\u53D6\u6D88","Use a custom name":"\u4F7F\u7528\u4E00\u4E2A\u81EA\u5B9A\u4E49\u540D\u79F0","Choose a custom Name for your new Command":"\u4E3A\u4F60\u7684\u65B0\u547D\u4EE4\u9009\u62E9\u4E00\u4E2A\u81EA\u5B9A\u4E49\u540D\u79F0","to save":"\u4FDD\u5B58","Choose a Icon for your new Command":"\u4E3A\u4F60\u7684\u65B0\u547D\u4EE4\u9009\u62E9\u4E00\u4E2A\u56FE\u6807","to choose a custom icon":"\u9009\u62E9\u4E00\u4E2A\u81EA\u5B9A\u4E49\u56FE\u6807","Remove Command":"\u79FB\u9664\u547D\u4EE4","Double click to rename":"\u53CC\u51FB\u4EE5\u91CD\u547D\u540D","This device":"\u8BE5\u8BBE\u5907","Added by {{plugin_name}}.":"\u7531{{plugin_name}}\u6DFB\u52A0\u3002","Warning: This is a checked Command, meaning it might not run under every circumstance.":"\u8B66\u544A\uFF1A\u8FD9\u662F\u4E00\u4E2A\u53D7\u68C0\u7684\u547D\u4EE4\uFF0C\u8FD9\u610F\u5473\u7740\u5B83\u672A\u5FC5\u80FD\u5728\u6240\u4EE5\u73AF\u5883\u4E0B\u8FD0\u884C\u3002","Move down":"\u5411\u4E0B\u79FB\u52A8","Move up":"\u5411\u4E0A\u79FB\u52A8","Change Mode (Currently: {{current_mode}})":"\u5207\u6362\u6A21\u5F0F\uFF08\u5F53\u524D\uFF1A{{current_mode}}\uFF09","Are you sure you want to delete the Command?":"\u662F\u5426\u786E\u8BA4\u79FB\u9664\u8BE5\u547D\u4EE4\uFF1F","Remove and don't ask again":"\u79FB\u9664\u4E14\u4E0D\u8981\u518D\u8BE2\u95EE",Remove:"\u79FB\u9664",Cancel:"\u53D6\u6D88","Always ask before removing?":"\u5728\u79FB\u9664\u524D\u603B\u662F\u8BE2\u95EE\uFF1F","Always show a Popup to confirm deletion of a Command.":"\u5728\u786E\u8BA4\u79FB\u9664\u547D\u4EE4\u524D\u603B\u662F\u5F39\u7A97\u3002",'Show "Add Command" Button':"\u663E\u793A\u201C\u6DFB\u52A0\u547D\u4EE4\u201D\u6309\u94AE",'Show the "Add Command" Button in every Menu. Requires restart.':"\u5728\u6BCF\u4E2A\u83DC\u5355\u90FD\u5C55\u793A\u201C\u6DFB\u52A0\u547D\u4EE4\u201D\u6309\u94AE\u3002\u9700\u8981\u91CD\u542F\u3002","Please restart Obsidian for these changes to take effect.":"\u8BF7\u91CD\u542F Obsidian \u4EE5\u4F7F\u8FD9\u4E9B\u66F4\u6539\u751F\u6548\u3002","Enable debugging":"\u542F\u7528\u9664\u9519","Enable console output.":"\u542F\u7528\u63A7\u5236\u53F0\u8F93\u51FA\u3002",General:"\u901A\u7528","Editor Menu":"\u7F16\u8F91\u5668\u83DC\u5355","File Menu":"\u6587\u4EF6\u83DC\u5355","Left Ribbon":"\u5DE6\u4FA7\u8FB9\u680F","Right Ribbon":"\u53F3\u4FA7\u8FB9\u680F",Titlebar:"\u6807\u9898\u680F",Statusbar:"\u72B6\u6001\u680F","Page Header":"\u9875\u9996","Support development":"\u652F\u6301\u5F00\u53D1","No commands here!":"\u8FD9\u91CC\u6CA1\u6709\u547D\u4EE4\uFF01","Would you like to add one now?":"\u4F60\u73B0\u5728\u60F3\u8981\u52A0\u4E00\u4E2A\u5417\uFF1F","Hide Commands":"\u9690\u85CF\u547D\u4EE4","Choose new":"\u9009\u62E9\u65B0\u7684","Hide Commands of other Plugins":"\u9690\u85CF\u5176\u4ED6\u63D2\u4EF6\u7684\u547D\u4EE4",Icon:"\u56FE\u6807",Name:"\u540D\u79F0","Custom Name":"\u81EA\u5B9A\u4E49\u540D\u79F0","Add command to all devices":"\u5411\u6240\u6709\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to mobile devices":"\u53EA\u5411\u79FB\u52A8\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to desktop devices":"\u53EA\u5411\u684C\u9762\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to this device":"\u53EA\u5411\u5F53\u524D\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4",Done:"\u5B8C\u6210","By Johnny\u2728 and phibr0":"\u7531 Johnny\u2728 \u548C phibr0 \u5F00\u53D1","Leave feedback":"\u7559\u4E0B\u53CD\u9988",Donate:"\u6350\u8D60","Share feedback, issues, and ideas with our feedback form.":"\u4EE5\u6211\u4EEC\u7684\u53CD\u9988\u8868\uFF0C\u5206\u4EAB\u53CD\u9988\u3001\u8BAE\u9898\u6216\u8005\u4F60\u7684\u60F3\u6CD5\u3002","Consider donating to support development.":"\u8003\u8651\u6350\u8D60\u4EE5\u652F\u6301\u5F00\u53D1\u3002",Save:"\u4FDD\u5B58","This Command is not available on this device.":"\u8FD9\u4E00\u547D\u4EE4\u5728\u5F53\u524D\u8BBE\u5907\u4E0D\u53EF\u7528\u3002",Show:"\u663E\u793A",Hide:"\u9690\u85CF","Hide other Commands":"\u9690\u85CF\u5176\u4F59\u547D\u4EE4","Double click to enter custom value":"\u53CC\u51FB\u4EE5\u6DFB\u52A0\u81EA\u5B9A\u4E49\u503C","Choose custom spacing for Command Buttons":"\u4E3A\u547D\u4EE4\u6309\u94AE\u9009\u62E9\u81EA\u5B9A\u4E49\u95F4\u8DDD","Change the spacing between commands. You can set different values on mobile and desktop.":"\u6539\u53D8\u547D\u4EE4\u4E4B\u95F4\u7684\u95F4\u8DDD\u3002\u4F60\u53EF\u4EE5\u4E3A\u79FB\u52A8\u548C\u684C\u9762\u8BBE\u5907\u8BBE\u7F6E\u4E0D\u540C\u7684\u503C\u3002"};var yo={};var pa={ar:Qt,cs:eo,da:to,de:oo,en:gt,"en-gb":no,es:ao,fr:io,hi:ro,id:so,it:co,ja:mo,ko:lo,nl:uo,nn:po,pl:fo,pt:ho,"pt-br":vo,ro:go,ru:bo,tr:Co,"zh-cn":_o,"zh-tw":yo},bt=pa[Ct.moment.locale()];function u(t){return bt||console.error("Error: dictionary locale not found",Ct.moment.locale()),bt&&bt[t]||gt[t]}var ne=class extends Ve.FuzzySuggestModal{constructor(e){super(e.app);this.plugin=e,this.commands=Object.values(e.app.commands.commands),this.setPlaceholder(u("Choose a Command to add")),this.setInstructions([{command:"\u2191\u2193",purpose:u("to navigate")},{command:"\u21B5",purpose:u("to choose an icon")},{command:"esc",purpose:u("to cancel")}])}async awaitSelection(){return this.open(),new Promise((e,n)=>{this.onChooseItem=a=>e(a),this.onClose=()=>window.setTimeout(()=>n("No Command selected"),0)})}renderSuggestion(e,n){if(n.addClass("mod-complex"),n.createDiv({cls:"suggestion-content"}).createDiv({cls:"suggestion-title"}).setText(e.item.name),e.item.icon){let i=n.createDiv({cls:"suggestion-aux"});(0,Ve.setIcon)(i.createSpan({cls:"suggestion-flair"}),e.item.icon)}}getItems(){return this.commands}getItemText(e){return e.name}onChooseItem(e,n){}};var We=require("obsidian"),wo={confirmDeletion:!0,showAddCommand:!0,debug:!1,editorMenu:[],fileMenu:[],leftRibbon:[],rightRibbon:[],titleBar:[],statusBar:[],pageHeader:[],macros:[],explorer:[],hide:{statusbar:[],leftRibbon:[]},spacing:8,advancedToolbar:{rowHeight:48,rowCount:1,spacing:0,buttonWidth:48,columnLayout:!1,mappedIcons:[],tooltips:!1,heightOffset:0}},ko=(0,We.requireApiVersion)("1.7.3")?(0,We.getIconIds)():["activity","airplay","alarm-check","alarm-clock-off","alarm-clock","alarm-minus","alarm-plus","album","alert-circle","alert-octagon","alert-triangle","align-center-horizontal","align-center-vertical","align-center","align-end-horizontal","align-end-vertical","align-horizontal-distribute-center","align-horizontal-distribute-end","align-horizontal-distribute-start","align-horizontal-justify-center","align-horizontal-justify-end","align-horizontal-justify-start","align-horizontal-space-around","align-horizontal-space-between","align-justify","align-left","align-right","align-start-horizontal","align-start-vertical","align-vertical-distribute-center","align-vertical-distribute-end","align-vertical-distribute-start","align-vertical-justify-center","align-vertical-justify-end","align-vertical-justify-start","align-vertical-space-around","align-vertical-space-between","anchor","aperture","archive","arrow-big-down","arrow-big-left","arrow-big-right","arrow-big-up","arrow-down-circle","arrow-down-left","arrow-down-right","arrow-down","arrow-left-circle","arrow-left-right","arrow-left","arrow-right-circle","arrow-right","arrow-up-circle","arrow-up-left","arrow-up-right","arrow-up","asterisk","at-sign","award","axe","banknote","bar-chart-2","bar-chart","baseline","battery-charging","battery-full","battery-low","battery-medium","battery","beaker","bell-minus","bell-off","bell-plus","bell-ring","bell","bike","binary","bitcoin","bluetooth-connected","bluetooth-off","bluetooth-searching","bluetooth","bold","book-open","book","bookmark-minus","bookmark-plus","bookmark","bot","box-select","box","briefcase","brush","bug","building-2","building","bus","calculator","calendar","camera-off","camera","car","carrot","cast","check-circle-2","check-circle","check-square","check","chevron-down","chevron-first","chevron-last","chevron-left","chevron-right","chevron-up","chevrons-down-up","chevrons-down","chevrons-left","chevrons-right","chevrons-up-down","chevrons-up","chrome","circle-slashed","circle","clipboard-check","clipboard-copy","clipboard-list","clipboard-x","clipboard","clock-1","clock-10","clock-11","clock-12","clock-2","clock-3","clock-4","clock-5","clock-6","clock-7","clock-8","clock-9","lucide-clock","cloud-drizzle","cloud-fog","cloud-hail","cloud-lightning","cloud-moon","cloud-off","cloud-rain-wind","cloud-rain","cloud-snow","cloud-sun","lucide-cloud","cloudy","clover","code-2","code","codepen","codesandbox","coffee","coins","columns","command","compass","contact","contrast","cookie","copy","copyleft","copyright","corner-down-left","corner-down-right","corner-left-down","corner-left-up","corner-right-down","corner-right-up","corner-up-left","corner-up-right","cpu","credit-card","crop","lucide-cross","crosshair","crown","currency","database","delete","dice-1","dice-2","dice-3","dice-4","dice-5","dice-6","disc","divide-circle","divide-square","divide","dollar-sign","download-cloud","download","dribbble","droplet","droplets","drumstick","edit-2","edit-3","edit","egg","equal-not","equal","eraser","euro","expand","external-link","eye-off","eye","facebook","fast-forward","feather","figma","file-check-2","file-check","file-code","file-digit","file-input","file-minus-2","file-minus","file-output","file-plus-2","file-plus","file-search","file-text","file-x-2","file-x","file","files","film","filter","flag-off","flag-triangle-left","flag-triangle-right","flag","flame","flashlight-off","flashlight","flask-conical","flask-round","folder-minus","folder-open","folder-plus","lucide-folder","form-input","forward","frame","framer","frown","function-square","gamepad-2","gamepad","gauge","gavel","gem","ghost","gift","git-branch-plus","git-branch","git-commit","git-fork","git-merge","git-pull-request","github","gitlab","glasses","globe-2","globe","grab","graduation-cap","grid","grip-horizontal","grip-vertical","hammer","hand-metal","hand","hard-drive","hard-hat","hash","haze","headphones","heart","help-circle","hexagon","highlighter","history","home","image-minus","image-off","image-plus","image","import","inbox","indent","indian-rupee","infinity","lucide-info","inspect","instagram","italic","japanese-yen","key","keyboard","landmark","lucide-languages","laptop-2","laptop","lasso-select","lasso","layers","layout-dashboard","layout-grid","layout-list","layout-template","layout","library","life-buoy","lightbulb-off","lightbulb","link-2-off","link-2","lucide-link","linkedin","list-checks","list-minus","list-ordered","list-plus","list-x","list","loader-2","loader","locate-fixed","locate-off","locate","lock","log-in","log-out","mail","map-pin","map","maximize-2","maximize","megaphone","meh","menu","message-circle","message-square","mic-off","mic","minimize-2","minimize","minus-circle","minus-square","minus","monitor-off","monitor-speaker","monitor","moon","more-horizontal","more-vertical","mountain-snow","mountain","mouse-pointer-2","mouse-pointer-click","mouse-pointer","mouse","move-diagonal-2","move-diagonal","move-horizontal","move-vertical","move","music","navigation-2","navigation","network","octagon","option","outdent","package-check","package-minus","package-plus","package-search","package-x","package","palette","palmtree","paperclip","pause-circle","pause-octagon","pause","pen-tool","lucide-pencil","percent","person-standing","phone-call","phone-forwarded","phone-incoming","phone-missed","phone-off","phone-outgoing","phone","pie-chart","piggy-bank","lucide-pin","pipette","plane","play-circle","play","plug-zap","plus-circle","plus-square","plus","pocket","podcast","pointer","pound-sterling","power-off","power","printer","qr-code","quote","radio-receiver","radio","redo","refresh-ccw","refresh-cw","regex","repeat-1","repeat","reply-all","reply","rewind","rocket","rocking-chair","rotate-ccw","rotate-cw","rss","ruler","russian-ruble","save","scale","scan-line","scan","scissors","screen-share-off","screen-share","lucide-search","send","separator-horizontal","separator-vertical","server-crash","server-off","server","settings-2","settings","share-2","share","sheet","shield-alert","shield-check","shield-close","shield-off","shield","shirt","shopping-bag","shopping-cart","shovel","shrink","shuffle","sidebar-close","sidebar-open","sidebar","sigma","signal-high","signal-low","signal-medium","signal-zero","signal","skip-back","skip-forward","skull","slack","slash","sliders","smartphone-charging","smartphone","smile","snowflake","sort-asc","sort-desc","speaker","sprout","square","star-half","lucide-star","stop-circle","stretch-horizontal","stretch-vertical","strikethrough","subscript","sun","sunrise","sunset","superscript","swiss-franc","switch-camera","table","tablet","tag","target","tent","terminal-square","terminal","text-cursor-input","text-cursor","thermometer-snowflake","thermometer-sun","thermometer","thumbs-down","thumbs-up","ticket","timer-off","timer-reset","timer","toggle-left","toggle-right","tornado","trash-2","lucide-trash","trello","trending-down","trending-up","triangle","truck","tv-2","tv","twitch","twitter","type","umbrella","underline","undo","unlink-2","unlink","unlock","upload-cloud","upload","user-check","user-minus","user-plus","user-x","user","users","verified","vibrate","video-off","video","view","voicemail","volume-1","volume-2","volume-x","volume","wallet","wand","watch","waves","webcam","wifi-off","wifi","wind","wrap-text","wrench","x-circle","x-octagon","x-square","x","youtube","zap-off","zap","zoom-in","zoom-out","search-large"];var je=require("obsidian");var R=class extends je.FuzzySuggestModal{constructor(e){super(e.app);this.plugin=e,this.setPlaceholder(u("Choose a Icon for your new Command")),this.setInstructions([{command:"\u2191\u2193",purpose:u("to navigate")},{command:"\u21B5",purpose:u("to choose a custom icon")},{command:"esc",purpose:u("to cancel")}])}async awaitSelection(){return this.open(),new Promise((e,n)=>{this.onChooseItem=a=>e(a),this.onClose=()=>window.setTimeout(()=>n("No Icon selected"),0)})}renderSuggestion(e,n){n.addClass("mod-complex"),n.createDiv({cls:"suggestion-content"}).createDiv({cls:"suggestion-title"}).setText(e.item.replace("lucide-","").replace(/-/g," ").replace(/(^\w{1})|(\s+\w{1})/g,c=>c.toUpperCase()));let i=n.createDiv({cls:"suggestion-aux"});(0,je.setIcon)(i.createSpan({cls:"suggestion-flair"}),e.item)}getItems(){return ko}getItemText(e){return e}onChooseItem(e,n){}};var tt=require("obsidian");var Mo=require("obsidian");var U=class extends Mo.SuggestModal{constructor(e,n){super(n.app);this.defaultName=e;this.plugin=n;this.setPlaceholder(u("Use a custom name")),this.resultContainerEl.style.display="none",this.setInstructions([{command:"",purpose:u("Choose a custom Name for your new Command")},{command:"\u21B5",purpose:u("to save")},{command:"esc",purpose:u("to cancel")}])}onOpen(){var a;super.onOpen(),this.inputEl.value=this.defaultName;let e=createDiv({cls:"cmdr-name-input-wrapper"});(a=this.inputEl.parentNode)==null||a.insertBefore(e,this.inputEl),e.appendChild(this.inputEl),e.parentElement.style.display="block";let n=createEl("button",{text:u("Save"),cls:"mod-cta"});n.onclick=i=>this.selectSuggestion(this.inputEl.value,i),e.appendChild(n)}async awaitSelection(){return this.open(),new Promise((e,n)=>{this.onChooseSuggestion=a=>e(a),this.onClose=()=>window.setTimeout(()=>n("No Name selected"),0)})}getSuggestions(e){return[e]}renderSuggestion(e,n){}onChooseSuggestion(e,n){}};var Xe,_,Io,fa,_e,Eo,Ao,qe={},To=[],ha=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function ae(t,o){for(var e in o)t[e]=o[e];return t}function Lo(t){var o=t.parentNode;o&&o.removeChild(t)}function r(t,o,e){var n,a,i,c={};for(i in o)i=="key"?n=o[i]:i=="ref"?a=o[i]:c[i]=o[i];if(arguments.length>2&&(c.children=arguments.length>3?Xe.call(arguments,2):e),typeof t=="function"&&t.defaultProps!=null)for(i in t.defaultProps)c[i]===void 0&&(c[i]=t.defaultProps[i]);return Ue(t,c,n,a,null)}function Ue(t,o,e,n,a){var i={type:t,props:o,key:e,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:a==null?++Io:a};return a==null&&_.vnode!=null&&_.vnode(i),i}function P(t){return t.children}function G(t,o){this.props=t,this.context=o}function ye(t,o){if(o==null)return t.__?ye(t.__,t.__.__k.indexOf(t)+1):null;for(var e;o<t.__k.length;o++)if((e=t.__k[o])!=null&&e.__e!=null)return e.__e;return typeof t.type=="function"?ye(t):null}function No(t){var o,e;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,o=0;o<t.__k.length;o++)if((e=t.__k[o])!=null&&e.__e!=null){t.__e=t.__c.base=e.__e;break}return No(t)}}function _t(t){(!t.__d&&(t.__d=!0)&&_e.push(t)&&!Ze.__r++||Eo!==_.debounceRendering)&&((Eo=_.debounceRendering)||setTimeout)(Ze)}function Ze(){for(var t;Ze.__r=_e.length;)t=_e.sort(function(o,e){return o.__v.__b-e.__v.__b}),_e=[],t.some(function(o){var e,n,a,i,c,m;o.__d&&(c=(i=(e=o).__v).__e,(m=e.__P)&&(n=[],(a=ae({},i)).__v=i.__v+1,yt(m,i,a,e.__n,m.ownerSVGElement!==void 0,i.__h!=null?[c]:null,n,c==null?ye(i):c,i.__h),Ro(n,i),i.__e!=c&&No(i)))})}function Do(t,o,e,n,a,i,c,m,l,h){var s,g,b,f,C,w,v,A=n&&n.__k||To,y=A.length;for(e.__k=[],s=0;s<o.length;s++)if((f=e.__k[s]=(f=o[s])==null||typeof f=="boolean"?null:typeof f=="string"||typeof f=="number"||typeof f=="bigint"?Ue(null,f,null,null,f):Array.isArray(f)?Ue(P,{children:f},null,null,null):f.__b>0?Ue(f.type,f.props,f.key,f.ref?f.ref:null,f.__v):f)!=null){if(f.__=e,f.__b=e.__b+1,(b=A[s])===null||b&&f.key==b.key&&f.type===b.type)A[s]=void 0;else for(g=0;g<y;g++){if((b=A[g])&&f.key==b.key&&f.type===b.type){A[g]=void 0;break}b=null}yt(t,f,b=b||qe,a,i,c,m,l,h),C=f.__e,(g=f.ref)&&b.ref!=g&&(v||(v=[]),b.ref&&v.push(b.ref,null,f),v.push(g,f.__c||C,f)),C!=null?(w==null&&(w=C),typeof f.type=="function"&&f.__k===b.__k?f.__d=l=Bo(f,l,t):l=Ho(t,f,b,A,C,l),typeof e.type=="function"&&(e.__d=l)):l&&b.__e==l&&l.parentNode!=t&&(l=ye(b))}for(e.__e=w,s=y;s--;)A[s]!=null&&Oo(A[s],A[s]);if(v)for(s=0;s<v.length;s++)zo(v[s],v[++s],v[++s])}function Bo(t,o,e){for(var n,a=t.__k,i=0;a&&i<a.length;i++)(n=a[i])&&(n.__=t,o=typeof n.type=="function"?Bo(n,o,e):Ho(e,n,n,a,n.__e,o));return o}function we(t,o){return o=o||[],t==null||typeof t=="boolean"||(Array.isArray(t)?t.some(function(e){we(e,o)}):o.push(t)),o}function Ho(t,o,e,n,a,i){var c,m,l;if(o.__d!==void 0)c=o.__d,o.__d=void 0;else if(e==null||a!=i||a.parentNode==null)e:if(i==null||i.parentNode!==t)t.appendChild(a),c=null;else{for(m=i,l=0;(m=m.nextSibling)&&l<n.length;l+=1)if(m==a)break e;t.insertBefore(a,i),c=i}return c!==void 0?c:a.nextSibling}function va(t,o,e,n,a){var i;for(i in e)i==="children"||i==="key"||i in o||Je(t,i,null,e[i],n);for(i in o)a&&typeof o[i]!="function"||i==="children"||i==="key"||i==="value"||i==="checked"||e[i]===o[i]||Je(t,i,o[i],e[i],n)}function xo(t,o,e){o[0]==="-"?t.setProperty(o,e):t[o]=e==null?"":typeof e!="number"||ha.test(o)?e:e+"px"}function Je(t,o,e,n,a){var i;e:if(o==="style")if(typeof e=="string")t.style.cssText=e;else{if(typeof n=="string"&&(t.style.cssText=n=""),n)for(o in n)e&&o in e||xo(t.style,o,"");if(e)for(o in e)n&&e[o]===n[o]||xo(t.style,o,e[o])}else if(o[0]==="o"&&o[1]==="n")i=o!==(o=o.replace(/Capture$/,"")),o=o.toLowerCase()in t?o.toLowerCase().slice(2):o.slice(2),t.l||(t.l={}),t.l[o+i]=e,e?n||t.addEventListener(o,i?Po:So,i):t.removeEventListener(o,i?Po:So,i);else if(o!=="dangerouslySetInnerHTML"){if(a)o=o.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(o!=="href"&&o!=="list"&&o!=="form"&&o!=="tabIndex"&&o!=="download"&&o in t)try{t[o]=e==null?"":e;break e}catch(c){}typeof e=="function"||(e==null||e===!1&&o.indexOf("-")==-1?t.removeAttribute(o):t.setAttribute(o,e))}}function So(t){this.l[t.type+!1](_.event?_.event(t):t)}function Po(t){this.l[t.type+!0](_.event?_.event(t):t)}function yt(t,o,e,n,a,i,c,m,l){var h,s,g,b,f,C,w,v,A,y,H,ee,Be,ue,pe,j=o.type;if(o.constructor!==void 0)return null;e.__h!=null&&(l=e.__h,m=o.__e=e.__e,o.__h=null,i=[m]),(h=_.__b)&&h(o);try{e:if(typeof j=="function"){if(v=o.props,A=(h=j.contextType)&&n[h.__c],y=h?A?A.props.value:h.__:n,e.__c?w=(s=o.__c=e.__c).__=s.__E:("prototype"in j&&j.prototype.render?o.__c=s=new j(v,y):(o.__c=s=new G(v,y),s.constructor=j,s.render=ba),A&&A.sub(s),s.props=v,s.state||(s.state={}),s.context=y,s.__n=n,g=s.__d=!0,s.__h=[],s._sb=[]),s.__s==null&&(s.__s=s.state),j.getDerivedStateFromProps!=null&&(s.__s==s.state&&(s.__s=ae({},s.__s)),ae(s.__s,j.getDerivedStateFromProps(v,s.__s))),b=s.props,f=s.state,g)j.getDerivedStateFromProps==null&&s.componentWillMount!=null&&s.componentWillMount(),s.componentDidMount!=null&&s.__h.push(s.componentDidMount);else{if(j.getDerivedStateFromProps==null&&v!==b&&s.componentWillReceiveProps!=null&&s.componentWillReceiveProps(v,y),!s.__e&&s.shouldComponentUpdate!=null&&s.shouldComponentUpdate(v,s.__s,y)===!1||o.__v===e.__v){for(s.props=v,s.state=s.__s,o.__v!==e.__v&&(s.__d=!1),s.__v=o,o.__e=e.__e,o.__k=e.__k,o.__k.forEach(function(fe){fe&&(fe.__=o)}),H=0;H<s._sb.length;H++)s.__h.push(s._sb[H]);s._sb=[],s.__h.length&&c.push(s);break e}s.componentWillUpdate!=null&&s.componentWillUpdate(v,s.__s,y),s.componentDidUpdate!=null&&s.__h.push(function(){s.componentDidUpdate(b,f,C)})}if(s.context=y,s.props=v,s.__v=o,s.__P=t,ee=_.__r,Be=0,"prototype"in j&&j.prototype.render){for(s.state=s.__s,s.__d=!1,ee&&ee(o),h=s.render(s.props,s.state,s.context),ue=0;ue<s._sb.length;ue++)s.__h.push(s._sb[ue]);s._sb=[]}else do s.__d=!1,ee&&ee(o),h=s.render(s.props,s.state,s.context),s.state=s.__s;while(s.__d&&++Be<25);s.state=s.__s,s.getChildContext!=null&&(n=ae(ae({},n),s.getChildContext())),g||s.getSnapshotBeforeUpdate==null||(C=s.getSnapshotBeforeUpdate(b,f)),pe=h!=null&&h.type===P&&h.key==null?h.props.children:h,Do(t,Array.isArray(pe)?pe:[pe],o,e,n,a,i,c,m,l),s.base=o.__e,o.__h=null,s.__h.length&&c.push(s),w&&(s.__E=s.__=null),s.__e=!1}else i==null&&o.__v===e.__v?(o.__k=e.__k,o.__e=e.__e):o.__e=ga(e.__e,o,e,n,a,i,c,l);(h=_.diffed)&&h(o)}catch(fe){o.__v=null,(l||i!=null)&&(o.__e=m,o.__h=!!l,i[i.indexOf(m)]=null),_.__e(fe,o,e)}}function Ro(t,o){_.__c&&_.__c(o,t),t.some(function(e){try{t=e.__h,e.__h=[],t.some(function(n){n.call(e)})}catch(n){_.__e(n,e.__v)}})}function ga(t,o,e,n,a,i,c,m){var l,h,s,g=e.props,b=o.props,f=o.type,C=0;if(f==="svg"&&(a=!0),i!=null){for(;C<i.length;C++)if((l=i[C])&&"setAttribute"in l==!!f&&(f?l.localName===f:l.nodeType===3)){t=l,i[C]=null;break}}if(t==null){if(f===null)return document.createTextNode(b);t=a?document.createElementNS("http://www.w3.org/2000/svg",f):document.createElement(f,b.is&&b),i=null,m=!1}if(f===null)g===b||m&&t.data===b||(t.data=b);else{if(i=i&&Xe.call(t.childNodes),h=(g=e.props||qe).dangerouslySetInnerHTML,s=b.dangerouslySetInnerHTML,!m){if(i!=null)for(g={},C=0;C<t.attributes.length;C++)g[t.attributes[C].name]=t.attributes[C].value;(s||h)&&(s&&(h&&s.__html==h.__html||s.__html===t.innerHTML)||(t.innerHTML=s&&s.__html||""))}if(va(t,b,g,a,m),s)o.__k=[];else if(C=o.props.children,Do(t,Array.isArray(C)?C:[C],o,e,n,a&&f!=="foreignObject",i,c,i?i[0]:e.__k&&ye(e,0),m),i!=null)for(C=i.length;C--;)i[C]!=null&&Lo(i[C]);m||("value"in b&&(C=b.value)!==void 0&&(C!==t.value||f==="progress"&&!C||f==="option"&&C!==g.value)&&Je(t,"value",C,g.value,!1),"checked"in b&&(C=b.checked)!==void 0&&C!==t.checked&&Je(t,"checked",C,g.checked,!1))}return t}function zo(t,o,e){try{typeof t=="function"?t(o):t.current=o}catch(n){_.__e(n,e)}}function Oo(t,o,e){var n,a;if(_.unmount&&_.unmount(t),(n=t.ref)&&(n.current&&n.current!==t.__e||zo(n,null,o)),(n=t.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(i){_.__e(i,o)}n.base=n.__P=null,t.__c=void 0}if(n=t.__k)for(a=0;a<n.length;a++)n[a]&&Oo(n[a],o,e||typeof t.type!="function");e||t.__e==null||Lo(t.__e),t.__=t.__e=t.__d=void 0}function ba(t,o,e){return this.constructor(t,e)}function W(t,o,e){var n,a,i;_.__&&_.__(t,o),a=(n=typeof e=="function")?null:e&&e.__k||o.__k,i=[],yt(o,t=(!n&&e||o).__k=r(P,null,[t]),a||qe,qe,o.ownerSVGElement!==void 0,!n&&e?[e]:a?null:o.firstChild?Xe.call(o.childNodes):null,i,!n&&e?e:a?a.__e:o.firstChild,n),Ro(i,t)}function Ye(t,o){var e={__c:o="__cC"+Ao++,__:t,Consumer:function(n,a){return n.children(a)},Provider:function(n){var a,i;return this.getChildContext||(a=[],(i={})[o]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(c){this.props.value!==c.value&&a.some(_t)},this.sub=function(c){a.push(c);var m=c.componentWillUnmount;c.componentWillUnmount=function(){a.splice(a.indexOf(c),1),m&&m.call(c)}}),n.children}};return e.Provider.__=e.Consumer.contextType=e}Xe=To.slice,_={__e:function(t,o,e,n){for(var a,i,c;o=o.__;)if((a=o.__c)&&!a.__)try{if((i=a.constructor)&&i.getDerivedStateFromError!=null&&(a.setState(i.getDerivedStateFromError(t)),c=a.__d),a.componentDidCatch!=null&&(a.componentDidCatch(t,n||{}),c=a.__d),c)return a.__E=a}catch(m){t=m}throw t}},Io=0,fa=function(t){return t!=null&&t.constructor===void 0},G.prototype.setState=function(t,o){var e;e=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=ae({},this.state),typeof t=="function"&&(t=t(ae({},e),this.props)),t&&ae(e,t),t!=null&&this.__v&&(o&&this._sb.push(o),_t(this))},G.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),_t(this))},G.prototype.render=P,_e=[],Ze.__r=0,Ao=0;var ke,z,wt,Fo,Ke=0,Jo=[],$e=[],Vo=_.__b,Wo=_.__r,jo=_.diffed,Uo=_.__c,qo=_.unmount;function Qe(t,o){_.__h&&_.__h(z,t,Ke||o),Ke=0;var e=z.__H||(z.__H={__:[],__h:[]});return t>=e.__.length&&e.__.push({__V:$e}),e.__[t]}function D(t){return Ke=1,Xo(Yo,t)}function Xo(t,o,e){var n=Qe(ke++,2);if(n.t=t,!n.__c&&(n.__=[e?e(o):Yo(void 0,o),function(i){var c=n.__N?n.__N[0]:n.__[0],m=n.t(c,i);c!==m&&(n.__N=[m,n.__[1]],n.__c.setState({}))}],n.__c=z,!z.u)){z.u=!0;var a=z.shouldComponentUpdate;z.shouldComponentUpdate=function(i,c,m){if(!n.__c.__H)return!0;var l=n.__c.__H.__.filter(function(s){return s.__c});if(l.every(function(s){return!s.__N}))return!a||a.call(this,i,c,m);var h=!1;return l.forEach(function(s){if(s.__N){var g=s.__[0];s.__=s.__N,s.__N=void 0,g!==s.__[0]&&(h=!0)}}),!(!h&&n.__c.props===i)&&(!a||a.call(this,i,c,m))}}return n.__N||n.__}function O(t,o){var e=Qe(ke++,3);!_.__s&&Et(e.__H,o)&&(e.__=t,e.i=o,z.__H.__h.push(e))}function Mt(t,o){var e=Qe(ke++,4);!_.__s&&Et(e.__H,o)&&(e.__=t,e.i=o,z.__h.push(e))}function K(t){return Ke=5,et(function(){return{current:t}},[])}function et(t,o){var e=Qe(ke++,7);return Et(e.__H,o)?(e.__V=t(),e.i=o,e.__h=t,e.__V):e.__}function Ca(){for(var t;t=Jo.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(Ge),t.__H.__h.forEach(kt),t.__H.__h=[]}catch(o){t.__H.__h=[],_.__e(o,t.__v)}}_.__b=function(t){z=null,Vo&&Vo(t)},_.__r=function(t){Wo&&Wo(t),ke=0;var o=(z=t.__c).__H;o&&(wt===z?(o.__h=[],z.__h=[],o.__.forEach(function(e){e.__N&&(e.__=e.__N),e.__V=$e,e.__N=e.i=void 0})):(o.__h.forEach(Ge),o.__h.forEach(kt),o.__h=[])),wt=z},_.diffed=function(t){jo&&jo(t);var o=t.__c;o&&o.__H&&(o.__H.__h.length&&(Jo.push(o)!==1&&Fo===_.requestAnimationFrame||((Fo=_.requestAnimationFrame)||_a)(Ca)),o.__H.__.forEach(function(e){e.i&&(e.__H=e.i),e.__V!==$e&&(e.__=e.__V),e.i=void 0,e.__V=$e})),wt=z=null},_.__c=function(t,o){o.some(function(e){try{e.__h.forEach(Ge),e.__h=e.__h.filter(function(n){return!n.__||kt(n)})}catch(n){o.some(function(a){a.__h&&(a.__h=[])}),o=[],_.__e(n,e.__v)}}),Uo&&Uo(t,o)},_.unmount=function(t){qo&&qo(t);var o,e=t.__c;e&&e.__H&&(e.__H.__.forEach(function(n){try{Ge(n)}catch(a){o=a}}),e.__H=void 0,o&&_.__e(o,e.__v))};var Zo=typeof requestAnimationFrame=="function";function _a(t){var o,e=function(){clearTimeout(n),Zo&&cancelAnimationFrame(o),setTimeout(t)},n=setTimeout(e,100);Zo&&(o=requestAnimationFrame(e))}function Ge(t){var o=z,e=t.__c;typeof e=="function"&&(t.__c=void 0,e()),z=o}function kt(t){var o=z;t.__c=t.__(),z=o}function Et(t,o){return!t||t.length!==o.length||o.some(function(e,n){return e!==t[n]})}function Yo(t,o){return typeof o=="function"?o(t):o}var xt={};(function t(o,e,n,a){var i=!!(o.Worker&&o.Blob&&o.Promise&&o.OffscreenCanvas&&o.OffscreenCanvasRenderingContext2D&&o.HTMLCanvasElement&&o.HTMLCanvasElement.prototype.transferControlToOffscreen&&o.URL&&o.URL.createObjectURL);function c(){}function m(p){var d=e.exports.Promise,S=d!==void 0?d:o.Promise;return typeof S=="function"?new S(p):(p(c,c),null)}var l=function(){var p=Math.floor(16.666666666666668),d,S,M={},T=0;return typeof requestAnimationFrame=="function"&&typeof cancelAnimationFrame=="function"?(d=function(I){var E=Math.random();return M[E]=requestAnimationFrame(function x(B){T===B||T+p-1<B?(T=B,delete M[E],I()):M[E]=requestAnimationFrame(x)}),E},S=function(I){M[I]&&cancelAnimationFrame(M[I])}):(d=function(I){return setTimeout(I,p)},S=function(I){return clearTimeout(I)}),{frame:d,cancel:S}}(),h=function(){var p,d,S={};function M(T){function I(E,x){T.postMessage({options:E||{},callback:x})}T.init=function(x){var B=x.transferControlToOffscreen();T.postMessage({canvas:B},[B])},T.fire=function(x,B,te){if(d)return I(x,null),d;var L=Math.random().toString(36).slice(2);return d=m(function($){function Y(N){N.data.callback===L&&(delete S[L],T.removeEventListener("message",Y),d=null,te(),$())}T.addEventListener("message",Y),I(x,L),S[L]=Y.bind(null,{data:{callback:L}})}),d},T.reset=function(){T.postMessage({reset:!0});for(var x in S)S[x](),delete S[x]}}return function(){if(p)return p;if(!n&&i){var T=["var CONFETTI, SIZE = {}, module = {};","("+t.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join(`
`);try{p=new Worker(URL.createObjectURL(new Blob([T])))}catch(I){return typeof console!==void 0&&typeof console.warn=="function"&&console.warn("\u{1F38A} Could not load worker",I),null}M(p)}return p}}(),s={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function g(p,d){return d?d(p):p}function b(p){return p!=null}function f(p,d,S){return g(p&&b(p[d])?p[d]:s[d],S)}function C(p){return p<0?0:Math.floor(p)}function w(p,d){return Math.floor(Math.random()*(d-p))+p}function v(p){return parseInt(p,16)}function A(p){return p.map(y)}function y(p){var d=String(p).replace(/[^0-9a-f]/gi,"");return d.length<6&&(d=d[0]+d[0]+d[1]+d[1]+d[2]+d[2]),{r:v(d.substring(0,2)),g:v(d.substring(2,4)),b:v(d.substring(4,6))}}function H(p){var d=f(p,"origin",Object);return d.x=f(d,"x",Number),d.y=f(d,"y",Number),d}function ee(p){p.width=document.documentElement.clientWidth,p.height=document.documentElement.clientHeight}function Be(p){var d=p.getBoundingClientRect();p.width=d.width,p.height=d.height}function ue(p){var d=document.createElement("canvas");return d.style.position="fixed",d.style.top="0px",d.style.left="0px",d.style.pointerEvents="none",d.style.zIndex=p,d}function pe(p,d,S,M,T,I,E,x,B){p.save(),p.translate(d,S),p.rotate(I),p.scale(M,T),p.arc(0,0,1,E,x,B),p.restore()}function j(p){var d=p.angle*(Math.PI/180),S=p.spread*(Math.PI/180);return{x:p.x,y:p.y,wobble:Math.random()*10,wobbleSpeed:Math.min(.11,Math.random()*.1+.05),velocity:p.startVelocity*.5+Math.random()*p.startVelocity,angle2D:-d+(.5*S-Math.random()*S),tiltAngle:(Math.random()*(.75-.25)+.25)*Math.PI,color:p.color,shape:p.shape,tick:0,totalTicks:p.ticks,decay:p.decay,drift:p.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:p.gravity*3,ovalScalar:.6,scalar:p.scalar}}function fe(p,d){d.x+=Math.cos(d.angle2D)*d.velocity+d.drift,d.y+=Math.sin(d.angle2D)*d.velocity+d.gravity,d.wobble+=d.wobbleSpeed,d.velocity*=d.decay,d.tiltAngle+=.1,d.tiltSin=Math.sin(d.tiltAngle),d.tiltCos=Math.cos(d.tiltAngle),d.random=Math.random()+2,d.wobbleX=d.x+10*d.scalar*Math.cos(d.wobble),d.wobbleY=d.y+10*d.scalar*Math.sin(d.wobble);var S=d.tick++/d.totalTicks,M=d.x+d.random*d.tiltCos,T=d.y+d.random*d.tiltSin,I=d.wobbleX+d.random*d.tiltCos,E=d.wobbleY+d.random*d.tiltSin;if(p.fillStyle="rgba("+d.color.r+", "+d.color.g+", "+d.color.b+", "+(1-S)+")",p.beginPath(),d.shape==="circle")p.ellipse?p.ellipse(d.x,d.y,Math.abs(I-M)*d.ovalScalar,Math.abs(E-T)*d.ovalScalar,Math.PI/10*d.wobble,0,2*Math.PI):pe(p,d.x,d.y,Math.abs(I-M)*d.ovalScalar,Math.abs(E-T)*d.ovalScalar,Math.PI/10*d.wobble,0,2*Math.PI);else if(d.shape==="star")for(var x=Math.PI/2*3,B=4*d.scalar,te=8*d.scalar,L=d.x,$=d.y,Y=5,N=Math.PI/Y;Y--;)L=d.x+Math.cos(x)*te,$=d.y+Math.sin(x)*te,p.lineTo(L,$),x+=N,L=d.x+Math.cos(x)*B,$=d.y+Math.sin(x)*B,p.lineTo(L,$),x+=N;else p.moveTo(Math.floor(d.x),Math.floor(d.y)),p.lineTo(Math.floor(d.wobbleX),Math.floor(T)),p.lineTo(Math.floor(I),Math.floor(E)),p.lineTo(Math.floor(M),Math.floor(d.wobbleY));return p.closePath(),p.fill(),d.tick<d.totalTicks}function In(p,d,S,M,T){var I=d.slice(),E=p.getContext("2d"),x,B,te=m(function(L){function $(){x=B=null,E.clearRect(0,0,M.width,M.height),T(),L()}function Y(){n&&!(M.width===a.width&&M.height===a.height)&&(M.width=p.width=a.width,M.height=p.height=a.height),!M.width&&!M.height&&(S(p),M.width=p.width,M.height=p.height),E.clearRect(0,0,M.width,M.height),I=I.filter(function(N){return fe(E,N)}),I.length?x=l.frame(Y):$()}x=l.frame(Y),B=$});return{addFettis:function(L){return I=I.concat(L),te},canvas:p,promise:te,reset:function(){x&&l.cancel(x),B&&B()}}}function jt(p,d){var S=!p,M=!!f(d||{},"resize"),T=f(d,"disableForReducedMotion",Boolean),I=i&&!!f(d||{},"useWorker"),E=I?h():null,x=S?ee:Be,B=p&&E?!!p.__confetti_initialized:!1,te=typeof matchMedia=="function"&&matchMedia("(prefers-reduced-motion)").matches,L;function $(N,pt,ft){for(var he=f(N,"particleCount",C),He=f(N,"angle",Number),Re=f(N,"spread",Number),re=f(N,"startVelocity",Number),An=f(N,"decay",Number),Tn=f(N,"gravity",Number),Ln=f(N,"drift",Number),qt=f(N,"colors",A),Nn=f(N,"ticks",Number),Zt=f(N,"shapes"),Dn=f(N,"scalar"),Jt=H(N),Xt=he,ht=[],Bn=p.width*Jt.x,Hn=p.height*Jt.y;Xt--;)ht.push(j({x:Bn,y:Hn,angle:He,spread:Re,startVelocity:re,color:qt[Xt%qt.length],shape:Zt[w(0,Zt.length)],ticks:Nn,decay:An,gravity:Tn,drift:Ln,scalar:Dn}));return L?L.addFettis(ht):(L=In(p,ht,x,pt,ft),L.promise)}function Y(N){var pt=T||f(N,"disableForReducedMotion",Boolean),ft=f(N,"zIndex",Number);if(pt&&te)return m(function(re){re()});S&&L?p=L.canvas:S&&!p&&(p=ue(ft),document.body.appendChild(p)),M&&!B&&x(p);var he={width:p.width,height:p.height};E&&!B&&E.init(p),B=!0,E&&(p.__confetti_initialized=!0);function He(){if(E){var re={getBoundingClientRect:function(){if(!S)return p.getBoundingClientRect()}};x(re),E.postMessage({resize:{width:re.width,height:re.height}});return}he.width=he.height=null}function Re(){L=null,M&&o.removeEventListener("resize",He),S&&p&&(document.body.removeChild(p),p=null,B=!1)}return M&&o.addEventListener("resize",He,!1),E?E.fire(N,he,Re):$(N,he,Re)}return Y.reset=function(){E&&E.reset(),L&&L.reset()},Y}var ut;function Ut(){return ut||(ut=jt(null,{useWorker:!0,resize:!0})),ut}e.exports=function(){return Ut().apply(this,arguments)},e.exports.reset=function(){Ut().reset()},e.exports.create=jt})(function(){return typeof window!="undefined"?window:typeof self!="undefined"?self:this||{}}(),xt,!1);var $o=xt.exports,$i=xt.exports.create;async function q(t){let o=await new ne(t).awaitSelection(),e;o.hasOwnProperty("icon")||(e=await new R(t).awaitSelection());let n=await new U(o.name,t).awaitSelection();return{id:o.id,icon:e!=null?e:o.icon,name:n||o.name,mode:"any"}}function Q(t,o){var e;return(e=o.app.commands.commands[t])!=null?e:null}function k(n){var a=n,{icon:t,size:o}=a,e=Kt(a,["icon","size"]);let i=K(null);return Mt(()=>{(0,tt.setIcon)(i.current,t)},[t,o]),r("div",Fe({ref:i},e))}function X(t,o){let{isMobile:e,appId:n}=o.app;return t==="any"||t===n||t==="mobile"&&e||t==="desktop"&&!e}function Me(t){var e,n;let o="";for(let a of(e=t.hide.leftRibbon)!=null?e:[])o+=`div.side-dock-ribbon-action[aria-label="${a}"] {display: none !important; content-visibility: hidden;}`;for(let a of t.hide.statusbar)o+=`div.status-bar-item.plugin-${a} {display: none !important; content-visibility: hidden;}`;(n=document.head.querySelector("style#cmdr"))==null||n.remove(),o&&document.head.appendChild(createEl("style",{attr:{id:"cmdr"},text:o,type:"text/css"}))}async function St({target:t}){let o=activeDocument.createElement("canvas");activeDocument.body.appendChild(o),o.style.position="fixed",o.style.width="100vw",o.style.height="100vh",o.style.top="0px",o.style.left="0px",o.style["pointer-events"]="none",o.style["z-index"]="100";let e=$o.create(o,{resize:!0,useWorker:!0}),n=t.getBoundingClientRect();await e({particleCount:tt.Platform.isDesktop?160:80,startVelocity:55,spread:75,angle:90,drift:-1,ticks:250,origin:{x:(n.x+n.width/2)/activeWindow.innerWidth,y:(n.y+n.height/2)/activeWindow.innerHeight}}),o.remove()}function ot(t){activeDocument.body.style.setProperty("--cmdr-spacing",`${t}px`)}function Ee(t){let o=Object.keys(t.app.commands.commands).filter(n=>n.startsWith("cmdr:macro-"));for(let n of o)app.commands.removeCommand(n);let e=t.settings.macros;for(let[n,a]of Object.entries(e))t.addCommand({id:`macro-${n}`,name:a.name,callback:()=>{t.executeMacro(parseInt(n))}})}function oe(t){var n,a;let{classList:o,style:e}=document.body;e.setProperty("--at-button-height",((n=t.rowHeight)!=null?n:48)+"px"),e.setProperty("--at-button-width",((a=t.buttonWidth)!=null?a:48)+"px"),e.setProperty("--at-row-count",t.rowCount.toString()),e.setProperty("--at-spacing",t.spacing+"px"),e.setProperty("--at-offset",t.heightOffset+"px"),o.toggle("AT-multirow",t.rowCount>1),o.toggle("AT-row",!t.columnLayout),o.toggle("AT-column",t.columnLayout),o.toggle("AT-no-toolbar",t.rowCount===0)}function Go(){let{classList:t,style:o}=document.body;o.removeProperty("--at-button-height"),o.removeProperty("--at-button-width"),o.removeProperty("--at-row-count"),o.removeProperty("--at-spacing"),o.removeProperty("--at-offset"),t.remove("AT-multirow"),t.remove("AT-row"),t.remove("AT-column"),t.remove("AT-no-toolbar"),t.remove("advanced-toolbar")}function nt(t,o){t.mappedIcons.forEach(e=>{let n=o.app.commands.commands[e.commandID];n?n.icon=e.iconID:t.mappedIcons.remove(e)})}var Pn=require("obsidian");var xe=require("obsidian");var Qo=require("obsidian");function Ko({modal:t}){return r(P,null,r("p",null,u("Are you sure you want to delete the Command?")),r("div",{className:"modal-button-container"},r("button",{className:"mod-warning",onClick:async()=>{t.plugin.settings.confirmDeletion=!1,t.plugin.saveSettings(),t.remove=!0,t.close()}},u("Remove and don't ask again")),r("button",{className:"mod-warning",onClick:()=>{t.remove=!0,t.close()}},u("Remove")),r("button",{onClick:()=>{t.remove=!1,t.close()}},u("Cancel"))))}var F=class extends Qo.Modal{constructor(e){super(e.app);this.plugin=e}async onOpen(){this.titleEl.innerText=u("Remove Command"),this.containerEl.style.zIndex="99",this.reactComponent=r(Ko,{modal:this}),W(this.reactComponent,this.contentEl)}async didChooseRemove(){return this.open(),new Promise(e=>{this.onClose=()=>{var n;return e((n=this.remove)!=null?n:!1)}})}onClose(){W(null,this.contentEl)}};var Z=class{constructor(o,e){this.plugin=o,this.pairs=e}};var ve=class extends Z{constructor(e,n){super(e,n);this.actions=new Map;this.init(),this.plugin.register(()=>this.actions.forEach((a,i)=>this.removeAction(i)))}getFileExplorers(){return this.plugin.app.workspace.getLeavesOfType("file-explorer")}init(){this.plugin.app.workspace.onLayoutReady(()=>{for(let e of this.pairs)X(e.mode,this.plugin)&&(this.plugin.app.workspace.onLayoutReady(()=>{this.getFileExplorers().forEach(a=>{this.addAction(e,a)})}),this.plugin.registerEvent(this.plugin.app.workspace.on("layout-change",()=>{this.getFileExplorers().forEach(a=>{this.addAction(e,a)})})))})}reorder(){this.actions.forEach((e,n)=>this.removeAction(n,!0)),this.init()}async addCommand(e){this.pairs.push(e),this.plugin.app.workspace.onLayoutReady(()=>{this.getFileExplorers().forEach(a=>{this.addAction(e,a)})}),this.plugin.registerEvent(this.plugin.app.workspace.on("layout-change",()=>{this.getFileExplorers().forEach(a=>{this.addAction(e,a)})})),await this.plugin.saveSettings()}async removeCommand(e){this.pairs.remove(e),this.removeAction(e),await this.plugin.saveSettings()}buttonExists(e,n){return[...e.view.containerEl.querySelectorAll("div.nav-buttons-container > .cmdr.clickable-icon")].some(a=>a.getAttribute("data-cmdr")===n.icon+n.name)}addAction(e,n){var l,h,s,g,b;if(this.buttonExists(n,e))return;let a=createDiv({cls:"cmdr clickable-icon",attr:{"aria-label-position":"top","aria-label":e.name,"data-cmdr":e.icon+e.name}});this.actions.set(e,a),a.style.color=e.color==="#000000"||e.color===void 0?"inherit":e.color;let i=!1,c=()=>{a.empty(),(0,xe.setIcon)(a,e.icon),a.onclick=()=>this.plugin.app.commands.executeCommandById(e.id)},m=()=>{a.empty(),(0,xe.setIcon)(a,"trash"),a.onclick=async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(e)}};a.addEventListener("mouseleave",()=>{c(),i=!1}),a.addEventListener("mousemove",f=>{f.preventDefault(),f.stopImmediatePropagation(),f.shiftKey&&(i||m(),i=!0)}),a.addEventListener("contextmenu",f=>{f.stopImmediatePropagation(),new xe.Menu().addItem(C=>{C.setTitle(u("Add command")).setIcon("command").onClick(async()=>{let w=await q(this.plugin);this.addCommand(w)})}).addSeparator().addItem(C=>{C.setTitle(u("Change Icon")).setIcon("box").onClick(async()=>{let w=await new R(this.plugin).awaitSelection();w&&w!==e.icon&&(e.icon=w,await this.plugin.saveSettings(),this.reorder())})}).addItem(C=>{C.setTitle(u("Rename")).setIcon("text-cursor-input").onClick(async()=>{let w=await new U(e.name,this.plugin).awaitSelection();w&&w!==e.name&&(e.name=w,await this.plugin.saveSettings(),this.reorder())})}).addItem(C=>{C.dom.addClass("is-warning"),C.setTitle(u("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(e)})}).showAtMouseEvent(f)}),c(),(b=(g=(s=(h=(l=n.view)==null?void 0:l.containerEl)==null?void 0:h.querySelector)==null?void 0:s.call(h,"div.nav-buttons-container"))==null?void 0:g.appendChild)==null||b.call(g,a)}removeAction(e,n=!1){let a=this.actions.get(e);if(!!a){if(n){a.remove(),this.actions.delete(e);return}a.addClass("cmdr-ribbon-removing"),a.addEventListener("transitionend",async()=>{a.remove(),this.actions.delete(e)})}}};var se=require("obsidian");var at=class extends Z{async addCommand(o){this.pairs.push(o),await this.plugin.saveSettings()}async removeCommand(o){this.pairs.remove(o),await this.plugin.saveSettings()}reorder(){}addRemovableCommand(o,e,n,a,i){return c=>{var f;c.dom.addClass("cmdr"),c.dom.style.color=e.color==="#000000"||e.color===void 0?"inherit":e.color,c.setSection("cmdr"),c.dom.style.display="flex";let m=createDiv({cls:"cmdr-menu-more-options"}),l=null;m.addEventListener("click",C=>{C.preventDefault(),C.stopImmediatePropagation(),l?(l.hide(),l=null):l=new se.Menu().addItem(w=>{w.setTitle(u("Change Icon")).setIcon("box").onClick(async()=>{let v=await new R(n).awaitSelection();v&&v!==e.icon&&(e.icon=v,await n.saveSettings())})}).addItem(w=>{w.setTitle(u("Rename")).setIcon("text-cursor-input").onClick(async()=>{let v=await new U(e.name,n).awaitSelection();v&&v!==e.name&&(e.name=v,await n.saveSettings())})}).addItem(w=>{w.dom.addClass("is-warning"),w.setTitle(u("Delete")).setIcon("lucide-trash").onClick(async()=>{(!n.settings.confirmDeletion||await new F(n).didChooseRemove())&&b()})}).showAtMouseEvent(C)}),(0,se.setIcon)(m,"more-vertical"),c.dom.append(m),c.setTitle((f=e.name)!=null?f:o.name).setIcon(e.icon).onClick(()=>n.app.commands.executeCommandById(e.id));let h=!1,s=()=>{m.style.display="none"},g=()=>{m.style.display="block"},b=async()=>{c.dom.addClass("cmdr-removing"),a.registerDomEvent(c.dom,"transitionend",()=>{c.dom.remove()}),i.remove(e),await n.saveSettings()};a.registerDomEvent(c.dom,"mousemove",C=>{C.preventDefault(),C.stopImmediatePropagation(),h||g(),h=!0}),a.registerDomEvent(c.dom,"mouseleave",()=>{s(),h=!1}),s()}}addCommandAddButton(o,e,n){o.settings.showAddCommand&&e.addItem(a=>{a.setTitle(u("Add command")).setIcon("plus-circle").setSection("cmdr").onClick(async()=>{try{let i=await q(o);n.push(i),await o.saveSettings()}catch(i){console.log(i)}})})}},Se=class extends at{applyEditorMenuCommands(o){return async(e,n,a)=>{this.addCommandAddButton(o,e,o.settings.editorMenu);for(let i of o.settings.editorMenu){let c=Q(i.id,o);!c||!X(i.mode,o)||c.checkCallback&&!c.checkCallback(!0)||c.editorCheckCallback&&!c.editorCheckCallback(!0,n,a)||e.addItem(this.addRemovableCommand.call(this,c,i,o,e,o.settings.editorMenu))}}}},Pe=class extends at{applyFileMenuCommands(o){return async(e,n,a,i)=>{this.addCommandAddButton(o,e,o.settings.fileMenu);for(let c of o.settings.fileMenu){let m=Q(c.id,o);if(!!m&&!(m.checkCallback&&!m.checkCallback(!0))){if(m.editorCallback){if(!((i==null?void 0:i.view)instanceof se.MarkdownView))continue}else if(m.editorCheckCallback)if((i==null?void 0:i.view)instanceof se.MarkdownView){if(!m.editorCheckCallback(!0,i.view.editor,i.view))continue}else continue;e.addItem(this.addRemovableCommand.call(this,m,c,o,e,o.settings.fileMenu))}}}}};var ce=require("obsidian");var ge=class extends Z{constructor(e,n){super(e,n);this.buttons=new WeakMap;this.init()}addPageHeaderButton(e,n){let{id:a,icon:i,name:c}=n,{view:m}=e;if(!(m instanceof ce.ItemView))return;let l=this.buttonsFor(e,!0);if(!l||l.has(a))return;let h=m.addAction(i,c,()=>{this.plugin.app.workspace.setActiveLeaf(e,{focus:!0}),this.plugin.app.commands.executeCommandById(a)});l.set(a,h),h.addClasses(["cmdr-page-header",a]),h.style.color=n.color==="#000000"||n.color===void 0?"inherit":n.color,h.addEventListener("contextmenu",s=>{s.stopImmediatePropagation(),new ce.Menu().addItem(g=>{g.setTitle(u("Add command")).setIcon("command").onClick(async()=>{let b=await q(this.plugin);this.addCommand(b)})}).addSeparator().addItem(g=>{g.setTitle(u("Change Icon")).setIcon("box").onClick(async()=>{let b=await new R(this.plugin).awaitSelection();b&&b!==n.icon&&(n.icon=b,await this.plugin.saveSettings(),this.reorder())})}).addItem(g=>{g.setTitle(u("Rename")).setIcon("text-cursor-input").onClick(async()=>{let b=await new U(n.name,this.plugin).awaitSelection();b&&b!==n.name&&(n.name=b,await this.plugin.saveSettings(),this.reorder())})}).addItem(g=>{g.dom.addClass("is-warning"),g.setTitle(u("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(n)})}).showAtMouseEvent(s)})}init(){this.plugin.register(()=>{this.removeButtonsFromAllLeaves()}),this.plugin.registerEvent(this.plugin.app.workspace.on("layout-change",()=>{this.addButtonsToAllLeaves()})),this.plugin.app.workspace.onLayoutReady(()=>setTimeout(()=>this.addButtonsToAllLeaves(),100))}addAdderButton(e){var c;let{view:n}=e,a="cmdr-adder";if(!(n instanceof ce.ItemView)||(c=this.buttons.get(n))!=null&&c.has(a))return;let i=n.addAction("plus",u("Add new"),async()=>{this.addCommand(await q(this.plugin))});i.addClasses(["cmdr",a]),this.buttons.has(n)||this.buttons.set(n,new Map),this.buttons.get(n).set(a,i)}addButtonsToAllLeaves(e=!1){activeWindow.requestAnimationFrame(()=>this.plugin.app.workspace.iterateAllLeaves(n=>this.addButtonsToLeaf(n,e)))}removeButtonsFromAllLeaves(){activeWindow.requestAnimationFrame(()=>this.plugin.app.workspace.iterateAllLeaves(e=>this.removeButtonsFromLeaf(e)))}buttonsFor(e,n=!1){if(e.view instanceof ce.ItemView)return n&&!this.buttons.has(e.view)&&this.buttons.set(e.view,new Map),this.buttons.get(e.view)}addButtonsToLeaf(e,n=!1){var a;if(e.view instanceof ce.ItemView){if(n)this.removeButtonsFromLeaf(e);else if((a=this.buttonsFor(e))!=null&&a.size)return;for(let i=this.pairs.length-1;i>=0;i--){let c=this.pairs[i];X(c.mode,this.plugin)&&this.addPageHeaderButton(e,c)}this.plugin.settings.showAddCommand&&this.addAdderButton(e)}}removeButtonsFromLeaf(e){let n=this.buttonsFor(e);if(n){for(let a of n.values())a.detach();n==null||n.clear()}}reorder(){this.addButtonsToAllLeaves(!0)}async addCommand(e){this.pairs.push(e),this.addButtonsToAllLeaves(!0),await this.plugin.saveSettings()}async removeCommand(e){this.pairs.remove(e),this.addButtonsToAllLeaves(!0),await this.plugin.saveSettings()}};var de=require("obsidian");var be=class extends Z{constructor(e,n){super(e,n);this.actions=new Map;this.addBtn=createDiv({cls:"cmdr status-bar-item cmdr-adder",attr:{"aria-label-position":"top","aria-label":u("Add new")}});this.init(),this.plugin.register(()=>this.actions.forEach((a,i)=>this.removeAction(i)))}init(){this.plugin.app.workspace.onLayoutReady(()=>{this.container=this.plugin.app.statusBar.containerEl;for(let e of this.pairs)Q(e.id,this.plugin)||this.pairs.remove(e),X(e.mode,this.plugin)&&this.addAction(e);this.plugin.saveSettings(),this.plugin.registerDomEvent(this.container,"contextmenu",e=>{e.target===this.container&&new de.Menu().addItem(n=>{n.setTitle(u("Add command")).setIcon("command").onClick(async()=>{let a=await q(this.plugin);this.addCommand(a)})}).showAtMouseEvent(e)}),this.plugin.register(()=>this.addBtn.remove()),(0,de.setIcon)(this.addBtn,"plus"),this.addBtn.onclick=async()=>{let e=await q(this.plugin);this.addCommand(e),this.reorder()},this.plugin.settings.showAddCommand&&this.container.prepend(this.addBtn)})}reorder(){this.addBtn.remove(),this.actions.forEach((e,n)=>this.removeAction(n,!0)),this.init()}async addCommand(e){this.pairs.push(e),this.addAction(e),await this.plugin.saveSettings()}async removeCommand(e){this.pairs.remove(e),this.removeAction(e),await this.plugin.saveSettings()}addAction(e){let n=createDiv({cls:"cmdr status-bar-item clickable-icon",attr:{"aria-label-position":"top","aria-label":e.name}});this.actions.set(e,n),n.style.color=e.color==="#000000"||e.color===void 0?"inherit":e.color;let a=!1,i=()=>{n.empty(),(0,de.setIcon)(n,e.icon),n.onclick=()=>this.plugin.app.commands.executeCommandById(e.id)},c=()=>{n.empty(),(0,de.setIcon)(n,"trash"),n.onclick=async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(e)}};n.addEventListener("mouseleave",()=>{i(),a=!1}),n.addEventListener("mousemove",m=>{m.preventDefault(),m.stopImmediatePropagation(),m.shiftKey&&(a||c(),a=!0)}),n.addEventListener("contextmenu",m=>{m.stopImmediatePropagation(),new de.Menu().addItem(l=>{l.setTitle(u("Add command")).setIcon("command").onClick(async()=>{let h=await q(this.plugin);this.addCommand(h)})}).addSeparator().addItem(l=>{l.setTitle(u("Change Icon")).setIcon("box").onClick(async()=>{let h=await new R(this.plugin).awaitSelection();h&&h!==e.icon&&(e.icon=h,await this.plugin.saveSettings(),this.reorder())})}).addItem(l=>{l.setTitle(u("Rename")).setIcon("text-cursor-input").onClick(async()=>{let h=await new U(e.name,this.plugin).awaitSelection();h&&h!==e.name&&(e.name=h,await this.plugin.saveSettings(),this.reorder())})}).addItem(l=>{l.dom.addClass("is-warning"),l.setTitle(u("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(e)})}).showAtMouseEvent(m)}),i(),this.container.prepend(n)}removeAction(e,n=!1){let a=this.actions.get(e);if(!!a){if(n){a.remove(),this.actions.delete(e);return}a.addClass("cmdr-ribbon-removing"),a.addEventListener("transitionend",async()=>{a.remove(),this.actions.delete(e)})}}};var en=require("obsidian");var dt=require("obsidian");var J=require("obsidian");var Tt=require("obsidian");var tn=["https://github.com/jsmorabito","https://github.com/phibr0","https://www.youtube.com/watch?v=dQw4w9WgXcQ"];function Pt(){let[t,o]=D(0);return r("div",{className:"cmdr-credits"},r("span",{onClick:()=>{o(e=>e+1),location.replace(tn[t%tn.length])}},u("By Johnny\u2728 and phibr0")))}function wa(t,o){for(var e in o)t[e]=o[e];return t}function At(t,o){for(var e in t)if(e!=="__source"&&!(e in o))return!0;for(var n in o)if(n!=="__source"&&t[n]!==o[n])return!0;return!1}function on(t){this.props=t}function ln(t,o){function e(a){var i=this.props.ref,c=i==a.ref;return!c&&i&&(i.call?i(null):i.current=null),o?!o(this.props,a)||!c:At(this.props,a)}function n(a){return this.shouldComponentUpdate=e,r(t,a)}return n.displayName="Memo("+(t.displayName||t.name)+")",n.prototype.isReactComponent=!0,n.__f=!0,n}(on.prototype=new G).isPureReactComponent=!0,on.prototype.shouldComponentUpdate=function(t,o){return At(this.props,t)||At(this.state,o)};var nn=_.__b;_.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),nn&&nn(t)};var Ss=typeof Symbol!="undefined"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;var ka=_.__e;_.__e=function(t,o,e,n){if(t.then){for(var a,i=o;i=i.__;)if((a=i.__c)&&a.__c)return o.__e==null&&(o.__e=e.__e,o.__k=e.__k),a.__c(t,o)}ka(t,o,e,n)};var an=_.unmount;function un(t,o,e){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(n){typeof n.__c=="function"&&n.__c()}),t.__c.__H=null),(t=wa({},t)).__c!=null&&(t.__c.__P===e&&(t.__c.__P=o),t.__c=null),t.__k=t.__k&&t.__k.map(function(n){return un(n,o,e)})),t}function pn(t,o,e){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(n){return pn(n,o,e)}),t.__c&&t.__c.__P===o&&(t.__e&&e.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=e)),t}function It(){this.__u=0,this.t=null,this.__b=null}function fn(t){var o=t.__.__c;return o&&o.__a&&o.__a(t)}function it(){this.u=null,this.o=null}_.unmount=function(t){var o=t.__c;o&&o.__R&&o.__R(),o&&t.__h===!0&&(t.type=null),an&&an(t)},(It.prototype=new G).__c=function(t,o){var e=o.__c,n=this;n.t==null&&(n.t=[]),n.t.push(e);var a=fn(n.__v),i=!1,c=function(){i||(i=!0,e.__R=null,a?a(m):m())};e.__R=c;var m=function(){if(!--n.__u){if(n.state.__a){var h=n.state.__a;n.__v.__k[0]=pn(h,h.__c.__P,h.__c.__O)}var s;for(n.setState({__a:n.__b=null});s=n.t.pop();)s.forceUpdate()}},l=o.__h===!0;n.__u++||l||n.setState({__a:n.__b=n.__v.__k[0]}),t.then(c,c)},It.prototype.componentWillUnmount=function(){this.t=[]},It.prototype.render=function(t,o){if(this.__b){if(this.__v.__k){var e=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=un(this.__b,e,n.__O=n.__P)}this.__b=null}var a=o.__a&&r(P,null,t.fallback);return a&&(a.__h=null),[r(P,null,o.__a?null:t.children),a]};var rn=function(t,o,e){if(++e[1]===e[0]&&t.o.delete(o),t.props.revealOrder&&(t.props.revealOrder[0]!=="t"||!t.o.size))for(e=t.u;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;t.u=e=e[2]}};(it.prototype=new G).__a=function(t){var o=this,e=fn(o.__v),n=o.o.get(t);return n[0]++,function(a){var i=function(){o.props.revealOrder?(n.push(a),rn(o,t,n)):a()};e?e(i):i()}},it.prototype.render=function(t){this.u=null,this.o=new Map;var o=we(t.children);t.revealOrder&&t.revealOrder[0]==="b"&&o.reverse();for(var e=o.length;e--;)this.o.set(o[e],this.u=[1,0,this.u]);return t.children},it.prototype.componentDidUpdate=it.prototype.componentDidMount=function(){var t=this;this.o.forEach(function(o,e){rn(t,e,o)})};var Ma=typeof Symbol!="undefined"&&Symbol.for&&Symbol.for("react.element")||60103,Ea=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,xa=typeof document!="undefined",Sa=function(t){return(typeof Symbol!="undefined"&&typeof Symbol()=="symbol"?/fil|che|rad/i:/fil|che|ra/i).test(t)};G.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(G.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(o){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:o})}})});var sn=_.event;function Pa(){}function Ia(){return this.cancelBubble}function Aa(){return this.defaultPrevented}_.event=function(t){return sn&&(t=sn(t)),t.persist=Pa,t.isPropagationStopped=Ia,t.isDefaultPrevented=Aa,t.nativeEvent=t};var Ta,cn={configurable:!0,get:function(){return this.class}},dn=_.vnode;_.vnode=function(t){var o=t.type,e=t.props,n=e;if(typeof o=="string"){var a=o.indexOf("-")===-1;for(var i in n={},e){var c=e[i];xa&&i==="children"&&o==="noscript"||i==="value"&&"defaultValue"in e&&c==null||(i==="defaultValue"&&"value"in e&&e.value==null?i="value":i==="download"&&c===!0?c="":/ondoubleclick/i.test(i)?i="ondblclick":/^onchange(textarea|input)/i.test(i+o)&&!Sa(e.type)?i="oninput":/^onfocus$/i.test(i)?i="onfocusin":/^onblur$/i.test(i)?i="onfocusout":/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(i)?i=i.toLowerCase():a&&Ea.test(i)?i=i.replace(/[A-Z0-9]/g,"-$&").toLowerCase():c===null&&(c=void 0),/^oninput$/i.test(i)&&(i=i.toLowerCase(),n[i]&&(i="oninputCapture")),n[i]=c)}o=="select"&&n.multiple&&Array.isArray(n.value)&&(n.value=we(e.children).forEach(function(m){m.props.selected=n.value.indexOf(m.props.value)!=-1})),o=="select"&&n.defaultValue!=null&&(n.value=we(e.children).forEach(function(m){m.props.selected=n.multiple?n.defaultValue.indexOf(m.props.value)!=-1:n.defaultValue==m.props.value})),t.props=n,e.class!=e.className&&(cn.enumerable="className"in e,e.className!=null&&(n.class=e.className),Object.defineProperty(n,"className",cn))}t.$$typeof=Ma,dn&&dn(t)};var mn=_.__r;_.__r=function(t){mn&&mn(t),Ta=t.__c};var hn='<svg viewbox="0 0 118 105" width="118" xmlns="http://www.w3.org/2000/svg" height="105" style="-webkit-print-color-adjust:exact" fill="none"><defs><clipPath id="a" class="frame-clip"><rect rx="0" ry="0" width="118" height="105"/></clipPath></defs><g clip-path="url(#a)"><rect rx="0" ry="0" width="118" height="105" class="frame-background"/><g class="frame-children"><g class="any-key" style="fill:#000"><path d="M35.2 1C25.7 1 18 8.7 18 18.2v51.6C18 79.3 25.7 87 35.2 87h45.6C90.3 87 98 79.3 98 69.8V18.2C98 8.7 90.3 1 80.8 1H35.2Zm0 4h45.6C88.1 5 94 10.9 94 18.2v37.6C94 63.1 88.1 69 80.8 69H35.2C27.9 69 22 63.1 22 55.8V18.2C22 10.9 27.9 5 35.2 5ZM56 19v14.5L43.6 26l-2.1 3.4L54.1 37l-12.6 7.6 2.1 3.4L56 40.5V55h4V40.5L72.4 48l2.1-3.4L61.9 37l12.6-7.6-2.1-3.4L60 33.5V19h-4ZM22 66.8c3.2 3.8 7.9 6.2 13.2 6.2h45.6c5.3 0 10-2.4 13.2-6.2v3C94 77.1 88.1 83 80.8 83H35.2C27.9 83 22 77.1 22 69.8v-3Z" style="fill:var(--text-accent);fill-opacity:1"/><path d="M35.2 1C25.7 1 18 8.7 18 18.2v51.6C18 79.3 25.7 87 35.2 87h45.6C90.3 87 98 79.3 98 69.8V18.2C98 8.7 90.3 1 80.8 1H35.2Zm0 4h45.6C88.1 5 94 10.9 94 18.2v37.6C94 63.1 88.1 69 80.8 69H35.2C27.9 69 22 63.1 22 55.8V18.2C22 10.9 27.9 5 35.2 5ZM56 19v14.5L43.6 26l-2.1 3.4L54.1 37l-12.6 7.6 2.1 3.4L56 40.5V55h4V40.5L72.4 48l2.1-3.4L61.9 37l12.6-7.6-2.1-3.4L60 33.5V19h-4ZM22 66.8c3.2 3.8 7.9 6.2 13.2 6.2h45.6c5.3 0 10-2.4 13.2-6.2v3C94 77.1 88.1 83 80.8 83H35.2C27.9 83 22 77.1 22 69.8v-3Z" style="fill:none;stroke-width:1;stroke:var(--text-accent);stroke-opacity:1" class="stroke-shape"/></g><path d="M20.11 53.587 3 63.9"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="b" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M20.11 53.587 3 63.9" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#b)"/></g><path d="m96 53.567 19.161-11.55"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="c" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="m96 53.567 19.161-11.55" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#c)"/></g><path d="M20.11 53.587 3 63.9"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="d" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M20.11 53.587 3 63.9" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#d)"/></g><path d="M42 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="e" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M42 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#e)"/></g><path d="M74 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="f" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M74 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#f)"/></g><path d="m96 53.567 19.161-11.55"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="g" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="m96 53.567 19.161-11.55" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#g)"/></g><path d="M42 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="h" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M42 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#h)"/></g><path d="M74 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="i" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M74 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#i)"/></g></g></g></svg>';var vn='<svg width="124" height="189" viewBox="0 0 124 189" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M120 175.999L104.432 133.999C74.7524 140.282 54.9222 146.129 23.5771 137.861C16.3206 150.681 13.2565 163.179 6 175.999C11.7765 173.272 14.6163 173.349 19.0573 175.999C25.2389 172.439 27.3603 173.689 31.1101 175.999C39.3142 169.983 43.4376 171.766 50.696 175.999C57.2083 171.119 60.7022 171.597 66.7665 175.999C76.3874 170.399 80.6872 172.41 88.3505 175.994L88.3612 175.999C94.0886 172.481 97.1438 172.819 102.423 175.999C109.021 172.023 112.937 173.03 120 175.999Z" fill="#A80000" stroke="#A80000" stroke-width="4" /><path d="M37.156 80.2386L85.6308 78.676L53.8425 8.1636L37.156 80.2386Z" fill="#B50D0D" stroke="#B50D0D" stroke-width="4" /><ellipse cx="85" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="93" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="101" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="112" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="116" cy="177.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="76" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="67" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="58" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="49" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="42" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="37" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="31" cy="174.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="29" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="25" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="20" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="14" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="8" cy="177.499" rx="8" ry="6.5" fill="#E9E9E9" /><path d="M47 166.999V183.999H59" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M79 166.999V183.999H91" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M40.2 82.9993C30.7 82.9993 23 90.6993 23 100.199V151.799C23 161.299 30.7 168.999 40.2 168.999H85.8C95.3 168.999 103 161.299 103 151.799V100.199C103 90.6993 95.3 82.9993 85.8 82.9993H40.2ZM40.2 86.9993H85.8C93.1 86.9993 99 92.8993 99 100.199V137.799C99 145.099 93.1 150.999 85.8 150.999H40.2C32.9 150.999 27 145.099 27 137.799V100.199C27 92.8993 32.9 86.9993 40.2 86.9993ZM61 100.999V115.499L48.6 107.999L46.5 111.399L59.1 118.999L46.5 126.599L48.6 129.999L61 122.499V136.999H65V122.499L77.4 129.999L79.5 126.599L66.9 118.999L79.5 111.399L77.4 107.999L65 115.499V100.999H61ZM27 148.799C30.2 152.599 34.9 154.999 40.2 154.999H85.8C91.1 154.999 95.8 152.599 99 148.799V151.799C99 159.099 93.1 164.999 85.8 164.999H40.2C32.9 164.999 27 159.099 27 151.799V148.799Z" fill="#28CC39" stroke="#28CC39" /><path d="M25 135.999L7.99997 146.603" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M120.104 123.488L101 135.614" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M84.5 177.999V173.499H75.5V177.999H84.5ZM80 183.999H75.5V188.499H80V183.999ZM90 188.499C92.4853 188.499 94.5 186.485 94.5 183.999C94.5 181.514 92.4853 179.499 90 179.499V188.499ZM75.5 177.999V183.999H84.5V177.999H75.5ZM80 188.499H90V179.499H80V188.499Z" fill="#303030" /><path d="M52.5 177.999V173.499H43.5V177.999H52.5ZM48 183.999H43.5V188.499H48V183.999ZM58 188.499C60.4853 188.499 62.5 186.485 62.5 183.999C62.5 181.514 60.4853 179.499 58 179.499V188.499ZM43.5 177.999V183.999H52.5V177.999H43.5ZM48 188.499H58V179.499H48V188.499Z" fill="#303030" /><ellipse cx="38.1555" cy="80.2062" rx="8" ry="6.5" transform="rotate(-1.84634 38.1555 80.2062)" fill="#E9E9E9" /><ellipse cx="43.0885" cy="78.046" rx="8" ry="6.5" transform="rotate(-1.84634 43.0885 78.046)" fill="#E9E9E9" /><ellipse cx="46.1513" cy="79.9483" rx="8" ry="6.5" transform="rotate(-1.84634 46.1513 79.9483)" fill="#E9E9E9" /><ellipse cx="54.0827" cy="77.692" rx="8" ry="6.5" transform="rotate(-1.84634 54.0827 77.692)" fill="#E9E9E9" /><ellipse cx="59.1445" cy="79.5299" rx="8" ry="6.5" transform="rotate(-1.84634 59.1445 79.5299)" fill="#E9E9E9" /><ellipse cx="67.0759" cy="77.2731" rx="8" ry="6.5" transform="rotate(-1.84634 67.0759 77.2731)" fill="#E9E9E9" /><ellipse cx="70.1389" cy="79.1754" rx="8" ry="6.5" transform="rotate(-1.84634 70.1389 79.1754)" fill="#E9E9E9" /><ellipse cx="80.0692" cy="76.8541" rx="8" ry="6.5" transform="rotate(-1.84634 80.0692 76.8541)" fill="#E9E9E9" /><ellipse cx="83.1321" cy="78.7565" rx="8" ry="6.5" transform="rotate(-1.84634 83.1321 78.7565)" fill="#E9E9E9" /><ellipse cx="53.8585" cy="7.66343" rx="8" ry="6.5" transform="rotate(-1.84634 53.8585 7.66343)" fill="#E9E9E9" /><path d="M104.5 127.999C75.5109 146.65 55.8196 154.503 21.5 133.999" stroke="#750000" stroke-width="4" /><path d="M68.2248 148.783C69.0243 149.525 69.5328 150.357 69.7415 151.062C69.9573 151.791 69.8141 152.195 69.6516 152.37C69.4892 152.545 69.0976 152.718 68.3543 152.557C67.6357 152.402 66.7679 151.957 65.9684 151.215C65.1688 150.473 64.6603 149.641 64.4517 148.936C64.2359 148.207 64.379 147.803 64.5415 147.628C64.7039 147.453 65.0955 147.28 65.8389 147.441C66.5574 147.596 67.4252 148.041 68.2248 148.783Z" stroke="#750000" stroke-width="2" /><path d="M62.5372 151.611C61.7935 152.57 60.9314 153.229 60.1818 153.547C59.398 153.88 58.9595 153.766 58.7766 153.624C58.5937 153.482 58.3744 153.086 58.5013 152.244C58.6227 151.439 59.0467 150.44 59.7903 149.481C60.534 148.522 61.3961 147.863 62.1457 147.545C62.9296 147.212 63.3681 147.326 63.551 147.468C63.7339 147.61 63.9532 148.006 63.8262 148.848C63.7048 149.653 63.2809 150.652 62.5372 151.611Z" stroke="#750000" stroke-width="2" /></svg>';var gn='<svg width="152" height="220" viewBox="0 0 127 184" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.82568 174.501L23.3257 131.001C52.8749 137.508 72.6181 143.564 103.826 135.001C111.05 148.279 114.101 161.223 121.326 174.501C115.575 171.676 112.747 171.756 108.326 174.501C102.171 170.814 100.059 172.108 96.3257 174.501C88.1576 168.27 84.0522 170.116 76.8257 174.501C70.342 169.446 66.8634 169.941 60.8257 174.501C51.247 168.701 46.9661 170.784 39.3364 174.496L39.3257 174.501C33.6234 170.857 30.5816 171.207 25.3257 174.501C18.7562 170.383 14.8574 171.426 7.82568 174.501Z" fill="#8B6CEF" stroke="#8B6CEF" stroke-width="4" /><path d="M80.3257 164.501V181.501H68.3257" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M48.3257 164.501V181.501H36.3257" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M87.1257 80.501C96.6257 80.501 104.326 88.201 104.326 97.701V149.301C104.326 158.801 96.6257 166.501 87.1257 166.501H41.5257C32.0257 166.501 24.3257 158.801 24.3257 149.301V97.701C24.3257 88.201 32.0257 80.501 41.5257 80.501H87.1257ZM87.1257 84.501H41.5257C34.2257 84.501 28.3257 90.401 28.3257 97.701V135.301C28.3257 142.601 34.2257 148.501 41.5257 148.501H87.1257C94.4257 148.501 100.326 142.601 100.326 135.301V97.701C100.326 90.401 94.4257 84.501 87.1257 84.501ZM66.3257 98.501V113.001L78.7257 105.501L80.8257 108.901L68.2257 116.501L80.8257 124.101L78.7257 127.501L66.3257 120.001V134.501H62.3257V120.001L49.9257 127.501L47.8257 124.101L60.4257 116.501L47.8257 108.901L49.9257 105.501L62.3257 113.001V98.501H66.3257ZM100.326 146.301C97.1257 150.101 92.4257 152.501 87.1257 152.501H41.5257C36.2257 152.501 31.5257 150.101 28.3257 146.301V149.301C28.3257 156.601 34.2257 162.501 41.5257 162.501H87.1257C94.4257 162.501 100.326 156.601 100.326 149.301V146.301Z" fill="#FF820F" stroke="#FF820F" /><path d="M102.326 133.501L119.326 144.105" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M7.22161 120.99L26.3257 133.116" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M9.32568 136.501L3.32568 105.501" stroke="black" stroke-width="4" /><path d="M3.36682 105.807L1.95891 99.5009" stroke="white" stroke-width="4" /><path d="M39.8257 78.501H88.3257L58.8257 7.00098L39.8257 78.501Z" fill="#8B6CEF" stroke="#8B6CEF" stroke-width="4" /><path d="M70.3257 57.119L69.6786 59.1104L69.5664 59.4559H69.2031H67.1092L68.8032 60.6866L69.0971 60.9002L68.9849 61.2457L68.3378 63.237L70.0318 62.0063L70.3257 61.7928L70.6196 62.0063L72.3136 63.237L71.6665 61.2457L71.5543 60.9002L71.8481 60.6866L73.5421 59.4559H71.4483H71.085L70.9727 59.1104L70.3257 57.119Z" fill="#FFF50F" stroke="#FFF50F" /><path d="M58.3537 35.403L55.5616 39.5509L55.3588 39.8523L55.0095 39.7525L50.2018 38.3788L53.2839 42.316L53.5078 42.602L53.3049 42.9034L50.5129 47.0512L55.2098 45.3367L55.551 45.2121L55.7749 45.4982L58.857 49.4353L58.6778 44.4385L58.6647 44.0755L59.006 43.9509L63.7029 42.2364L58.8952 40.8627L58.5459 40.7629L58.5329 40.3999L58.3537 35.403Z" fill="#FFF50F" stroke="#FFF50F" /><path d="M22.8257 125.501C51.8148 144.152 71.5061 151.504 105.826 131.001" stroke="#5845CF" stroke-width="4" /><path d="M58.101 145.285C57.3014 146.027 56.7929 146.859 56.5843 147.564C56.3685 148.293 56.5117 148.696 56.6741 148.871C56.8366 149.046 57.2281 149.219 57.9715 149.059C58.69 148.903 59.5579 148.458 60.3574 147.716C61.1569 146.975 61.6654 146.142 61.8741 145.437C62.0899 144.708 61.9467 144.305 61.7843 144.13C61.6218 143.955 61.2302 143.782 60.4869 143.942C59.7683 144.098 58.9005 144.543 58.101 145.285Z" stroke="#5845CF" stroke-width="2" /><path d="M63.7886 148.113C64.5322 149.072 65.3943 149.731 66.1439 150.049C66.9278 150.381 67.3663 150.268 67.5492 150.126C67.7321 149.984 67.9514 149.588 67.8244 148.746C67.703 147.94 67.2791 146.941 66.5354 145.982C65.7917 145.023 64.9296 144.364 64.18 144.046C63.3962 143.714 62.9577 143.827 62.7748 143.969C62.5919 144.111 62.3726 144.508 62.4995 145.349C62.6209 146.155 63.0449 147.154 63.7886 148.113Z" stroke="#5845CF" stroke-width="2" /><ellipse rx="41.5" ry="4" transform="matrix(-1 0 0 1 62.8257 79.501)" fill="#8B6CEF" /><path d="M48.7999 64.3399L48.7051 67.8856L48.6954 68.2487L48.3471 68.3517L44.9456 69.3573L48.2885 70.5431L48.6309 70.6645L48.6212 71.0276L48.5264 74.5733L50.6872 71.7605L50.9085 71.4724L51.2508 71.5939L54.5937 72.7796L52.5863 69.8554L52.3807 69.5559L52.602 69.2679L54.7627 66.455L51.3613 67.4606L51.0129 67.5636L50.8073 67.2641L48.7999 64.3399Z" fill="#FFF50F" stroke="#FFF50F" /></svg>';var bn=require("obsidian"),Ba={9:gn,11:vn};function Ha(){var t;return r("div",{class:"cmdr-icon-wrapper",dangerouslySetInnerHTML:{__html:(t=Ba[(0,bn.moment)().month()])!=null?t:hn}})}var Ce=ln(Ha);function Lt({manifest:t}){let o=r("button",{className:"mod-cta",onClick:n=>{St(n),setTimeout(()=>location.replace("https://forms.gle/hPjn61G9bqqFb3256"),Math.random()*800+500)}},r(k,{icon:"message-square",size:20}),u("Leave feedback")),e=r("button",{className:"mod-cta",onClick:n=>{St(n),setTimeout(()=>location.replace("https://ko-fi.com/phibr0"),Math.random()*800+500)}},r(k,{icon:"coffee",size:20}),u("Support development"));return r("div",{className:"cmdr-about"},Tt.Platform.isMobile&&[r("hr",null),o,e],Tt.Platform.isDesktop&&[r("div",{className:"setting-item mod-toggle",style:{width:"100%",borderTop:"1px solid var(--background-modifier-border)",paddingTop:"18px"}},r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},u("Leave feedback")),r("div",{className:"setting-item-description"},u("Share feedback, issues, and ideas with our feedback form."))),r("div",{className:"setting-item-control"},o)),r("div",{className:"setting-item mod-toggle",style:{width:"100%"}},r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},u("Donate")),r("div",{className:"setting-item-description"},u("Consider donating to support development."))),r("div",{className:"setting-item-control"},e)),r("hr",null)],r(Ce,null),r("b",null,t.name),r(Pt,null),r("a",{className:"cmdr-version",href:"https://github.com/phibr0/obsidian-commander/releases/tag/"+t.version},t.version))}var V=require("obsidian");function Nt(t,o){if(t.empty(),new V.Setting(t).setName("Toolbar Row Count").setDesc("Set how many Rows the Mobile Toolbar should have. Set this to 0 to remove the Toolbar.").addSlider(n=>n.setLimits(0,5,1).setValue(o.settings.advancedToolbar.rowCount).setDynamicTooltip().onChange(async a=>{o.settings.advancedToolbar.rowCount=a,await o.saveSettings(),oe(o.settings.advancedToolbar)})),new V.Setting(t).setName("Column Layout").setDesc("Use a column based layout instead of the default row. This makes it easier to arrange the Commands.").addToggle(n=>n.setValue(o.settings.advancedToolbar.columnLayout).onChange(async a=>{o.settings.advancedToolbar.columnLayout=a,await o.saveSettings(),oe(o.settings.advancedToolbar)})),new V.Setting(t).setName("Bottom Offset").setDesc("Offset the Toolbar from the Bottom of the Screen. This is useful if the toolbar is partially obscured by other UI Elements.").addSlider(n=>n.setLimits(0,32,1).setValue(o.settings.advancedToolbar.heightOffset).setDynamicTooltip().onChange(async a=>{o.settings.advancedToolbar.heightOffset=a,await o.saveSettings(),oe(o.settings.advancedToolbar)})),V.Platform.isMobile){let n=document.createDocumentFragment();n.appendChild(createEl("h3",{text:"Custom Icons"})),t.appendChild(n),o.getCommandsWithoutIcons().forEach(a=>{new V.Setting(t).setName(a.name).setDesc(`ID: ${a.id}`).addButton(i=>{var m;let c=i.buttonEl.createDiv({cls:"AT-settings-icon"});if(a.icon)(0,V.setIcon)(c,a.icon);else{let l=(m=o.settings.advancedToolbar.mappedIcons.find(h=>h.commandID===a.id))==null?void 0:m.iconID;l?(0,V.setIcon)(c,l):i.setButtonText("No Icon")}i.onClick(async()=>{let l=await new R(o).awaitSelection(),h=o.settings.advancedToolbar.mappedIcons.find(s=>s.commandID===a.id);h?h.iconID=l:o.settings.advancedToolbar.mappedIcons.push({commandID:a.id,iconID:l}),await o.saveSettings(),nt(o.settings.advancedToolbar,o),Nt(t,o)})}).addExtraButton(i=>{i.setIcon("reset").setTooltip("Reset to default - Requires a restart").onClick(async()=>{o.settings.advancedToolbar.mappedIcons=o.settings.advancedToolbar.mappedIcons.filter(c=>c.commandID!==a.id),delete a.icon,delete o.app.commands.commands[a.id].icon,await o.saveSettings(),Nt(t,o),new V.Notice("If the default Icon doesn't appear, you might have to restart Obsidian.")})})})}let e=t.appendChild(createEl("div",{cls:"cmdr-sep-con",attr:{style:"margin-top: 64px"}}));e.appendChild(createEl("div",{text:"Advanced Settings",attr:{style:"margin-bottom: 8px; font-weight: bold"}})),new V.Setting(e).setName("Button Height").setDesc("Change the Height of each Button inside the Mobile Toolbar (in px).").addText(n=>{var a,i;return n.setValue((i=(a=o.settings.advancedToolbar.rowHeight)==null?void 0:a.toString())!=null?i:"48").setPlaceholder("48").onChange(async c=>{let m=Number(c),l=isNaN(m);n.inputEl.toggleClass("is-invalid",l),l||(o.settings.advancedToolbar.rowHeight=m,await o.saveSettings(),oe(o.settings.advancedToolbar))})}),new V.Setting(e).setName("Button Width").setDesc("Change the Width of each Button inside the Mobile Toolbar (in px).").addText(n=>{var a,i;return n.setValue((i=(a=o.settings.advancedToolbar.buttonWidth)==null?void 0:a.toString())!=null?i:"48").setPlaceholder("48").onChange(async c=>{let m=Number(c),l=isNaN(m);n.inputEl.toggleClass("is-invalid",l),l||(o.settings.advancedToolbar.buttonWidth=m,await o.saveSettings(),oe(o.settings.advancedToolbar))})}),new V.Setting(e).setName("Toolbar Extra Spacing").setDesc("Some Themes need extra spacing in the toolbar. If your Toolbar doesn't wrap properly, try increasing this value.").addSlider(n=>n.setLimits(0,64,1).setValue(o.settings.advancedToolbar.spacing).setDynamicTooltip().onChange(async a=>{o.settings.advancedToolbar.spacing=a,await o.saveSettings(),oe(o.settings.advancedToolbar)}))}function Dt({plugin:t}){let o=K(null);return O(()=>(o.current&&Nt(o.current,t),()=>o.current&&o.current.empty()),[]),r(P,null,r("div",{className:"cmdr-sep-con callout","data-callout":"info"},r("span",{className:"cmdr-callout-warning"},r(k,{icon:"alert-circle"})," ","Info"),r("p",{className:"cmdr-warning-description"},"The Toolbar is only available in Obsidian Mobile. ",V.Platform.isMobile&&r(P,null,"To configure which Commands show up in the Toolbar, open the Mobile Settings.")),V.Platform.isMobile&&r("button",{onClick:()=>{t.app.setting.openTabById("mobile")},className:"mod-cta"},"Open Mobile Settings")),r("div",{ref:o,style:{paddingBottom:"128px"}}))}var le=require("obsidian");var _n=require("obsidian");var Cn=require("obsidian");var rt=({initialColor:t,onChange:o})=>{let e=K(null);return O(()=>(e.current&&new Cn.ColorComponent(e.current).setValue(t).onChange(o),()=>{var n,a;return(a=(n=e.current)==null?void 0:n.empty)==null?void 0:a.call(n)}),[o,t]),r("div",{ref:e,className:"cmdr-flex cmdr-items-center"})};function Bt({plugin:t,modal:o}){var e;return O(()=>{let n=()=>{this.forceUpdate()};return addEventListener("cmdr-icon-changed",n),()=>removeEventListener("cmdr-icon-changed",n)},[]),r("div",{className:"cmdr-mobile-modify-grid"},r("div",{className:"cmdr-mobile-modify-option",onClick:o.handleNewIcon},r("span",null,u("Icon")),r("span",{className:"cmdr-flex cmdr-gap-1"},r(k,{icon:o.pair.icon,size:20,className:"clickable-icon",style:{marginRight:"0px"}}),r(rt,{initialColor:(e=o.pair.color)!=null?e:"#000",onChange:o.handleColorChange}))),r("div",{className:"cmdr-mobile-modify-option"},r("span",null,u("Name")),r("input",{onBlur:({currentTarget:n})=>o.handleRename(n.value),type:"text",placeholder:u("Custom Name"),value:o.pair.name})),r("div",{className:"cmdr-mobile-modify-option"},r("select",{className:"dropdown",value:o.pair.mode,onChange:({currentTarget:n})=>o.handleModeChange(n.value)},r("option",{value:"any"},u("Add command to all devices")),r("option",{value:"mobile"},u("Add command only to mobile devices")),r("option",{value:"desktop"},u("Add command only to desktop devices")),r("option",{value:t.app.appId},u("Add command only to this device")))),r("div",{className:"modal-button-container"},r("button",{className:"mod-cta",onClick:()=>o.close()},u("Done"))))}var me=class extends _n.Modal{constructor(e,n,a,i,c,m){super(e.app);this.plugin=e;this.pair=n;this.handleRename=a;this.handleNewIcon=i;this.handleModeChange=c;this.handleColorChange=m}async onOpen(){this.titleEl.innerText=this.pair.name,this.reactComponent=r(Bt,{plugin:this.plugin,modal:this}),W(this.reactComponent,this.contentEl)}onClose(){W(null,this.contentEl)}};function Ie({value:t,handleChange:o,ariaLabel:e}){let[n,a]=D(!1),i=K(null),[c,m]=D(0);return O(()=>{var l,h;(l=i==null?void 0:i.current)==null||l.select(),(h=i==null?void 0:i.current)==null||h.focus()}),r("div",{class:"cmdr-editable"},n?r("input",{type:"text",value:t,style:{width:c+25+"px"},onKeyDown:l=>{l.key==="Enter"&&l.target.value.length>0&&(a(!1),o(l))},onBlur:()=>a(!1),ref:i}):r("span",{onDblClick:({target:l})=>{m(l==null?void 0:l.offsetWidth),a(!0)},"aria-label":e},t))}function Ht({plugin:t,pair:o,handleRemove:e,handleDown:n,handleUp:a,handleNewIcon:i,handleRename:c,handleModeChange:m,handleColorChange:l,sortable:h=!0}){var A;let s=Q(o.id,t);if(!s)return r(P,null,le.Platform.isDesktop&&r("div",{className:"setting-item mod-toggle"},r(k,{icon:"alert-triangle",size:20,className:"cmdr-icon clickable-icon mod-warning"}),r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},o.name),r("div",{className:"setting-item-description"},u("This Command is not available on this device."))),r("div",{className:"setting-item-control"},r("button",{className:"mod-warning",style:"display: flex",onClick:e,"aria-label":u("Delete")},r(k,{icon:"lucide-trash"})))),le.Platform.isMobile&&r("div",{className:"mobile-option-setting-item",onClick:()=>{new le.Notice(u("This Command is not available on this device."))}},r("span",{className:"mobile-option-setting-item-remove-icon",onClick:e},r(k,{icon:"minus-with-circle",size:22,style:{color:"var(--text-error)"}})),r("span",{className:"mobile-option-setting-item-option-icon mod-warning"},r(k,{icon:"alert-triangle",size:22})),r("span",{className:"mobile-option-setting-item-name"},o.name)));let g=s.id.split(":").first(),b=t.app.plugins.manifests[g],f=!b,C=s.hasOwnProperty("checkCallback")||s.hasOwnProperty("editorCheckCallback"),w=Ra(o.mode),v=o.mode.match(/desktop|mobile|any/)?o.mode[0].toUpperCase()+o.mode.substring(1):u("This device");return r(P,null,le.Platform.isDesktop&&r("div",{className:"setting-item mod-toggle"},r(k,{icon:o.icon,size:20,"aria-label":u("Choose new"),onClick:i,className:"cmdr-icon clickable-icon"}),r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},r(Ie,{ariaLabel:u("Double click to rename"),handleChange:({target:y})=>{c(y==null?void 0:y.value)},value:o.name}),o.name!==s.name&&r("span",{style:"margin-left: .8ex"},"(",s.name,")")),r("div",{className:"setting-item-description"},u("Added by {{plugin_name}}.".replace("{{plugin_name}}",f?"Obsidian":b.name))," ",C?u("Warning: This is a checked Command, meaning it might not run under every circumstance."):"")),r("div",{className:"setting-item-control"},r(rt,{initialColor:(A=o.color)!=null?A:"#000",onChange:l}),h&&r(P,null,r(k,{icon:"arrow-down",className:"setting-editor-extra-setting-button clickable-icon",onClick:n,"aria-label":u("Move down")}),r(k,{icon:"arrow-up",className:"setting-editor-extra-setting-button clickable-icon",onClick:a,"aria-label":u("Move up")})),r(k,{icon:w,className:"setting-editor-extra-setting-button clickable-icon",onClick:()=>m(),"aria-label":u("Change Mode (Currently: {{current_mode}})").replace("{{current_mode}}",v)}),r("button",{className:"mod-warning",style:"display: flex",onClick:e,"aria-label":u("Delete")},r(k,{icon:"lucide-trash"})))),le.Platform.isMobile&&r("div",{className:"mobile-option-setting-item"},r("span",{className:"mobile-option-setting-item-remove-icon",onClick:e},r(k,{icon:"minus-with-circle",size:22,style:{color:"var(--text-error)"}})),r("span",{className:"mobile-option-setting-item-option-icon"},r(k,{icon:o.icon,size:22,onClick:()=>{new me(t,o,c,i,m,l).open()}})),r("span",{className:"mobile-option-setting-item-name",onClick:()=>{new me(t,o,c,i,m,l).open()}},o.name,o.name!==s.name&&r("span",{className:"cmdr-option-setting-name"},"(",s.name,")")),r("span",{className:"mobile-option-setting-item-option-icon"},h&&r(P,null,r(k,{icon:"arrow-down",className:"clickable-icon",onClick:n}),r(k,{icon:"arrow-up",className:"clickable-icon",onClick:a})),r(k,{icon:"three-horizontal-bars",className:"clickable-icon",onClick:()=>{new me(t,o,c,i,m,l).open()}}))))}function Ra(t){return t==="mobile"?"smartphone":t==="desktop"?"monitor":t==="any"?"cmdr-all-devices":"airplay"}function Rt(t,o,e){let n=o<0?t.length+o:o;if(n>=0&&n<t.length){let a=e<0?t.length+e:e,[i]=t.splice(o,1);t.splice(a,0,i)}}var yn=require("obsidian");var za=Ye(null);function ie({manager:t,plugin:o,children:e,sortable:n=!0}){return r(P,null,r(za.Provider,{value:t},r("div",{className:"cmdr-sep-con"},t.pairs.map((a,i)=>{if(a.mode.match(/desktop|mobile|any/)||a.mode===o.app.appId)return r(Ht,{plugin:o,sortable:n,key:a.id,pair:a,handleRemove:async()=>{(!o.settings.confirmDeletion||await new F(o).didChooseRemove())&&(await t.removeCommand(a),this.forceUpdate())},handleUp:()=>{Rt(t.pairs,i,i-1),t.reorder(),this.forceUpdate()},handleDown:()=>{Rt(t.pairs,i,i+1),t.reorder(),this.forceUpdate()},handleRename:async c=>{a.name=c,await o.saveSettings(),t.reorder(),this.forceUpdate()},handleNewIcon:async()=>{let c=await new R(o).awaitSelection();c&&c!==a.icon&&(a.icon=c,await o.saveSettings(),t.reorder(),this.forceUpdate()),dispatchEvent(new Event("cmdr-icon-changed"))},handleModeChange:async c=>{let m=["any","desktop","mobile",o.app.appId],l=m.indexOf(a.mode);l===3&&(l=-1),a.mode=c||m[l+1],await o.saveSettings(),t.reorder(),this.forceUpdate()},handleColorChange:async c=>{a.color=c,await o.saveSettings(),t.reorder()}})})),!t.pairs.some(a=>X(a.mode,o)||a.mode.match(/mobile|desktop/))&&r("div",{class:"cmdr-commands-empty"},r(Ce,null),r("h3",null,u("No commands here!")),r("span",null,u("Would you like to add one now?"))),yn.Platform.isMobile&&r("hr",null),r("div",{className:"cmdr-add-new-wrapper"},r("button",{className:"mod-cta",onClick:async()=>{let a=await q(o);await t.addCommand(a),t.reorder(),this.forceUpdate()}},u("Add command")))),e)}function st({title:t,children:o}){let[e,n]=D(!1);return r("div",{className:"cmdr-accordion cmdr-sep-con","aria-expanded":e},r("div",{className:"cmdr-accordion-header cmdr-mb-1",onClick:()=>{n(!e)}},r(k,{className:"cmdr-accordion-chevron clickable-icon",icon:"chevron-down",size:24}),r("span",null,t)),r("div",{className:"cmdr-accordion-content",style:{maxHeight:[o].flat().length*120+"px"}},o))}function zt({name:t,description:o,children:e,className:n}){return r("div",{className:`setting-item ${n}`},r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},t),r("div",{className:"setting-item-description"},o)),r("div",{className:"setting-item-control"},e))}function Ot(t){let[o,e]=D(t.value);return r(zt,{name:t.name,description:t.description,className:"mod-toggle"},r("div",{className:`checkbox-container ${o?"is-enabled":""}`,onClick:()=>{e(!o),t.changeHandler(o)}}))}function Ft({name:t,description:o,changeHandler:e,value:n,hideLabel:a,showLabel:i}){let[c,m]=D(n);return r(zt,{name:t,description:o,className:"mod-toggle"},r(k,{"aria-label":c?i:a,icon:c?"eye-off":"eye",size:20,className:"clickable-icon",onClick:()=>{m(!c),e(c)}}))}function ct(t){var n,a,i;let[o,e]=D(t.value);return r(zt,{description:t.description,name:t.name,className:"cmdr-slider"},r("div",null,r(Ie,{ariaLabel:u("Double click to enter custom value"),value:o.toString(),handleChange:({target:c})=>{let m=Number(c.value);!isNaN(m)&&o!==m&&(e(m),t.changeHandler(m))}}),r("input",{class:"slider",type:"range",min:(n=t.min)!=null?n:"0",max:(a=t.max)!=null?a:"32",step:(i=t.step)!=null?i:"1",value:o,onPointerMove:({target:c})=>{o!==c.value&&(e(c.value),t.changeHandler(c.value))}})))}function wn({plugin:t}){let[o,e]=D([]),n=t.settings.hide.leftRibbon;return O(()=>{e(app.workspace.leftRibbon.items.map(a=>({name:a.title,icon:a.icon})))},[]),r(P,null,r("hr",null),r(st,{title:u("Hide other Commands")},o.map(a=>r(Ft,{name:a.name,description:"",hideLabel:u("Hide"),showLabel:u("Show"),changeHandler:async i=>{i?n.contains(a.name)&&n.remove(a.name):n.push(a.name),Me(t.settings),await t.saveSettings()},value:n.contains(a.name)}))))}function kn({plugin:t}){let o=t.settings.hide.statusbar,[e,n]=D([]);return O(()=>{let i=[...t.app.statusBar.containerEl.getElementsByClassName("status-bar-item")].map(c=>[...c.classList].find(m=>m.startsWith("plugin-"))).filter(c=>c).map(c=>c.substring(7));n(i.map(c=>t.app.plugins.manifests[c]||{id:c,name:c.replace(/-/g," ").replace(/(^\w{1})|(\s+\w{1})/g,m=>m.toUpperCase()),description:"Core Plugin"}))},[]),r(P,null,r("hr",null),r(st,{title:u("Hide other Commands")},e.map(a=>r(Ft,{name:a.name,description:a.description,value:o.contains(a.id),hideLabel:u("Hide"),showLabel:u("Show"),changeHandler:async i=>{i?o.contains(a.id)&&o.remove(a.id):o.push(a.id),Me(t.settings),await t.saveSettings()}}))))}var xn=require("obsidian");var En=require("obsidian");function Mn({plugin:t,macro:o,onSave:e,onCancel:n}){let[a,i]=D(o.name||"Macro Name"),[c,m]=D(o.icon||"star"),[l,h]=D(o.startup||!1),[s,g]=D(JSON.parse(JSON.stringify(o.macro))||[]),b=this.forceUpdate.bind(this),f=async()=>{let w=await new ne(t).awaitSelection();w&&g([...s,{action:0,commandId:w.id}])},C=async()=>{g([...s,{action:1,delay:250}])};return r("div",null,r("div",{class:"setting-item cmdr-mm-item"},r("div",null,r("span",null,"Name"),r("input",{type:"text",placeholder:"Macro Name",value:a,onChange:w=>i(w.currentTarget.value),width:"100%"})),r("div",null,r("span",null,"Icon"),r("button",{onClick:async()=>m(await new R(t).awaitSelection())},r(k,{icon:c})))),s.map((w,v)=>{switch(w.action){case 0:let A=Q(w.commandId,t);return r("div",{class:"setting-item cmdr-mm-item"},r("div",null,r("button",{onClick:async()=>{let y=await new ne(t).awaitSelection();g(s.map((H,ee)=>ee===v?Gt(Fe({},H),{commandId:y.id}):H))}},(A==null?void 0:A.name)||"Cannot find Command")),r("div",null,r("div",{class:"cmdr-mm-action-options"},r(k,{class:"clickable-icon",icon:"arrow-down",onClick:()=>{if(v===s.length-1)return;let y=[...s],H=y[v];y[v]=y[v+1],y[v+1]=H,g(y)}}),r(k,{class:"clickable-icon",icon:"arrow-up",onClick:()=>{if(v===0)return;let y=[...s],H=y[v];y[v]=y[v-1],y[v-1]=H,g(y)}}),r(k,{class:"clickable-icon",icon:"cross",onClick:()=>{g(s.filter((y,H)=>H!==v))}}))));case 1:return r("div",{class:"setting-item cmdr-mm-item"},r("div",null,r(ct,{name:"Delay",min:0,max:1e4,step:50,description:"Delay in milliseconds",value:w.delay,changeHandler:y=>w.delay=y})),r("div",null,r("div",{class:"cmdr-mm-action-options"},r(k,{class:"clickable-icon",icon:"arrow-down",onClick:()=>{if(v===s.length-1)return;let y=[...s],H=y[v];y[v]=y[v+1],y[v+1]=H,g(y)}}),r(k,{class:"clickable-icon",icon:"arrow-up",onClick:()=>{if(v===0)return;let y=[...s],H=y[v];y[v]=y[v-1],y[v-1]=H,g(y)}}),r(k,{class:"clickable-icon",icon:"cross",onClick:()=>{g(s.filter((y,H)=>H!==v))}}))));case 2:return r("div",null,"Editor: ",w.action);case 3:return r("div",null,"Loop: ",w.times)}}),r("div",{className:"setting-item cmdr-mm-actions cmdr-justify-between"},r("div",{className:"cmdr-flex cmdr-items-center cmdr-justify-self-start"},r("input",{type:"checkbox",id:"checkbox",checked:l,onChange:({target:w})=>{var v;h((v=w==null?void 0:w.checked)!=null?v:!1)}}),r("label",{htmlFor:"checkbox"},"Auto-Run on Startup")),r("div",null,r("button",{onClick:f},"Add Command"),r("button",{onClick:C},"Add Delay"))),r("div",{className:"cmdr-mm-control"},r("button",{class:s.length===0?"disabled":"mod-cta",disabled:s.length===0,onClick:()=>s.length&&e({macro:s,name:a,icon:c,startup:l})},"Save"),r("button",{onClick:n},"Cancel")))}var Ae=class extends En.Modal{constructor(e,n,a){super(e.app);this.macro=n,this.plugin=e,this.onSave=a}onOpen(){this.titleEl.setText("Macro Builder"),W(r(Mn,{plugin:this.plugin,macro:this.macro,onSave:this.onSave,onCancel:this.close.bind(this)}),this.contentEl)}onClose(){W(null,this.contentEl)}};function Vt({plugin:t,macros:o}){let e=(a,i)=>{let c=l=>{o.splice(i!==void 0?i:o.length,i!==void 0?1:0,l),t.saveSettings(),this.forceUpdate(),Ee(t),m.close()},m=new Ae(t,a,c);m.open()},n=a=>{o.splice(a,1),t.saveSettings(),this.forceUpdate(),Ee(t)};return r(P,null,r("div",{className:"cmdr-sep-con"},o.map((a,i)=>r("div",{class:"setting-item mod-toggle"},r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},a.name),r("div",{className:"setting-item-description"},a.macro.length," Actions")),r("div",{className:"setting-item-control"},r("button",{"aria-label":"Edit Macro",onClick:()=>e(a,i)},r(k,{icon:"lucide-pencil"})),r("button",{"aria-label":"Delete",class:"mod-warning",onClick:async()=>{(!t.settings.confirmDeletion||await new F(t).didChooseRemove())&&n(i)}},r(k,{icon:"trash"})))))),!o.length&&r("div",{class:"cmdr-commands-empty"},r(Ce,null),r("h3",null,"No Macros yet!"),r("span",null,u("Would you like to add one now?"))),xn.Platform.isMobile&&r("hr",null),r("div",{className:"cmdr-add-new-wrapper"},r("button",{class:"mod-cta",onClick:()=>e({name:"",macro:[],icon:"star"})},"Add Macro")))}function Te({plugin:t,mobileMode:o}){let[e,n]=D(0),[a,i]=D(!0),c=({key:l,shiftKey:h})=>{h&&l==="Tab"?e>0?n((e-1)%m.length):n(m.length-1):l==="Tab"&&n((e+1)%m.length)};O(()=>(addEventListener("keydown",c),()=>removeEventListener("keydown",c)),[e]),J.Platform.isMobile&&O(()=>{let l=document.querySelector(".modal-setting-back-button"),h=l.cloneNode(!0);l.parentNode.replaceChild(h,l),i(!0)},[]),O(()=>{let l=document.querySelector(".modal-setting-back-button");!l||(a?(l.parentElement.lastChild.textContent="Commander",l.onclick=()=>t.app.setting.closeActiveTab()):(l.parentElement.lastChild.textContent=m[e].name,l.onclick=()=>i(!0)))},[a]);let m=et(()=>[{name:u("General"),tab:r(P,null,r(Ot,{name:u("Always ask before removing?"),description:u("Always show a Popup to confirm deletion of a Command."),value:t.settings.confirmDeletion,changeHandler:async l=>{t.settings.confirmDeletion=!l,await t.saveSettings()}}),r(Ot,{value:t.settings.showAddCommand,name:u('Show "Add Command" Button'),description:'Show the "Add Command" Button in every Menu.',changeHandler:async l=>{t.settings.showAddCommand=!l,t.manager.pageHeader.reorder(),await t.saveSettings()}}),r(ct,{value:t.settings.spacing,name:u("Choose custom spacing for Command Buttons"),description:u("Change the spacing between commands. You can set different values on mobile and desktop."),changeHandler:async l=>{ot(l),t.settings.spacing=l,await t.saveSettings()}}))},{name:u("Left Ribbon"),tab:r(ie,{manager:t.manager.leftRibbon,plugin:t,sortable:!1},r(wn,{plugin:t}),r("div",{className:"cmdr-sep-con callout","data-callout":"warning"},r("span",{className:"cmdr-callout-warning"},r(k,{icon:"alert-triangle"})," ","Reordering and Sorting"),r("p",{className:"cmdr-warning-description"},"As of Obsidian 1.1.0 you can reorder the Buttons in the left ribbon by dragging. This will replace the old sorting feature.")))},{name:u("Page Header"),tab:r(ie,{manager:t.manager.pageHeader,plugin:t},r("hr",null),r("div",{className:"cmdr-sep-con callout","data-callout":"warning"},r("span",{className:"cmdr-callout-warning"},r(k,{icon:"alert-triangle"})," ",u("Warning")),r("p",{className:"cmdr-warning-description"},u("As of Obsidian 0.16.0 you need to explicitly enable the View Header.")),r("button",{onClick:()=>{t.app.setting.openTabById("appearance"),setTimeout(()=>{var l,h,s,g;t.app.setting.activeTab.containerEl.scroll({behavior:"smooth",top:250}),(g=(s=(h=(l=t.app.setting.activeTab.containerEl.querySelectorAll(".setting-item-heading")[1].nextSibling)==null?void 0:l.nextSibling)==null?void 0:h.nextSibling)==null?void 0:s.addClass)==null||g.call(s,"cmdr-cta")},50)},className:"mod-cta"},u("Open Appearance Settings"))))},{name:u("Statusbar"),tab:r(ie,{manager:t.manager.statusBar,plugin:t},r(kn,{plugin:t}))},{name:u("Editor Menu"),tab:r(ie,{manager:t.manager.editorMenu,plugin:t})},{name:u("File Menu"),tab:r(ie,{manager:t.manager.fileMenu,plugin:t})},{name:u("Explorer"),tab:r(ie,{manager:t.manager.explorerManager,plugin:t},r("hr",null),r("div",{className:"cmdr-sep-con callout","data-callout":"warning"},r("span",{className:"cmdr-callout-warning"},r(k,{icon:"alert-triangle"})," ",u("Warning")),r("p",{className:"cmdr-warning-description"},"When clicking on a Command in the Explorer, the Explorer view will become focused. This might interfere with Commands that are supposed to be executed on an active File/Explorer.")))},{name:J.Platform.isMobile?"Mobile Toolbar":"Toolbar",tab:r(Dt,{plugin:t})},{name:"Macros",tab:r(Vt,{plugin:t,macros:t.settings.macros})}],[]);return r(P,null,J.Platform.isDesktop&&r("div",{className:"cmdr-setting-title"},r("h1",null,t.manifest.name)),(J.Platform.isDesktop||a)&&r(Fa,{tabs:m,activeTab:e,setActiveTab:n,setOpen:i}),r("div",{class:`cmdr-setting-content ${o?"cmdr-mobile":""}`},(J.Platform.isDesktop||!a)&&m[e].tab,(J.Platform.isMobile&&a||J.Platform.isDesktop&&e===0)&&r(Lt,{manifest:t.manifest})))}function Fa({tabs:t,activeTab:o,setActiveTab:e,setOpen:n}){let a=K(null),i=c=>{var m;c.preventDefault(),(m=a.current)==null||m.scrollBy({left:c.deltaY>0?16:-16})};return O(()=>{let c=a.current;if(!(!c||J.Platform.isMobile))return c.addEventListener("wheel",i),()=>c.removeEventListener("wheel",i)},[]),O(()=>{var c;return(c=document.querySelector(".cmdr-tab-active"))==null?void 0:c.scrollIntoView({behavior:"smooth",block:"nearest"})},[o]),r("nav",{class:`cmdr-setting-header ${J.Platform.isMobile?"cmdr-mobile":""}`,ref:a},r("div",{class:`cmdr-setting-tab-group ${J.Platform.isMobile?"vertical-tab-header-group-items":""}`},t.map((c,m)=>r("div",{className:`cmdr-tab ${o===m?"cmdr-tab-active":""} ${J.Platform.isMobile?"vertical-tab-nav-item":""}`,onClick:()=>{e(m),n(!1)}},c.name,J.Platform.isMobile&&r(k,{className:"vertical-tab-nav-item-chevron cmdr-block",icon:"chevron-right",size:24})))),J.Platform.isDesktop&&r("div",{className:"cmdr-fill"}))}var Le=class extends dt.PluginSettingTab{constructor(e){super(e.app,e);this.plugin=e}display(){W(r(Te,{plugin:this.plugin,mobileMode:dt.Platform.isMobile}),this.containerEl)}hide(){W(null,this.containerEl)}};var mt=require("obsidian");var Ne=class extends mt.Modal{constructor(e){super(e.app);this.plugin=e,this.containerEl.addClass("cmdr-setting-modal")}onOpen(){let e=mt.Platform.isMobile;W(r(Te,{plugin:this.plugin,mobileMode:e}),this.contentEl)}onClose(){W(null,this.contentEl)}};var Sn=require("obsidian");function Wt(){(0,Sn.addIcon)("cmdr-all-devices",'<g style="fill: currentColor;"><path d="M 12.5 16.667969 L 83.332031 16.667969 C 87.9375 16.667969 91.667969 20.398438 91.667969 25 L 91.667969 33.332031 L 75 33.332031 L 75 25 L 20.832031 25 L 20.832031 75 L 58.332031 75 L 58.332031 83.332031 L 12.5 83.332031 C 7.898438 83.332031 4.167969 79.601562 4.167969 75 L 4.167969 25 C 4.167969 20.398438 7.898438 16.667969 12.5 16.667969 M 70.832031 41.667969 L 95.832031 41.667969 C 98.132812 41.667969 100 43.53125 100 45.832031 L 100 87.5 C 100 89.800781 98.132812 91.667969 95.832031 91.667969 L 70.832031 91.667969 C 68.53125 91.667969 66.667969 89.800781 66.667969 87.5 L 66.667969 45.832031 C 66.667969 43.53125 68.53125 41.667969 70.832031 41.667969 M 75 50 L 75 79.167969 L 91.667969 79.167969 L 91.667969 50 Z M 75 50 "/></g>')}var De=class extends Z{constructor(e){super(e,e.settings.leftRibbon);this.plugin=e,this.plugin.settings.leftRibbon.forEach(n=>this.addCommand(n,!1)),this.plugin.app.workspace.onLayoutReady(()=>{})}async addCommand(e,n=!0){if(n&&(this.plugin.settings.leftRibbon.push(e),await this.plugin.saveSettings()),X(e.mode,this.plugin)){this.plugin.addRibbonIcon(e.icon,e.name,()=>this.plugin.app.commands.executeCommandById(e.id));let a=this.plugin.app.workspace.leftRibbon.items.find(i=>i.icon===e.icon&&i.name===i.name);a&&(a.buttonEl.style.color=e.color==="#000000"||e.color===void 0?"inherit":e.color),this.plugin.register(()=>this.removeCommand(e,!1))}}async removeCommand(e,n=!0){n&&(this.plugin.settings.leftRibbon.remove(e),await this.plugin.saveSettings());let a=this.plugin.app.workspace.leftRibbon.items.find(i=>i.icon===e.icon&&i.name===i.name);a&&a.buttonEl.remove(),app.workspace.leftRibbon.items.remove(a)}reorder(){this.plugin.settings.leftRibbon.forEach(e=>{this.removeCommand(e,!1),this.addCommand(e,!1)})}};var lt=class extends Pn.Plugin{async executeStartupMacros(){this.settings.macros.forEach((e,n)=>{e.startup&&this.executeMacro(n)})}async executeMacro(e){let n=this.settings.macros[e];if(!n)throw new Error("Macro not found");for(let a of n.macro)switch(a.action){case 0:{await this.app.commands.executeCommandById(a.commandId);continue}case 1:{await new Promise(i=>setTimeout(i,a.delay));continue}case 2:continue;case 3:{for(let i=0;i<a.times;i++)await this.app.commands.executeCommandById(a.commandId);continue}}}async onload(){var e,n;await this.loadSettings(),(n=(e=this.settings.hide).leftRibbon)!=null||(e.leftRibbon=[]),Wt(),this.manager={editorMenu:new Se(this,this.settings.editorMenu),fileMenu:new Pe(this,this.settings.fileMenu),leftRibbon:new De(this),statusBar:new be(this,this.settings.statusBar),pageHeader:new ge(this,this.settings.pageHeader),explorerManager:new ve(this,this.settings.explorer)},this.addSettingTab(new Le(this)),this.addCommand({name:u("Open Commander Settings"),id:"open-commander-settings",callback:()=>new Ne(this).open()}),this.registerEvent(this.app.workspace.on("editor-menu",this.manager.editorMenu.applyEditorMenuCommands(this))),this.registerEvent(this.app.workspace.on("file-menu",this.manager.fileMenu.applyFileMenuCommands(this))),this.app.workspace.onLayoutReady(()=>{Me(this.settings),Ee(this),ot(this.settings.spacing),oe(this.settings.advancedToolbar),nt(this.settings.advancedToolbar,this),this.executeStartupMacros()})}onunload(){var e;(e=document.head.querySelector("style#cmdr"))==null||e.remove(),Go()}async loadSettings(){let e=Object.assign({},wo,await this.loadData());this.settings=e}async saveSettings(){await this.saveData(this.settings)}listActiveToolbarCommands(){return this.app.vault.getConfig("mobileToolbarCommands")}getCommands(){let e=[];return this.listActiveToolbarCommands().forEach(n=>{let a=this.app.commands.commands[n];a&&e.push(a)}),e}getCommandsWithoutIcons(e=!0){let n=[];return this.getCommands().forEach(a=>{a&&!a.icon&&n.push(a)}),e&&this.getCommands().forEach(a=>{this.settings.advancedToolbar.mappedIcons.find(i=>i.commandID===a.id)&&n.push(a)}),n}};

/* by phibr0 */

/* nosourcemap */