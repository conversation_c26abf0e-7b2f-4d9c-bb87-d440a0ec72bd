#!/usr/bin/env python3
"""
Simple search interface for the embedded Obsidian vault in Qdrant
"""

import requests
from qdrant_client import QdrantClient
import argparse
import json
from typing import List, Dict

class VaultSearcher:
    def __init__(self, 
                 qdrant_host: str = "localhost",
                 qdrant_port: int = 6333,
                 ollama_host: str = "localhost", 
                 ollama_port: int = 11434,
                 collection_name: str = "obsidian_vault",
                 embedding_model: str = "qwen2.5:7b"):
        
        self.qdrant_client = QdrantClient(host=qdrant_host, port=qdrant_port)
        self.ollama_base_url = f"http://{ollama_host}:{ollama_port}"
        self.collection_name = collection_name
        self.embedding_model = embedding_model
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama"""
        response = requests.post(
            f"{self.ollama_base_url}/api/embeddings",
            json={
                "model": self.embedding_model,
                "prompt": text
            }
        )
        response.raise_for_status()
        return response.json()["embedding"]
    
    def search(self, query: str, limit: int = 10) -> List[Dict]:
        """Search the embedded vault"""
        query_embedding = self.get_embedding(query)
        
        results = self.qdrant_client.search(
            collection_name=self.collection_name,
            query_vector=query_embedding,
            limit=limit,
            with_payload=True
        )
        
        return [
            {
                'score': result.score,
                'file_path': result.payload.get('file_path'),
                'title': result.payload.get('title'),
                'content': result.payload.get('chunk_content'),
                'type': result.payload.get('type'),
                'tags': result.payload.get('tags', []),
                'area': result.payload.get('area'),
                'metadata': result.payload
            }
            for result in results
        ]
    
    def interactive_search(self):
        """Interactive search interface"""
        print("🔍 Obsidian Vault Semantic Search")
        print("Type 'quit' to exit")
        print("-" * 40)
        
        while True:
            query = input("\nEnter search query: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            try:
                results = self.search(query)
                
                if not results:
                    print("No results found.")
                    continue
                
                print(f"\n📋 Found {len(results)} results:")
                print("=" * 60)
                
                for i, result in enumerate(results, 1):
                    print(f"\n{i}. 📄 {result['title']} (Score: {result['score']:.3f})")
                    print(f"   📁 File: {result['file_path']}")
                    
                    if result['type']:
                        print(f"   🏷️  Type: {result['type']}")
                    
                    if result['area']:
                        print(f"   📂 Area: {result['area']}")
                    
                    if result['tags']:
                        tags_str = ', '.join(result['tags']) if isinstance(result['tags'], list) else str(result['tags'])
                        print(f"   🏷️  Tags: {tags_str}")
                    
                    content = result['content']
                    if len(content) > 300:
                        content = content[:300] + "..."
                    print(f"   📝 Content: {content}")
                    
                    if i < len(results):
                        print("-" * 60)
                
            except Exception as e:
                print(f"❌ Search error: {e}")

def main():
    parser = argparse.ArgumentParser(description='Search embedded Obsidian vault')
    parser.add_argument('--qdrant-host', default='localhost', help='Qdrant host')
    parser.add_argument('--qdrant-port', type=int, default=6333, help='Qdrant port')
    parser.add_argument('--ollama-host', default='localhost', help='Ollama host')
    parser.add_argument('--ollama-port', type=int, default=11434, help='Ollama port')
    parser.add_argument('--collection', default='obsidian_vault', help='Qdrant collection name')
    parser.add_argument('--model', default='qwen2.5:7b', help='Ollama embedding model')
    parser.add_argument('--query', help='Single search query')
    parser.add_argument('--limit', type=int, default=10, help='Number of results to return')
    
    args = parser.parse_args()
    
    searcher = VaultSearcher(
        qdrant_host=args.qdrant_host,
        qdrant_port=args.qdrant_port,
        ollama_host=args.ollama_host,
        ollama_port=args.ollama_port,
        collection_name=args.collection,
        embedding_model=args.model
    )
    
    if args.query:
        # Single query mode
        results = searcher.search(args.query, args.limit)
        
        print(f"🔍 Search Results for '{args.query}':")
        print("=" * 60)
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['title']} (Score: {result['score']:.3f})")
            print(f"   File: {result['file_path']}")
            print(f"   Content: {result['content'][:200]}...")
    else:
        # Interactive mode
        searcher.interactive_search()

if __name__ == "__main__":
    main()
